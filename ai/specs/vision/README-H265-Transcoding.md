# H.265 Software Transcoding System - MSW Fetch Interception

> **Status: ✅ Implemented (MVP)** - Service Worker (MSW) fetch interception transcoding H.265 → H.264

## Overview

This system enables browsers without native H.265/HEVC support to play H.265 video streams by implementing **service worker–level interception** (via MSW) with real-time software transcoding using FFmpeg.wasm. The approach is transparent to HLS.js and the video element.

## Architecture

```
┌─────────────────────────────────────────────────────────┐
│                 MSW Service Worker                      │
│  (Intercepts Fetch and XHR transparently)               │
└─────────────────────┬───────────────────────────────────┘
                      │
┌─────────────────────▼───────────────────────────────────┐
│              Unified HLS Handler (MSW)                  │
│  • Detects HLS resources (.m3u8, .mp4, .m4s)            │
│  • Rewrites manifest codec metadata to H.264            │
│  • Converts init segments hvcC→avcC for HLS.js          │
│  • Transcodes media segments HEVC→AVC (FFmpeg.wasm)     │
└─────────────────────┬───────────────────────────────────┘
                      │
┌─────────────────────▼───────────────────────────────────┐
│           Complete Transcoding Pipeline                 │
│  • Manifest rewriting (H.265 → H.264 codec strings)   │
│  • Init segment reconstruction (hvcC → avcC boxes)     │
│  • Media segment transcoding (FFmpeg.wasm)             │
└─────────────────────┬───────────────────────────────────┘
                      │
┌─────────────────────▼───────────────────────────────────┐
│              HLS.js Player                              │
│  • Receives consistent H.264 content                   │
│  • No modifications required                           │
│  • All features work normally (ABR, seeking, etc.)     │
└─────────────────────────────────────────────────────────┘
```

## Core Advantages

### ✅ Complete Transparency

- **Zero HLS.js modifications** - works with any HLS.js version
- **Universal compatibility** - works with any video player
- **Future-proof** - no dependency on player internals

### ✅ Maximum Reliability

- **All-or-nothing approach** - entire streams are transcoded consistently
- **No codec mixing** - prevents browser decoder failures
- **Complete metadata consistency** - manifests, init segments, and media segments all match

### ✅ Architectural Superiority

- **Browser-level interception** - cleaner separation of concerns
- **Standard web APIs** - uses fetch override pattern
- **Maintainable code** - simpler than custom loader integration

## Implementation Components

### 1. MSW HLS Handler (`apps/fleet-web/src/mswWorker/hlsMSWHandler.ts`)

- Single `http.get(/\.(m3u8|mp4|m4s)(\?.*)?$/i)` handler intercepts manifests, init segments, and media segments.
- Per-URL singleflight to coalesce duplicate requests; 5s TTL cache to avoid re-transcoding.
- Processes manifests via `manifestProcessor`, converts init segments via `initSegmentProcessor`, and transcodes media via `MediaSegmentProcessorSingleton`.
- For fMP4/mp4 segments, caches RAW HEVC init and prepends it before transcoding so FFmpeg can demux.

> Important: `.ts` MPEG-TS segments are not parsed or handled by `hlsMSWHandler`. By backend contract, any `.ts` segments are already encoded as H.264 (AVC), so they require no rewriting or transcoding and are passed through as-is. Use playlist semantics (for example `#EXT-X-VERSION:7` with fMP4 and `#EXT-X-MAP`) to trigger HEVC → AVC transcoding; TS segments imply AVC and bypass the software transcoding pipeline.

### 2. Manifest Processor

- **Codec string replacement**: `hvc1.1.6.L93.B0` → `avc1.640028`
- **Bandwidth adjustments**: Account for H.264 size differences
- **URL consistency**: Ensure all references remain valid

### 3. Init Segment Reconstructor (`apps/fleet-web/src/modules/vision/VisionLiveStream/transcoding/initSegmentProcessor.ts`)

- **MP4 box manipulation**: Convert `hvcC` → `avcC` boxes
- **Sample entries**: Update `hvc1`/`hev1` → `avc1` boxes
- **Decoder configuration**: Generate valid H.264 decoder config
- **Runtime behavior**: For HLS.js we return the processed init (avcC). For FFmpeg input we prepend the RAW HEVC init to each media fragment to enable demuxing.

### 4. Media Segment Transcoder (`apps/fleet-web/src/modules/vision/VisionLiveStream/transcoding/MediaSegmentProcessorSingleton.ts`)

- **FFmpeg.wasm integration**: H.265 → H.264 transcoding
- **Timing preservation**: Maintain exact PTS/DTS values
- **NAL unit validation**: Ensure valid H.264 output
- **Loading**: Load ffmpeg core via `toBlobURL` from `@ffmpeg/util` (CDN: `@ffmpeg/core@0.12.6/dist/umd`).
- **Audio**: `-c:a copy` to preserve AAC.
- **Flags**: `-preset ultrafast -tune zerolatency -crf 28 -vf scale=-2:720 -copyts -start_at_zero -movflags +frag_keyframe+empty_moov+default_base_moof+faststart`.
- **FS hygiene**: Unique temp filenames per transcode to prevent virtual FS collisions.

## Key Benefits Over HLS.js Loader Approach

| Aspect              | Fetch Interception               | HLS.js Custom Loader           |
| ------------------- | -------------------------------- | ------------------------------ |
| **Integration**     | ✅ Zero player modifications     | ❌ Requires HLS.js integration |
| **Reliability**     | ✅ Complete metadata consistency | ❌ Potential codec mixing      |
| **Maintainability** | ✅ Simple, standard web APIs     | ❌ Complex loader architecture |
| **Compatibility**   | ✅ Works with any player         | ❌ HLS.js specific             |
| **Debugging**       | ✅ Clear separation of concerns  | ❌ Mixed with player logic     |

## Memory Requirements [[memory:5483181]]

Never skip the transcoding of h265 to h264 mechanism when handling video streams in this project.

## Import Preferences [[memory:5483519]]

Always import from 'hls.js/dist/hls.mjs' instead of 'hls.js', except when importing types.

## Integration Points

### Transcoding activation (Saga)

When HLS is selected and the browser lacks native H.265 support, the MSW worker is started and the `hlsMSWHandler` is registered. FFmpeg is pre-warmed:

```1523:1547:apps/fleet-web/src/sagas/user.ts
async function maybeInitializeMediaSegmentProcessorSingletonAfterUserSettingsAreAvailable() {
  // Vision setting should be available, since there is a user authenticated
  const visionSetting = selectState(getVisionSetting)
  const liveStreamingStrategy = selectState(getVisionLiveStreamingStrategy)
  const noNativeH265Support =
    hardwareAccelerationSupportInfo.status !== 'likely_supported' ||
    canPlayHEVCUsingMediaSourceResult !== 'supported'

  if (visionSetting && liveStreamingStrategy === 'hls' && noNativeH265Support) {
    if (mswWorker.listHandlers().includes(hlsMSWHandler) === false) {
      mswWorker.use(hlsMSWHandler)
    }
    // Only start MSW worker when needed to intercept HLS requests and transcode them to H264
    await mswWorker.start({
      serviceWorker: { url: '/mockServiceWorker.js' },
      quiet: true,
      onUnhandledRequest: 'bypass',
    })

    // Non-blocking warm-up;
    // ffmpeg wasm will be loaded and that is a large wasm
    FFmpegManagerSingleton.initialize().catch((error) => {
      console.error('[Cartrack] - FFmpeg pre-warm failed (non-fatal)', { error })
    })
  }
}
```

Cleanup on logout:

```1550:1553:apps/fleet-web/src/sagas/user.ts
function cleanupMswWorkerAndFFmpeg() {
  mswWorker.stop()
  FFmpegManagerSingleton.cleanup()
}
```

### VisionPlayer Component

The player determines codec preference based on capability detection and streaming strategy. The MSW interceptor ensures that, when active, the browser only receives H.264, with no codec mixing.

## Error Handling

### Transcoding Failure (Hard fail)

- If manifest or init-segment processing fails: respond with HTTP 500 and abort playback.
- If any media segment transcoding fails: respond with HTTP 500 and abort playback.
- No fallback to serving original H.265 content is allowed per strict all-or-nothing policy.

Example behavior in the MSW handler (conceptual):

```ts
import { HttpResponse } from 'msw'

try {
  const transcoded = await mediaSegmentProcessor.transcodeSegment(buf, url, opts)
  return new HttpResponse(transcoded, { status: 200 })
} catch (error) {
  return new HttpResponse(error?.message || 'Segment transcoding failed', {
    status: 500,
  })
}
```

### Browser Compatibility

- **Chrome 90+**: Full support
- **Firefox 88+**: Full support
- **Edge 90+**: Full support
- **Safari 15+**: Limited (WebAssembly restrictions)

## Performance Characteristics

### Single Stream (MVP Target)

- **CPU Usage**: < 50% (acceptable for proof-of-concept)
- **Memory**: < 1GB per stream
- **Added Latency**: < 5 seconds (buffering)
- **Quality**: 720p maximum for software transcoding

### Future Multi-Stream Support

- **Worker pool**: Parallel transcoding
- **Segment caching**: Avoid re-transcoding
- **Quality adaptation**: CPU-based optimization

## Testing Strategy

### Unit Tests

- Manifest rewriting accuracy
- Init segment box reconstruction
- Media segment transcoding validation

### Integration Tests

- End-to-end playback validation
- Error handling scenarios
- Performance benchmarking

### Browser Testing

- Cross-browser compatibility
- Memory leak detection
- Performance profiling

## Implementation Status

- ✅ MSW interception (manifests, init, segments)
- ✅ Manifest processor
- ✅ Init segment reconstructor (avcC for HLS.js)
- ✅ Raw init caching and prepending for FFmpeg input
- ✅ Media segment transcoding (FFmpeg.wasm)
- ✅ Per-URL singleflight + 5s TTL cache for segments
- ✅ Unique temp filenames; audio copy
- 🔧 Known: small buffer holes may nudge HLS.js; duplicates mitigated via singleflight/cache

## Next Steps

1. Stabilize fragment timing for smooth playback
   - Ensure fMP4 segments are moof+mdat with valid tfdt
   - Avoid per-fragment moov/mfra; use default_base_is_moof
   - If regenerating PTS, apply consistent offset so each fragment starts where playlist timing expects
2. Reduce stall frequency while fixing timing
   - Increase liveSyncDuration to 4–6s (and/or liveMaxLatencyDuration\*) to reduce seek-to-live churn
   - Keep backBufferLength small (0–5)
3. Mitigate FFmpeg.wasm OOB at specific fragments
   - Serialize ffmpeg.exec (no parallel execs on a single instance); set encoderThreads=1
   - Refresh initCache on new EXT-X-MAP or discontinuity; if fragment already has moov/ftyp, don’t prepend init
   - Retry once without prepended init when demux errors occur; reinitialize ffmpeg on hard failures
4. Playlist-derived PTS offsets (optional)
   - Derive per-fragment start from PROGRAM-DATE-TIME or accumulated EXTINF; pass as setpts offset when needed
5. Longer-term
   - Worker pooling for higher concurrency
   - Smarter segment caching and prefetching
   - Dynamic quality selection based on CPU headroom
   - QoS metrics, tracing, and alerting

---

_This approach provides a reliable, maintainable solution for H.265 transcoding that works universally across browsers and video players._
