# H.265 Software Transcoding Requirements - MSW Interception Implementation

_Status: UPDATED FOR MSW INTERCEPTION_

## 🎯 MVP Goal

**Get a single H.265 stream working with fetch-level transcoding to H.264 in unsupported browsers.**

Focus on **maximum reliability** with **strict all-or-nothing transcoding**:

### 🚨 CRITICAL REQUIREMENTS

- **IDENTIFY CODEC RELIABLY**: Server may send H.265 or H.264 content
- **NO FALLBACKS**: If any transcoding fails, the entire stream fails
- **NO CODEC MIXING**: Never allow mixed H.265/H.264 content to reach the browser
- **TRANSPARENT TO HLS.js**: Player must see only consistent H.264 content

## Business Requirements

### Primary Goal

- **Single Stream Success**: Enable one H.265 video stream to play in browsers without native H.265 support
- **Bandwidth Savings**: Demonstrate 40-50% bandwidth reduction potential [[memory:5483181]]
- **Universal Compatibility**: Works with any video player, not just HLS.js
- **Proof of Concept**: Validate fetch-level interception approach

### Success Criteria

- [x] H.265 stream loads in unsupported browser
- [x] Video plays without codec errors
- [x] Transcoding happens transparently at fetch level
- [x] No browser crashes or memory leaks
- [x] Works with unmodified HLS.js

## Technical Requirements

### New Architecture - MSW Interception

```
┌─────────────────────────────────────────────────────────┐
│                    user.ts (Saga)                       │
│  (Detects H.265 support & starts MSW/FFmpeg)            │
└─────────────────────┬───────────────────────────────────┘
                      │
┌─────────────────────▼───────────────────────────────────┐
│        apps/fleet-web/src/mswWorker/hlsMSWHandler.ts    │
│  • Intercepts HLS requests (.m3u8, .m4s, .mp4)           │
│  • Routes by URL type (manifest/init/media)             │
│  • Complete transcoding pipeline                        │
└─────────────────────┬───────────────────────────────────┘
                      │
┌─────────────────────▼───────────────────────────────────┐
│ VisionLiveStream/transcoding/*.ts                       │
│  • Manifest rewriting                                   │
│  • Init segment conversion (hvcC→avcC)                  │
│  • Media segment transcoding (FFmpeg.wasm)              │
└─────────────────────────────────────────────────────────┘
```

### Core Components

#### 1. hlsMSWHandler.ts (NEW)

- **MSW Interception**: Intercepts HLS resources via `msw` route matcher
- **URL Type Detection**: Identifies HLS resources (.m3u8, .m4s, .mp4)
- **Manifest Rewriting**: Updates codec strings (H.265→H.264)
- **Init Segment Reconstruction**: MP4 box manipulation (hvcC→avcC)
- **Media Segment Transcoding**: Invokes FFmpeg.wasm pipeline
- **Caching/Singleflight**: Avoid duplicate work and re-transcoding

> Important: `.ts` MPEG-TS segments are not parsed by `hlsMSWHandler`. By backend contract, `.ts` segments are always H.264 (AVC), so they need no modification; TS ladders are treated as AVC pass-through. HEVC detection and transcoding are triggered by playlist semantics (e.g., `#EXT-X-VERSION:7` with fMP4 and `#EXT-X-MAP`).

#### 2. FFmpegManagerSingleton.ts / MediaSegmentProcessorSingleton.ts

- **FFmpeg.wasm Integration**: H.265 → H.264 transcoding
- **Timing Preservation**: Maintain approximate PTS/DTS continuity
- **Quality Settings**: 480p/720p caps, ultrafast preset for speed
- **Memory Management**: Temp file hygiene and cleanup
- **Error Reporting**: Clear error messages for debugging

### Performance Targets (MVP)

- **Streams**: 1 concurrent stream (single focus)
- **Quality**: 720p maximum for software transcoding
- **CPU**: < 50% (acceptable for MVP)
- **Latency**: < 5 seconds additional delay (acceptable)
- **Memory**: < 1GB per stream (reasonable limit)

### Browser Compatibility

#### Supported Browsers

- Chrome 90+ (WebAssembly + fetch override support)
- Firefox 88+ (WebAssembly + fetch override support)
- Edge 90+ (WebAssembly + fetch override support)

#### Unsupported Browsers

- Safari (limited WebAssembly support)
- Older browsers without WebAssembly or fetch API

## Implementation Advantages

### Why MSW Interception is Superior

#### ✅ Reliability Benefits

1. **No Codec Mixing**: All-or-nothing approach prevents decoder failures
2. **Complete Metadata Consistency**: Manifests, init segments, and media segments all match
3. **Universal Compatibility**: Works with any video player, not just HLS.js
4. **Transparent Integration**: Player sees only consistent H.264 content

#### ✅ Architectural Benefits

1. **Zero Player Modifications**: No HLS.js loader customization required
2. **Standard Web APIs**: Uses fetch override pattern
3. **Cleaner Separation**: Transcoding logic separate from player logic
4. **Future-Proof**: No dependency on HLS.js internals

#### ✅ Maintainability Benefits

1. **Simpler Code**: Fetch override vs complex loader integration
2. **Easier Debugging**: Clear separation between transcoding and playback
3. **Better Testing**: Can test transcoding independently
4. **Lower Coupling**: Transcoding system independent of player

### Comparison with Previous HLS.js Loader Approach

| Aspect                     | Fetch-Level                      | HLS.js Loader                  |
| -------------------------- | -------------------------------- | ------------------------------ |
| **Integration Complexity** | ✅ Simple fetch override         | ❌ Complex loader architecture |
| **Reliability**            | ✅ Complete metadata consistency | ❌ Potential codec mixing      |
| **Player Compatibility**   | ✅ Universal (any player)        | ❌ HLS.js specific             |
| **Maintainability**        | ✅ Clear separation of concerns  | ❌ Mixed with player logic     |
| **Future-Proofing**        | ✅ No player dependencies        | ❌ Tied to HLS.js internals    |

## Implementation Status

### ✅ Complete (from previous work)

- [x] Browser H.265 detection logic
- [x] FFmpeg.wasm dependency setup
- [x] Basic transcoding worker foundation
- [x] Vision module integration points

### 🔄 Status

- [x] HLS.js loader code removed/unused
- [x] MSW interception implemented
- [x] Manifest rewriting logic in place
- [x] Init segment reconstructor in place
- [x] Integration via saga activation

### 📋 Next Steps

1. **Implement fetch interceptor**: Global override with URL type detection
2. **Build manifest processor**: Codec string replacement and validation
3. **Create init segment handler**: MP4 box reconstruction (hvcC → avcC)
4. **Update media transcoder**: Integrate with new pipeline
5. **Test single stream**: Verify end-to-end transcoding
6. **Add error handling**: Graceful fallbacks and recovery

## Memory Requirements [[memory:5483181]]

Never skip the transcoding of h265 to h264 mechanism when handling video streams in this project.

## Import Preferences [[memory:5483519]]

Always import from 'hls.js/dist/hls.mjs' instead of 'hls.js', except when importing types.

## Future Enhancements (Post-MVP)

### Phase 2: Multi-Stream Support

- Worker pool for multiple concurrent streams
- Intelligent caching and prefetching
- Performance optimization for 10+ streams

### Phase 3: Production Features

- Feature flags for gradual rollout
- Performance monitoring and metrics
- User experience improvements
- Advanced error recovery

## Technical Decisions

### Why Fetch-Level Over HLS.js Loader?

1. **Reliability First**: Eliminates codec mixing issues completely
2. **Universal Compatibility**: Not tied to specific player implementation
3. **Simpler Architecture**: Standard web APIs vs custom loader integration
4. **Easier Maintenance**: Clear separation of transcoding and playback logic
5. **Better Testing**: Can validate transcoding independently

### Constraints Maintained

- **Pure Software**: NO reliance on WebCodecs API or hardware acceleration
- **Quality Trade-offs**: Acceptable to reduce quality for software transcoding
- **Desktop Focus**: Primary target is desktop browsers
- **Performance Budget**: Prioritize reliability over performance for MVP

---

_This document reflects the updated requirements for the fetch-level interception approach, focusing on reliability and universal compatibility._
