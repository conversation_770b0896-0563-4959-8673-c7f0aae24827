# H.265 Software Transcoding Testing Setup - Fetch-Level Approach

````

## New Testing Approach - MSW Interception

**Important**: Testing setup has changed with the new fetch-level interception approach.

### Key Testing Differences

1. **No HLS.js Loader Testing**: We no longer test custom loader integration
2. **MSW Interception Testing**: Verify MSW handler is active and intercepting
3. **Manifest Rewriting**: Test codec string replacement in playlists
4. **Init Segment Reconstruction**: Validate MP4 box conversion (hvcC → avcC)

### Testing the MSW Interceptor

```javascript
// Trigger an HLS request and observe Network tab and logs
fetch('https://example.com/test.m3u8').then(r => {
  console.log('Test .m3u8 status:', r.status)
  console.log('Content-Type:', r.headers.get('content-type')) // expect application/vnd.apple.mpegURL when handled
})
```

Or use the native Worker support:
```javascript
// No special config needed for MSW; FFmpeg runs in main thread via FFmpeg.wasm.
```

### 4. Server Headers (Important for Firefox)

For SharedArrayBuffer support (better performance), add these headers:

```
Cross-Origin-Opener-Policy: same-origin
Cross-Origin-Embedder-Policy: require-corp
```

Without these, FFmpeg.wasm will work but slower.

## Testing Checklist

### Browser Setup

#### Firefox (No H.265 Support)

1. Download Firefox ESR or older version (< 120)
2. Verify H.265 not supported:
   - Open console
   - Run: `navigator.mediaCapabilities.decodingInfo({type: 'media-source', video: {contentType: 'video/mp4; codecs="hev1.1.6.L93.B0"', width: 1920, height: 1080, bitrate: 4000000, framerate: 30}}).then(r => console.log('H.265 supported:', r.supported))`
   - Should return `false`

#### Chrome/Edge (Depends on OS)

- Windows: Usually no H.265 support unless hardware decoder present
- macOS: Usually no H.265 support (Safari only)
- Linux: No H.265 support

### Test Scenarios

#### 1. Single Stream Test

```javascript
// Add to console for debugging
window.liveStreamPlayerDebug = true

// Monitor performance
setInterval(() => {
  if (window.performance && window.performance.memory) {
    console.log('Memory:', {
      used: (performance.memory.usedJSHeapSize / 1048576).toFixed(2) + ' MB',
      total: (performance.memory.totalJSHeapSize / 1048576).toFixed(2) + ' MB',
    })
  }
}, 5000)
```

#### 2. Multiple Stream Test (up to 10)

- Open multiple video players
- Monitor CPU usage in Task Manager/Activity Monitor
- Check for frame drops
- Verify audio sync

#### 3. Performance Metrics to Track

- [ ] Segment transcoding time (should be < 2 seconds for 2-second segments)
- [ ] CPU usage per stream (target < 10%)
- [ ] Memory usage per stream (target < 50MB)
- [ ] Frame drops (should be minimal)
- [ ] Audio/video sync (should maintain sync)

### Debug Commands for Console

```javascript
// Check if transcoding is active
console.log('H.265 Support:', window.canPlayHEVCUsingMediaSourceResult)

// Monitor HLS.js events
if (window.Hls) {
  const hls = window.hlsInstance // Assuming you expose it
  hls.on(Hls.Events.FRAG_LOADING, (event, data) => {
    console.log('Loading fragment:', data.frag.url)
  })
  hls.on(Hls.Events.FRAG_LOADED, (event, data) => {
    console.log(
      'Fragment loaded in:',
      data.stats.loading.end - data.stats.loading.start,
      'ms',
    )
  })
}

// Force software transcoding (for testing)
window.canPlayHEVCUsingMediaSourceResult = 'not_supported'

// Test fetch-level interception (NEW)
// Check if hlsTranscoder is active
console.log('HLS Transcoder status:', window.hlsTranscoder?.isEnabled())

// Monitor transcoding pipeline
if (window.hlsTranscoder) {
  // Enable detailed logging
  window.hlsTranscoder.setDebugMode(true)

  // Check cache status
  console.log('Transcoded segments cache:', window.hlsTranscoder.getCacheStats())

  // Monitor fetch interceptions
  window.hlsTranscoder.onIntercept((url, type) => {
    console.log(`Intercepted ${type}:`, url)
  })
}
```

## Expected Behavior

### When Fetch-Level Transcoding is Active

**NEW BEHAVIOR:**
1. **Manifest loading**: May see slight delay as codec strings are rewritten
2. **Init segment processing**: Additional delay for MP4 box reconstruction
3. **First segment**: Longer initial buffering (5-10 seconds) for transcoding setup
4. **Subsequent segments**: Faster due to worker pool and caching
5. **Network tab**: Will show original H.265 URLs but H.264 content returned
6. **HLS.js logs**: Will show H.264 codec info despite requesting H.265

**EXPECTED CONSOLE OUTPUT:**
```
[HLS-Transcoder] Fetch interceptor enabled
[HLS-Transcoder] Intercepted MASTER_PLAYLIST: playlist.m3u8
[HLS-Transcoder] Rewriting H.265 → H.264 codec strings
[HLS-Transcoder] Intercepted MEDIA_PLAYLIST: playlist_1.m3u8
[HLS-Transcoder] Intercepted INIT_SEGMENT: init.mp4
[HLS-Transcoder] Converting hvcC → avcC box
[HLS-Transcoder] Intercepted MEDIA_SEGMENT: segment_001.m4s
[HLS-Transcoder] Transcoding H.265 → H.264...
[HLS.js] Manifest parsed successfully (shows H.264 codec)
```

### Common Issues & Solutions

#### Issue: "FFmpeg.wasm failed to load"

**Solution:** Check CORS headers and file paths

#### Issue: "Worker failed to initialize"

**Solution:** Check MIME types and Worker syntax support

#### Issue: "Transcoding timeout"

**Solution:** Increase timeout values in H265TranscodingLoader

#### Issue: "High CPU usage"

**Solution:** Reduce quality settings or limit concurrent streams

#### Issue: "Audio out of sync"

**Solution:** Check segment timestamps and ensure audio passthrough

## Performance Tuning

### For Better Performance

```typescript
// In transcoding.worker.ts, adjust FFmpeg args:
const ffmpegArgs = [
  '-i',
  inputFile,
  '-c:v',
  'libx264',
  '-preset',
  'ultrafast', // Can try 'superfast' if CPU allows
  '-tune',
  'zerolatency',
  '-crf',
  '32', // Increase for lower quality/better performance (28-35 range)
  '-vf',
  'scale=1280:720', // Can reduce to 854:480 for better performance
  '-c:a',
  'copy',
  '-f',
  'mpegts',
  outputFile,
]
```

### For Better Quality

```typescript
const ffmpegArgs = [
  '-i',
  inputFile,
  '-c:v',
  'libx264',
  '-preset',
  'veryfast', // Better quality, more CPU
  '-tune',
  'film', // Better quality for movie content
  '-crf',
  '24', // Lower = better quality
  '-vf',
  'scale=1920:1080', // Full HD if CPU allows
  '-c:a',
  'copy',
  '-f',
  'mpegts',
  outputFile,
]
```

## Playwright Testing Script

```typescript
// For automated testing with Playwright
test('H.265 software transcoding', async ({ page }) => {
  // Navigate to video page
  await page.goto('your-video-url')

  // Enable debug mode
  await page.evaluate(() => {
    window.liveStreamPlayerDebug = true
  })

  // Listen for console logs
  page.on('console', (msg) => {
    if (msg.text().includes('TranscodingWorker')) {
      console.log('Transcoding:', msg.text())
    }
  })

  // Wait for video to start playing
  await page.waitForSelector('video')
  await page.waitForFunction(() => {
    const video = document.querySelector('video')
    return video && video.readyState >= 2
  })

  // Check if transcoding is active
  const isTranscoding = await page.evaluate(() => {
    return window.canPlayHEVCUsingMediaSourceResult !== 'supported'
  })

  console.log('Software transcoding active:', isTranscoding)

  // Monitor for 30 seconds
  await page.waitForTimeout(30000)

  // Check for errors
  const errors = await page.evaluate(() => {
    return window.transcodingErrors || []
  })

  expect(errors).toHaveLength(0)
})
```
````
