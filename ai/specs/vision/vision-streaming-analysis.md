# Vision Module Streaming Analysis

## Overview

The vision module implements a dual-streaming strategy for live video playback in the browser, with support for both WebSocket (VideoWs) and HLS (VideoLiveHLS) streaming protocols. The system is developing H.265/HEVC support for browser-based streaming.

## VisionPlayer.tsx Component Analysis

### Core Architecture

#### Key Components

1. **VisionPlayer**: Main orchestrator component that conditionally renders either VideoWs or VideoLiveHLS
2. **VehicleVisionPlayerWithHeader**: Wrapper for vehicle camera streams with UI header
3. **FacilityVisionPlayerWithHeader**: Wrapper for facility camera streams with UI header
4. **VisionPlayerHLS**: HLS-specific player implementation
5. **VisionPlayerWS**: WebSocket-specific player implementation

### Streaming Strategy Selection (Line 256-316)

The component uses a user-configurable setting to determine streaming strategy:

```typescript
const visionLiveStreamingStrategy = useTypedSelector(getVisionLiveStreamingStrategy)
```

Strategy selection logic (Line 302-315):

- **HLS mode**: Renders `VisionPlayerHLS` component
- **WebSocket mode**: Renders `VisionPlayerWS` component

### H.265/HEVC Codec Detection (Line 268-286)

The system intelligently selects video codec based on browser capabilities:

```typescript
preferredCodec: hardwareAccelerationSupportInfo.status === 'likely_supported' &&
canPlayHEVCUsingMediaSourceResult === 'supported'
  ? 'h265'
  : 'h264'
```

Key factors for H.265 selection:

1. Hardware acceleration must be enabled
2. Browser must support HEVC playback via Media Source Extensions
3. Falls back to H.264 if either condition fails

### URL Construction

#### HLS URLs (Line 356-364)

- Adds `nosound=1` parameter when audio is disabled
- Maintains original URL structure from backend

#### WebSocket URLs (Line 513-517)

- Converts HTTP URL to WebSocket: `wss://`
- Adds `ws=1` parameter
- Adds `nosound` parameter when audio is disabled

### State Management

#### Reconnection Logic

Both HLS and WS players implement reconnection state machine:

- `normal_streaming`: Regular playback state
- `closed_and_refetch_video_stream_url`: Triggers URL refetch on connection loss

#### Error Handling

- **Camera offline detection**: Shows user-friendly message (Line 401-427, 557-583)
- **Fatal error handling**: Refetches stream URL unless backend returns error
- **Media ended handling**: Automatically refetches stream URL

## VideoLiveHLS.tsx Component Analysis

### HLS.js Configuration (Line 95-102)

```typescript
new Hls({
  liveSyncDuration: 2, // Targets 2 seconds behind live edge
  maxLiveSyncPlaybackRate: 1.5, // Max 1.5x speed for catch-up
  enableWorker: true, // Offloads processing to Web Worker
  backBufferLength: 0, // No back buffer for live streams
  lowLatencyMode: false, // Standard latency mode
})
```

### Key Features

#### 1. Debug Mode (Line 52-74)

- Toggleable debug logging via `window.liveStreamPlayerDebug`
- Automatically enabled in development
- Comprehensive logging for troubleshooting

#### 2. Loading State Management (Line 121-156)

- Monitors video element ready state
- Shows loading spinner during buffering
- Considers multiple video element states:
  - `HAVE_NOTHING`: No data available
  - `HAVE_METADATA`: Metadata loaded
  - `HAVE_CURRENT_DATA`: Current frame available
  - `HAVE_FUTURE_DATA`: Can play forward
  - `HAVE_ENOUGH_DATA`: Can play smoothly

#### 3. HLS Event Handling (Line 203-236)

Key events monitored:

- **MANIFEST_PARSED**: Auto-plays video when manifest ready
- **FRAG_BUFFERED**: Fragment successfully buffered
- **FRAG_LOADED**: Fragment loaded from network
- **MEDIA_ENDED**: Stream ended (triggers reconnection)
- **ERROR**: Fatal errors trigger error callback

#### 4. Memory Management (Line 239-259)

- Revokes object URLs to prevent memory leaks
- Properly destroys HLS instance on unmount
- Cleans up event listeners

#### 5. User Controls

##### Volume Control (Line 467-547)

- Slider for volume adjustment (0-1 range)
- Mute/unmute toggle button
- Synchronized with video element state

##### Fullscreen Support (Line 549-570)

- Uses screenfull library for cross-browser compatibility
- Toggles between fullscreen and normal view
- Adapts UI overlay behavior in fullscreen mode

#### 6. UI Overlay Behavior

- **Normal mode**: Shows controls on hover
- **Fullscreen mode**: Shows controls on mouse movement, auto-hides after 3 seconds

### Error States

The component handles multiple error scenarios:

1. **mime_type_not_supported**: Browser cannot play the codec (likely H.265 without hardware acceleration)
2. **unknown**: Generic fatal error
3. **Network errors**: Handled via HLS.js error events

### Performance Optimizations

1. **Web Workers**: Offloads HLS processing from main thread
2. **No back buffer**: Reduces memory usage for live streams
3. **Adaptive playback rate**: Catches up to live edge when behind
4. **Object URL management**: Prevents memory leaks

## Integration Points

### Backend API

- Uses React Query for fetching stream metadata
- Endpoints return stream URLs with codec preferences
- Supports both vehicle and facility camera sources

### Redux Integration

- User settings stored in Redux (audio, streaming strategy)
- Accessed via typed selectors

### Analytics

- Tracks fullscreen events via GA
- Logs streaming events for debugging

## Key Observations for H.265 Implementation

### Current H.265 Support Status

1. **Detection**: System already detects H.265 capability
2. **Selection**: Automatically prefers H.265 when available
3. **Fallback**: Gracefully falls back to H.264

### Requirements for H.265 Streaming

1. Browser must support HEVC via MSE
2. Hardware acceleration must be enabled
3. Backend must provide H.265-encoded streams
4. HLS manifest must declare H.265 codec

### H.265 Implementation Strategy

**NEW APPROACH: MSW Interception**

Based on analysis of reliability requirements, the implementation uses **MSW-based interception** rather than HLS.js custom loaders:

#### Why MSW Interception is Superior

1. **Complete Transparency**: HLS.js sees only H.264 content, no modifications needed
2. **Universal Compatibility**: Works with any video player, not just HLS.js
3. **Maximum Reliability**: All-or-nothing transcoding prevents codec mixing
4. **Metadata Consistency**: Manifests, init segments, and media segments all match

#### Implementation Components

1. **MSW handler (`hlsMSWHandler.ts`)**: Intercepts HLS requests at the network layer
2. **Manifest rewriting**: Update codec strings (H.265→H.264)
3. **Init segment reconstruction**: MP4 box manipulation (hvcC→avcC)
4. **Media segment transcoding**: FFmpeg.wasm H.265→H.264 conversion

Activation is managed in `apps/fleet-web/src/sagas/user.ts` based on the streaming strategy and HEVC capability.

#### Integration with Vision Module

- **VideoLiveHLS**: Enable fetch interception when H.265 support missing
- **VisionPlayer**: Always request H.265 for bandwidth savings [[memory:5483181]]
- **Error handling**: Hard-fail on transcoding/processing errors (no codec mixing)

### Current Playback Findings (Aug 2025)

- **Stalls every 7–10s**: HLS logs show repeated `bufferStalledError` with very short forward buffer. We also see frequent `Adjusting initPTS` and `Updating timestampOffset` per fragment, indicating fragments are not carrying a stable, increasing timeline (tfdt missing/zero or regenerated PTS near 0). HLS falls back to playlist timing and keeps seeking to live edge, aborting in-flight loads and causing underflow.
- **FFmpeg.wasm OOB crash**: After minutes of playback, a specific fragment may trigger `RuntimeError: memory access out of bounds`. Likely causes:
  - Non-serialized `ffmpeg.exec` calls on a single FFmpeg.wasm instance (race/heap corruption)
  - Stale/cached init prepended to a fragment whose bitstream parameters changed (hidden discontinuity). Demux mismatch can crash wasm.
- **Manifest segment sequencing**: Sample durations (~1s) and PROGRAM-DATE-TIME look consistent; issues are timing/container, not manifest cadence.

### Immediate Actions

1. **Fragment timing**

- Emit moof+mdat per fragment with valid tfdt (default-base-is-moof); avoid per-fragment moov/mfra.
- If regenerating PTS, apply a consistent offset from playlist timing so each fragment’s timeline is monotonic.

2. **Player guardrails**

- Increase `liveSyncDuration` to 4–6 seconds to reduce seek-to-live churn while timing is fixed.
- Keep `backBufferLength` small (0–5).

3. **FFmpeg stability**

- Serialize `ffmpeg.exec` (one job at a time) and set `-threads 1`.
- Refresh init on new `EXT-X-MAP` or discontinuity; skip prepending init if fragment already contains `moov/ftyp`.
- On demux failure, retry once without prepended init; reinitialize FFmpeg on hard failures.

4. **Optional PTS offset**

- Derive per-fragment start from `PROGRAM-DATE-TIME` or accumulated `EXTINF` and pass as `setpts` offset when needed.

## Technical Dependencies

### External Libraries

- **hls.js**: HLS streaming implementation
- **screenfull**: Cross-browser fullscreen API
- **ts-pattern**: Pattern matching for conditional logic
- **Material-UI**: UI components and styling

### Internal Utilities

- Custom hooks for event handling
- Video utility functions for capability detection
- Redux hooks for state management
