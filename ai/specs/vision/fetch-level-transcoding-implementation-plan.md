# Fetch-Level H.265 Transcoding Implementation Plan

_NEW ARCHITECTURE: Browser-level interception for maximum reliability_  
_Status: IMPLEMENTED (via MSW handler)_  
_Last Updated: December 2024_

## 🎯 Implementation Strategy

**Approach**: Use MSW-based request interception for HLS resources for universal compatibility and maximum reliability. Implemented in `apps/fleet-web/src/mswWorker/hlsMSWHandler.ts`.

## 📋 Implementation Steps

### Phase 1: Core Interceptor (Completed)

#### 1.1 Implement `hlsMSWHandler.ts` (COMPLETE)

- [x] MSW `http.get(/\.(m3u8|mp4|m4s)(\?.*)?$/i)` to intercept HLS requests
- [x] Resource-type routing (manifest/init/media)
- [x] Graceful passthrough on unreadable manifests (opaque/cors)

#### 1.2 Manifest Processor (COMPLETE)

- [x] `manifestProcessor.processManifest` replaces H.265 codec strings with H.264
- [x] Validation to ensure no HEVC remains and AVC is present

### Phase 2: Segment Processing (Completed)

#### 2.1 Init Segment Reconstructor (COMPLETE)

- [x] `initSegmentProcessor.processInitSegment` converts MP4 boxes (hvcC→avcC, hev1/hvc1→avc1)
- [x] RAW HEVC init cached for FFmpeg demux input

#### 2.2 Media Segment Transcoder (COMPLETE)

- [x] `MediaSegmentProcessorSingleton.transcodeSegment` with FFmpeg.wasm
- [x] CRF/preset, CFR, GOP alignment, audio copy, TS/MP4 container flags
- [x] Temp file cleanup and logging

### Phase 3: Integration & Optimization (Completed)

#### 3.1 Transcoding activation

- [x] In `user.ts` saga, conditionally register `hlsMSWHandler` and start MSW when HLS strategy + no native H.265 support
- [x] Pre-warm FFmpeg on activation; cleanup on logout

#### 3.2 Performance Optimization (In Progress)

- [x] Short-lived per-URL cache (5s TTL)
- [x] Singleflight inflight map to avoid duplicate work
- [ ] Worker pooling/prefetching/memory monitoring (future)

### Phase 4: Testing & Validation (Ongoing)

#### 4.1 Unit Testing

- [ ] Manifest rewriting accuracy
- [ ] Init segment conversion
- [ ] Media transcoding
- [ ] Error handling

#### 4.2 Integration Testing

- [ ] **Single stream playback**: End-to-end validation
- [ ] **Multiple browsers**: Chrome, Firefox, Edge testing
- [ ] **Performance benchmarking**: CPU/memory usage measurement
- [ ] **Error scenarios**: Network failures, transcoding timeouts

## 🏗️ File Structure (As Implemented)

```
apps/fleet-web/src/mswWorker/
├── hlsMSWHandler.ts             # MSW handler intercepting HLS resources

apps/fleet-web/src/modules/vision/VisionLiveStream/transcoding/
├── FFmpegManagerSingleton.ts    # FFmpeg.wasm loader/manager
├── MediaSegmentProcessorSingleton.ts # H.265 → H.264 transcoder
├── initSegmentProcessor.ts      # MP4 init segment converter
└── manifestProcessor.ts         # Manifest codec rewriter
```

## 🔧 Key Implementation Details

### Interception Architecture (MSW)

Handled by `hlsMSWHandler.ts` using `msw`'s `http.get` route matcher with resource-type branching and per-URL caching/singleflight.

> Important: `.ts` MPEG-TS segments are not parsed or processed by the handler. By backend contract, `.ts` segments are always H.264 (AVC), so no rewriting or transcoding is required; they are passed through without interception. HEVC detection and transcoding are driven by playlist semantics (e.g., `#EXT-X-VERSION:7` with fMP4 and `#EXT-X-MAP`).

### Activation

Activation/cleanup handled in `apps/fleet-web/src/sagas/user.ts` (see README doc for code pointers).

### Codec Preference [[memory:5483181]]

The system requests H.265 when supported; when not, MSW ensures the browser receives H.264 with strict all-or-nothing transcoding.

## ✅ Success Criteria

### Must Have (MVP)

- [ ] **Single stream playback**: H.265 → H.264 transcoding works
- [ ] **Browser compatibility**: Chrome, Firefox, Edge support
- [ ] **Reliability**: No codec mixing, consistent metadata
- [ ] **Performance**: < 50% CPU usage for single stream
- [ ] **Memory**: < 1GB memory usage per stream

### Nice to Have (Future)

- [ ] **Multi-stream support**: 10+ concurrent streams
- [ ] **Quality adaptation**: CPU-based optimization
- [ ] **Advanced caching**: Intelligent prefetching
- [ ] **Performance analytics**: Real-time monitoring

## 🚨 Critical Requirements

### Memory Requirements [[memory:5483181]]

Never skip the transcoding of h265 to h264 mechanism when handling video streams in this project.

### Import Preferences [[memory:5483519]]

Always import from 'hls.js/dist/hls.mjs' instead of 'hls.js', except when importing types.

### 🔥 STRICT ALL-OR-NOTHING TRANSCODING

**FUNDAMENTAL RULE**: Either transcode EVERYTHING perfectly or FAIL COMPLETELY

#### Assumptions:

- **Server ALWAYS sends H.265**: Assume all content is H.265 from backend
- **No native H.265 fallback**: Browser never receives H.265 content
- **Complete stream consistency**: HLS.js sees only H.264 throughout

#### What Must Be Transcoded:

1. **Master Playlist**: H.265 → H.264 codec strings (`hvc1` → `avc1`)
2. **Media Playlists**: All codec references updated consistently
3. **Init Segments**: MP4 box reconstruction (`hvcC` → `avcC` boxes)
4. **Media Segments**: Every single segment transcoded H.265 → H.264

#### Failure Scenarios (MUST ABORT STREAM):

- ❌ **FFmpeg.wasm initialization fails**: Abort immediately
- ❌ **Manifest download fails**: Abort entire stream
- ❌ **Codec replacement fails**: Abort entire stream
- ❌ **Init segment conversion fails**: Abort entire stream
- ❌ **ANY media segment transcoding fails**: Abort entire stream

#### Success Requirements:

- ✅ **100% consistency**: All components show H.264 codec info
- ✅ **No codec mixing**: Browser NEVER sees H.265 content
- ✅ **Transparent operation**: HLS.js works normally with "H.264" stream
- ✅ **Complete reliability**: Either perfect transcoding or complete failure

## 🔄 Migration from Current Approach

### Files to Remove

- ✅ `SimpleTranscodingLoader.ts` - No longer needed
- ✅ `apps/fleet-web/src/modules/vision/VisionLiveStream/transcoding/README.md` - Removed

### Files to Update

- [ ] `VideoLiveHLS.tsx` - Remove custom loader, add fetch interceptor
- [ ] `transcoding.worker.ts` - Update for new pipeline integration
- [ ] `VisionPlayer.tsx` - Always request H.265

### New Files to Create

- [ ] `hlsTranscoder.js` - Main fetch interceptor
- [ ] `manifestProcessor.js` - Playlist rewriting
- [ ] `initSegmentProcessor.js` - MP4 box conversion
- [ ] `mp4Parser.js` - MP4 structure manipulation

## 📊 Timeline

| Week | Focus              | Deliverables                            |
| ---- | ------------------ | --------------------------------------- |
| 1    | Core Interceptor   | Fetch override + manifest processing    |
| 2    | Segment Processing | Init segment + media transcoding        |
| 3    | Integration        | VideoLiveHLS integration + optimization |
| 4    | Testing            | Validation + performance tuning         |

---

_This fetch-level approach provides maximum reliability and universal compatibility while maintaining clean architecture separation._
