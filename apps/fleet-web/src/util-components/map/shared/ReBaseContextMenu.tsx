import { useLayoutEffect, useRef } from 'react'
import type * as React from 'react'
import { Box, Stack, styled as styledMui, Typography } from '@karoo-ui/core'
import styled from 'styled-components'

import Icon from 'src/components/Icon'
import { ctIntl } from 'src/util-components/ctIntl'

import { useMapContainerContext } from './MapContainerWithProvider'

type Props = {
  items: Array<{
    message: string
    disabled?: boolean
    icon: React.ReactNode
    handleOnClick?: React.MouseEventHandler<HTMLDivElement>
  }>
  position: { y: number; x: number }
}

function ReBaseContextMenu({ items, position }: Props) {
  return (
    <RootWithMapLimits menuPosition={position}>
      {items.map((item) => (
        <ItemRoot
          key={item.message}
          disabled={item.disabled}
          onClick={item.handleOnClick}
        >
          <Stack sx={{ fontSize: '20px', color: 'rgba(0, 0, 0, 0.56)' }}>
            {item.icon}
          </Stack>
          <ItemText value={item.message} />
        </ItemRoot>
      ))}
    </RootWithMapLimits>
  )
}

type RootProps = {
  menuPosition: Props['position']
}

const Root = styledMui(Box, {
  shouldForwardProp: (prop) => prop !== 'menuPosition',
})<RootProps>(({ theme, menuPosition }) =>
  theme.unstable_sx({
    position: 'absolute',
    background: '#fff',
    borderRadius: '4px',
    zIndex: '99',
    top: menuPosition.y,
    left: menuPosition.x,
    py: 1,
  }),
)

const RootWithMapLimits = ({
  menuPosition,
  children,
}: RootProps & { children: React.ReactNode }) => {
  const rootRef = useRef<HTMLDivElement>(null)
  const { containerRef } = useMapContainerContext()

  useLayoutEffect(() => {
    if (containerRef && rootRef.current) {
      const maxX = containerRef.offsetWidth - rootRef.current.offsetWidth
      const maxY = containerRef.offsetHeight - rootRef.current.offsetHeight - 20
      const x = Math.min(maxX, menuPosition.x)
      const y = Math.min(maxY, menuPosition.y)

      rootRef.current.style.top = y + 'px'
      rootRef.current.style.left = x + 'px'
    }
  }, [containerRef, menuPosition.x, menuPosition.y])

  return (
    <Root
      ref={rootRef}
      menuPosition={menuPosition}
    >
      {children}
    </Root>
  )
}

const ItemRoot = styledMui(Stack, {
  shouldForwardProp: (prop) => prop !== 'disabled',
})<{ disabled?: boolean }>(({ disabled, theme }) =>
  theme.unstable_sx({
    px: 2,
    py: 0.5,
    flexDirection: 'row',
    alignItems: 'center',
    gap: 2,
    cursor: 'pointer',
    whiteSpace: 'nowrap',
    opacity: disabled ? 0.5 : 1,
    pointerEvents: disabled ? 'none' : 'all',
    '&:hover': {
      background: disabled ? '#fff' : '#eee',
    },
  }),
)

const ItemText = ({
  value,
  ...rest
}: React.HTMLAttributes<HTMLAnchorElement> & { value: string }) => (
  <a {...rest}>
    <Typography sx={{ color: 'rgba(0, 0, 0, 0.87)' }}>
      {ctIntl.formatMessage({ id: value })}
    </Typography>
  </a>
)

const ItemIcon = styled(Icon).attrs({
  className: 'MapContextMenu-item-icon',
})``

export default Object.assign(ReBaseContextMenu, {
  Root,
  ItemRoot,
  ItemIcon,
  ItemText,
  RootWithMapLimits,
})
