import { replace, push } from 'connected-react-router'
import { jwtDecode } from 'jwt-decode'

import * as Sen<PERSON> from '@sentry/browser'
import {
  takeLatest,
  call,
  put,
  fork,
  spawn,
  select,
  takeLeading,
  putResolve,
} from 'typed-redux-saga'

import { HubConnectionBuilder, LogLevel } from '@microsoft/signalr'

import userAPI from '../api/user'
import cache from '../api/cache'
import { getCostsAPIFrom } from '../api/mifleet/api-caller'
import { handleApiEffect } from './mifleet/utils'
import {
  makeLoadable,
  makeToast,
  makeMessage,
  waitForConditionToBeTruthy,
  createLoadableSaga,
  waitForValidJwtAccessToken,
} from './utils'
import { fetchSharedVehicleDataSaga } from './shared'
import {
  LOG_IN,
  FEDERATED_LOG_IN,
  TOKEN_LOG_IN,
  loginFailed,
  SUB_USER_LOGIN,
  receivedThirdPartyPreLoginData,
  SET_USER_PASSWORD,
  PASSWORD_UPDATE_STATUS,
  ON_SAVE_PROFILE,
  SAVE_PREFERENCE,
  SET_SYSTEM_STATE_MESSAGE,
  SUBMIT_HELP_REQUEST,
  federatedLogin,
  fetchPreLoginData,
  RECEIVE_USER_SETTINGS,
  TOGGLE_SSO_HASH,
  login,
  type tokenLogin,
  type subUserLogin,
  getThirdPartyUser,
  submittedThirdPartyLogin,
  type setUserPassword,
  getSettings_UNSAFE,
  getLocale,
  type toggleUserSsoHash,
  type savePreferences,
  type submitHelpRequest,
  tokenLoginSucceeded,
  loginSucceeded,
  getKarooUiTheme,
  logoutFailed,
  resetUserStateBeforeAttemptingALogin,
  loggedOutFromAnotherTab,
  logout,
  handleSaveProfileSuccess,
  SUBMIT_CLEAR_FUEL,
  receivedTwoFAPreLoginData,
  startedPreLoginFetch,
  failedPreLoginFetch,
  getPreLoginQuery,
  getEnableWelcomePageSetting,
  defaultPreferences,
  startLogOut,
  onUpdateVehicleLivePositionSuccess,
  getSettings,
  getUserStateFromSaga,
  type DevicePositionStreamSubscriptionMsg,
  receivePreLoginData,
  setKeyIdbStateWithReduxSync,
  getIdbStateForKey,
  setKeyIdbStateOnRedux,
  createVehicleLivePositionStrategy,
  getISO3166Alpha2CountryCode,
  getVisionSetting,
} from 'duxs/user'
import {
  getAppMainUrl,
  APP_MAIN_URL_NOT_AVAILABLE_VALUE,
} from 'duxs/app-routes-selectors'

import { actions as mifleetUserActions } from 'duxs/mifleet/user'
import { ctIntl } from 'src/util-components/ctIntl'
import type { FixMeAny } from 'src/types'
import type { AppState } from 'src/root-reducer'
import { queryClient } from 'src/RouterRoot'
import type { LoginTabOptionValue } from 'src/modules/app/authentication/login'
import { isNil, isEmpty } from 'lodash'
import type { ParsedKarooUiCustomizableTheme } from 'api/user/utils'
import { fetchDocumentArrayTypes } from 'src/modules/mifleet/DocumentsEdit/slice'
import { match, P } from 'ts-pattern'
import listDataApi from '../api/mifleet/list-data'
import { GA4 } from 'src/shared/google-analytics4'
import {
  isNilOrEmptyTrimmedStringOrFalseish,
  isNonEmptyTrimmedStringAndNotFalseish,
} from 'src/util-functions/string-utils'
import { secondsToMs } from 'src/util-functions/functional-utils'
import { onLeftPanelResetAllVehicleFiltersButtonClick } from 'src/modules/map-view/actions'
import {
  isNonEmptyArray,
  type ExcludeStrict,
  type NonEmptyArray,
} from 'src/types/utils'
import { idbStateKeysToDeleteOnLogout } from 'src/hooks/useUserIdbState'
import { batchReduxStateUpdatesFromSaga } from './actions'
import {
  onAppMounted,
  onSetInvoicingUpdateModalSeenMutationStarted,
  onSetNewPlatformAnnouncementSeenMutationStarted,
  onSetVisionPromotionalModalSeenMutationStarted,
  onUrlLoginSearchParamsChange,
  triggerRefreshJWTAccessToken,
} from 'duxs/sharedActions'
import { buildQueryStringFromObject } from 'api/utils'
import {
  getVehicles,
  TRY_REFETCH_VEHICLES_POSITIONS,
  type VehiclesReduxState,
  type RealTimeVehicleUpdateObject,
} from 'duxs/vehicles'
import type { AppSagaInjectedDeps } from './root-saga'
import { getMapStateFromSaga } from 'duxs/map'
import {
  getJwtAccessToken,
  getShowLivePositionsStoreless,
  getCanSetupSignalRHubConnectionMetaStoreless,
  getUser,
  LOGGED_OUT,
  getVisionLiveStreamingStrategy,
} from 'duxs/user-sensitive-selectors'
import { selectState, storeDispatch } from 'src/redux-store-sync'
import type { TerminalSerial } from 'api/types'
import { enqueueSnackbarWithButtonAction } from 'src/components/Snackbar/Notistack/utils'
import { SETTINGS } from 'src/modules/app/components/routes/settings'
import { update as updateIdb } from 'idb-keyval'
import {
  initPosthog,
  resetPosthog,
  maybeRegisterPosthogUser,
  logException,
  logConsoleError,
  errorMonitoringToolIgnoredErrors,
} from 'src/shared/posthog-integration'
import { SafeSignalRHubConnection } from 'src/util-functions/SafeSignalRHubConnection'
import { LRUCache } from '@karoo/utils'
import { createAnalyticsGlobalUniqueUserId } from 'duxs/utils'
import {
  canPlayHEVCUsingMediaSourceResult,
  hardwareAccelerationSupportInfo,
} from 'src/util-functions/video-utils'
import { mswWorker } from 'src/mswWorker'
import { hlsMSWHandler } from 'src/mswWorker/hlsMSWHandler'
import { FFmpegManagerSingleton } from 'src/modules/vision/VisionLiveStream/transcoding/FFmpegManagerSingleton'

const MOCK_ACCESS_TOKEN = 'giberish_so_that_FE_Still_works_while_BE_IS_unstable'

// Default to false so that we don't log any signalR errors in production but we do in dev.
window.signalRConnectionDebug = false
const logSignalR = (type: 'debug' | 'warn' | 'error', ...args: Array<any>): void => {
  const logContext = '[signalR]'
  if (type === 'error') {
    logConsoleError({ monitorTool: 'both', log: [logContext, ...args] })
    return
  }

  if (!window.signalRConnectionDebug) {
    return
  }
  switch (type) {
    case 'debug': {
      // eslint-disable-next-line no-console
      console.log(logContext, ...args)

      return
    }
    case 'warn': {
      console.warn(logContext, ...args)
      return
    }
  }
}

// Using a cache with a limit to prevent leaking too much memory, in case we have a memory leak bug.
const realTimeVehiclePositionsQueue = new LRUCache<
  TerminalSerial,
  DevicePositionStreamSubscriptionMsg
>(1000)
function runRealTimeVehiclePositionsQueueUpdates() {
  if (realTimeVehiclePositionsQueue.getSize() === 0) {
    return
  }

  storeDispatch(
    batchReduxStateUpdatesFromSaga({
      vehiclesState: (state): VehiclesReduxState => {
        const newVehicles = [...(state.vehicles ?? [])]
        for (let i = 0; i < newVehicles.length; i++) {
          const vehicle = newVehicles[i]
          if (realTimeVehiclePositionsQueue.getSize() === 0) {
            // No more messages to process. We can stop looping over remaining vehicles
            // This improves performance of these updates
            break
          }
          const msg = realTimeVehiclePositionsQueue.get(vehicle.terminalSerial)
          if (!msg) {
            continue
          }

          // Remove the message from the queue and process it
          realTimeVehiclePositionsQueue.delete(vehicle.terminalSerial)

          if (msg.eventType === 'NotSupported') {
            // NotSupported eventType usually contains faulty data. We can skip it
            continue
          }

          const updateObject: RealTimeVehicleUpdateObject = {
            latitude: msg.position.latitude,
            longitude: msg.position.longitude,
            bearing: msg.position.gpsHeading,
            speed: msg.position.speed,
            __webSocketEventTsUnix: new Date(msg.eventTsUtc).getTime(),
          }

          const updatedVehicle: typeof vehicle = {
            ...vehicle,
            ...updateObject,
          }

          logSignalR(
            'debug',
            'UPDATED_VEHICLE:',
            'REG:',
            updatedVehicle.registration,
            ', NAME:',
            updatedVehicle.name,
            ', UPDATE_OBJECT:',
            updateObject,
          )
          newVehicles[i] = updatedVehicle
        }

        return {
          ...state,
          vehicles: newVehicles,
        }
      },
    }),
  )
}

function fallbackVehicleLivePositionStrategyToPolling() {
  const { currentVehicleLivePositionStrategy } = selectState(getUserStateFromSaga)
  storeDispatch(
    batchReduxStateUpdatesFromSaga({
      userState: {
        currentVehicleLivePositionStrategy: createVehicleLivePositionStrategy({
          currentValue: currentVehicleLivePositionStrategy,
          newValue: { type: 'polling_or_idle' },
        }),
      },
    }),
  )
}

let queueIntervalId: number | null = null

function createSingletonDevicesPositionsStream({
  hubConnection,
  terminalSerials,
}: {
  hubConnection: Readonly<SafeSignalRHubConnection>
  // The stream does not accept an empty array. We need to pass at least one terminalSerial or it will fail.
  // This can happen if we refresh the window on a page that doesn't fetch vehicle list. Then, we don't have any vehicles to subscribe to.
  terminalSerials: NonEmptyArray<TerminalSerial>
}) {
  const { currentVehicleLivePositionStrategy } = selectState(getUserStateFromSaga)
  if (
    currentVehicleLivePositionStrategy.type === 'signalr' &&
    currentVehicleLivePositionStrategy.devicePositionStreamSubscription
  ) {
    // Dispose of the previous subscription if it exists
    currentVehicleLivePositionStrategy.devicePositionStreamSubscription.dispose()
  }
  // Make sure there is only one handler for this event to prevent race conditions
  const streamSubscription = hubConnection
    .stream<DevicePositionStreamSubscriptionMsg>('StreamDevicePosition', {
      Serials: terminalSerials,
    })
    .subscribe({
      next: (msgItem) => {
        realTimeVehiclePositionsQueue.set(msgItem.serial, msgItem)
      },
      complete: () => {},
      error: (err) => {
        logSignalR(
          'error',
          'Error in StreamDevicePosition. Falling back to polling...',
          err,
        )
        fallbackVehicleLivePositionStrategyToPolling()
      },
    })

  if (queueIntervalId) {
    window.clearInterval(queueIntervalId)
  }
  queueIntervalId = window.setInterval(runRealTimeVehiclePositionsQueueUpdates, 2500)

  return { streamSubscription }
}

async function maybeSetupUserHubConnectionOrFallbackToPolling({
  initialSubscribedTerminalSerials,
  initialAccessToken,
  userSettings,
}: {
  initialSubscribedTerminalSerials: Array<TerminalSerial> | 'not_available_yet'
  initialAccessToken: string | null | undefined
  userSettings: Record<string, unknown>
}) {
  const { currentVehicleLivePositionStrategy } = selectState(getUserStateFromSaga)

  if (!isNonEmptyTrimmedStringAndNotFalseish(initialAccessToken)) {
    logSignalR(
      'error',
      'No initial access token found. Faulty BE implementation of jwt auth. Falling back to polling.',
      { initialAccessToken },
    )
    fallbackVehicleLivePositionStrategyToPolling()
    return
  }
  const setupMeta = getCanSetupSignalRHubConnectionMetaStoreless(userSettings)
  if (!setupMeta) {
    logSignalR(
      'warn',
      'Not enough meta data setup to start hub connection. Falling back to polling.',
      {
        setupMeta,
      },
    )
    fallbackVehicleLivePositionStrategyToPolling()
    return
  }

  const hubConnection_ = new HubConnectionBuilder()
    .withUrl(setupMeta.connectionUrl, {
      // Always use the latest access token because the token might have been refreshed in the middle of the connection
      accessTokenFactory: () => selectState(getJwtAccessToken) ?? '',
    })
    .withAutomaticReconnect()
    .configureLogging(ENV.NODE_ENV === 'development' ? LogLevel.Debug : LogLevel.Error)
    .build()
  const hubConnection = new SafeSignalRHubConnection(hubConnection_)

  const startHubConnectionResult = await hubConnection.start()
  if (startHubConnectionResult.isErr()) {
    logSignalR(
      'error',
      'Failed to start hub connection. Falling back to polling.',
      startHubConnectionResult.error,
    )
    fallbackVehicleLivePositionStrategyToPolling()
    return
  }

  // Only start stream when connection is established
  const streamSubscription =
    initialSubscribedTerminalSerials === 'not_available_yet' ||
    !isNonEmptyArray(initialSubscribedTerminalSerials)
      ? null
      : createSingletonDevicesPositionsStream({
          hubConnection,
          terminalSerials: initialSubscribedTerminalSerials,
        }).streamSubscription

  storeDispatch(
    batchReduxStateUpdatesFromSaga({
      userState: {
        currentVehicleLivePositionStrategy: createVehicleLivePositionStrategy({
          currentValue: currentVehicleLivePositionStrategy,
          newValue: {
            type: 'signalr',
            devicePositionStreamSubscription: streamSubscription,
            hubConnection: hubConnection,
          },
        }),
      },
    }),
  )
}

function* fetchSystemStateMessage() {
  const systemStateMessage = yield* call(userAPI.fetchSystemStateMessage)
  if (
    systemStateMessage &&
    systemStateMessage.systemStateMessage[0] &&
    systemStateMessage.systemStateMessage[0].out_message
  ) {
    yield* put({
      type: SET_SYSTEM_STATE_MESSAGE,
      payload: {
        systemStateMessage: systemStateMessage.systemStateMessage[0].out_message,
      },
    })
    return true
  }

  return false
}

function* toggleSsoHashSaga(action: ReturnType<typeof toggleUserSsoHash>) {
  const ssoHash = yield* call(userAPI.toggleSSOHash, action.payload)
  yield* put({
    type: RECEIVE_USER_SETTINGS,
    payload: {
      settings: {
        ssoHash,
      },
    },
  })
}

function* UserClearDataSaga() {
  yield handleApiEffect(
    call(listDataApi.postListData, `costs/onboardingJSON.php?action=delete`, {}),
    {
      *onSuccess() {
        yield* call(makeToast, 'success', `Data cleared.`)
      },
      *onFailure(error) {
        const splitedNumber = error.message.split(/[{}]/)
        yield* call(
          makeToast,
          'error',
          ctIntl.formatMessage(
            { id: 'profile.setting.clear.data' },
            { values: { num1: splitedNumber[1], num2: splitedNumber[3] } },
          ),
        )
      },
    },
  )
}

function* maybeDisplayResolutionWarning() {
  const w = Math.max(document.documentElement.clientWidth, window.innerWidth || 0)
  if (w < 1024) {
    yield* call(
      makeMessage,
      ctIntl.formatMessage({ id: 'Window or screen size too small' }),
      ctIntl.formatMessage({
        id: 'We recommend a screen that supports 1024 x 768 pixels or greater for the best experience of Fleet.',
      }),
      ctIntl.formatMessage({ id: 'Got it' }),
    )
  }
}

export function* fetchMifleetAuthMetaData(
  { settings }: { settings: Record<string, unknown> },
  {
    onSuccess = function* () {},
    onFailure = function* () {},
    onFinish = function* () {},
  } = {} as ExcludeStrict<Parameters<typeof handleApiEffect>[1], undefined>, // additional handlers
) {
  yield handleApiEffect(
    call(
      userAPI.fetchMifleetAuthMetaData,
      getCostsAPIFrom(settings), // Sending costsApi explictly to avoid side effects in this function (like storing userSettings in SessionStorage)
    ),
    {
      *onSuccess(permissions) {
        yield* put(mifleetUserActions.receivePermissions(permissions))
        yield* put(fetchDocumentArrayTypes())
        yield onSuccess()
      },
      onFailure,
      onFinish,
    },
  )
}

function* loginSaga(
  { storeDispatch, history }: AppSagaInjectedDeps,
  {
    payload,
  }: Pick<ReturnType<typeof login> | ReturnType<typeof federatedLogin>, 'payload'>,
) {
  const userFromLocalStorage = localStorage.getItem('user')
  const user = yield* select(getUser)

  if (
    user ||
    (userFromLocalStorage !== null && !isEmpty(JSON.parse(userFromLocalStorage)))
  ) {
    // We are currently logged in on the current tab or in another tab
    yield logoutSaga({ payload: {} }) // Log out before trying to log in
  }

  // MAKE SURE we receive the pre-login data before logging in. Essential data like locales are needed for the login process
  yield waitForConditionToBeTruthy(function* () {
    const preLoginQuery = yield* select(getPreLoginQuery)
    return preLoginQuery.status === 'success'
  })

  yield* put(resetUserStateBeforeAttemptingALogin(payload))

  const locale = yield* select(getLocale)

  try {
    const account =
      payload.type === 'federated'
        ? yield* call(userAPI.federatedLogin, payload.federatedResponse)
        : yield* call(
            userAPI.login,
            match(payload)
              .with({ type: 'sso' }, ({ sso, t }) => ({
                username: sso,
                subUserUsername: '',
                password: undefined,
                locale,
                otp: '',
                vehicle: payload.vehicle,
                ssoToken: t,
              }))
              .with(
                { type: 'credentials_login' },
                ({ password, username, adminLogin, subUsername }) => ({
                  username,
                  subUserUsername: adminLogin === 'admin' ? '' : subUsername || '',
                  password,
                  locale,
                  otp: undefined,
                  vehicle: payload.vehicle,
                }),
              )
              .with({ type: 'otp' }, ({ otp, username }) => ({
                username,
                subUserUsername: '',
                password: undefined,
                locale,
                otp,
                vehicle: payload.vehicle,
              }))
              .exhaustive(),
          )

    if (account.status === 'THIRD_PARTY_USER') {
      yield* put(
        receivedThirdPartyPreLoginData({
          vehicleList: account.vehicleList,
          loginPayloadData: payload,
        }),
      )

      yield* put(replace('/third-party-user'))
      return
    }

    if (account.status === 'TWO_FACTOR_AUTHENTICATION') {
      // save the 2FA contacts
      yield* put(
        receivedTwoFAPreLoginData({
          email: account.email,
          phone: account.phone,
          loginData: account.loginData,
        }),
      )

      // redirect to the 2FA url
      yield* put(push('/2fa'))
      return
    }

    if (account.status === 'FEDERATED_TWO_FACTOR_AUTHENTICATION') {
      // save the 2FA contacts
      yield* put(
        receivedTwoFAPreLoginData({
          email: account.email,
          phone: account.phone,
          loginData: {
            type: 'federated',
            ...account.loginData,
          },
        }),
      )

      // redirect to the 2FA url
      yield* put(push('/2fa'))
      return
    }

    if (account.status === 'SUCCEEDED') {
      const { user, timeZones, settings, diagnosticStatus, accessToken } = account

      logUserDeviceMetadataToAnalytics()

      let newRefreshTokenTimeoutId: number | null = null
      try {
        const { timeoutId } = yield* setupDelayedTaskForRefreshToken({
          accessToken,
          storeDispatch,
        })
        newRefreshTokenTimeoutId = timeoutId
      } catch (error) {
        logException(error, {
          monitorTool: 'both',
          additionalProps: { context: '[Cartrack] - JWT login authentication failed' },
        })
        // Since JWT auth is still not the main way for authentication, we need to proceed like nothing happened in case something fails on accessToken is not defined (even though it should)
        newRefreshTokenTimeoutId = null
      }

      // Pick up user's preferences from local storage
      let preferences = localStorage.getItem('userPreferences' + user.id)
      preferences = preferences ? JSON.parse(preferences) : null

      // Store data to persist over reloads
      const sUser = JSON.stringify(user)
      const sSettings = JSON.stringify(settings)
      const sTimeZones = JSON.stringify(timeZones)
      const sPreferences = JSON.stringify(preferences)
      const sDiagnosticStatus = JSON.stringify(diagnosticStatus)

      // Add to session whatever needs to be synced between opened browser tabs
      sessionStorage.setItem('diagnosticStatus', sDiagnosticStatus)
      sessionStorage.setItem('userTimezones', sTimeZones)
      sessionStorage.setItem('userSettings', sSettings)

      sessionStorage.setItem('userPreferences', sPreferences)

      // Save user settings and preferences regardless if 'Remember me' is on or off to save it across tabs
      localStorage.setItem('user', sUser)
      localStorage.setItem('diagnosticStatus', sDiagnosticStatus)
      localStorage.setItem('userTimezones', sTimeZones)
      localStorage.setItem('userSettings', sSettings)
      localStorage.setItem('userPreferences' + user.id, sPreferences)
      if (payload.type === 'credentials_login') {
        localStorage.setItem('loginSelectedTab', payload.adminLogin)
      }

      yield* put(
        batchReduxStateUpdatesFromSaga({
          userState: {
            // Make token available before calling mifleet API
            jwtAccessToken: isNonEmptyTrimmedStringAndNotFalseish(accessToken)
              ? accessToken
              : MOCK_ACCESS_TOKEN,
          },
          mapState: {
            zoom: (settings.defaultMapZoom as number | null) || 1,
          },
        }),
      )

      if (settings.costs) {
        yield fetchMifleetAuthMetaData({ settings })
      }

      const hasSystemStateMessage = yield* call(fetchSystemStateMessage)
      if (!hasSystemStateMessage) yield* fork(maybeDisplayResolutionWarning)

      /* ------- Analytics ------- */

      const countryCode = selectState(getISO3166Alpha2CountryCode)
      const globalUniqueUserId = createAnalyticsGlobalUniqueUserId({
        user,
        countryCode,
      })

      GA4.setUserInfo({
        userId: globalUniqueUserId,
      })
      GA4.event({
        category: 'User',
        action: 'User Logged In Successfully',
      })

      maybeRegisterPosthogUser({ user, countryCode })

      if (ENV.NODE_ENV === 'production') {
        Sentry.getCurrentScope().setUser({
          username: user.username,
          id: globalUniqueUserId,
          email: user.primaryEmail,
          country: countryCode,
        })
        Sentry.getCurrentScope().setTag('company_name', user.companyName)
      }
      /* ------- Analytics ------- */

      yield* call(maybeSetupUserHubConnectionOrFallbackToPolling, {
        initialAccessToken: accessToken,
        userSettings: settings,
        initialSubscribedTerminalSerials: 'not_available_yet',
      })

      yield* put(
        loginSucceeded({
          loginMethodType: payload.type,
          apiData: account,
          preferences,
          debug: payload.type === 'otp',
          vehicleIdToSelectOnMap:
            'vehicleIdToSelectOnMap' in payload
              ? payload.vehicleIdToSelectOnMap
              : undefined,
          refreshTokenTimeoutId: newRefreshTokenTimeoutId,
        }),
      )

      // loginSucceeded action was dispatched, so we can safely assume settings are available
      maybeInitializeMediaSegmentProcessorSingletonAfterUserSettingsAreAvailable()

      if (account.status2FA === 'ACTIVATED_BUT_EMPTY') {
        enqueueSnackbarWithButtonAction({
          message: ctIntl.formatMessage({ id: 'login.2fa.activatedButEmpty' }),
          snackBarOptions: {
            variant: 'warning',
            persist: true,
            anchorOrigin: { horizontal: 'center', vertical: 'top' },
          },
          buttonText: ctIntl.formatMessage({ id: 'Add Contact' }),
          buttonAction: () => {
            history.replace(SETTINGS.subMenusRoutes.PROFILE_SETTINGS.path)
          },
        })
      }

      const appMainUrl = yield* select(getAppMainUrl)
      yield* put(replace(appMainUrl))
      return
    }

    yield* put(loginFailed(account))
  } catch (error) {
    GA4.event({
      category: 'User',
      action: 'User Login Failed',
    })
    yield* put(loginFailed({ unexpectedError: error.message }))

    throw error
  }
}

function* federatedLoginSaga(
  injectedDeps: AppSagaInjectedDeps,
  action: ReturnType<typeof federatedLogin>,
) {
  try {
    yield* call(loginSaga, injectedDeps, action)
  } catch (error) {
    GA4.event({
      category: 'User',
      action: 'Federated Login Failed',
    })
    yield* put(replace('/login'))

    throw error
  }
}

function* subUserLoginSaga(action: ReturnType<typeof subUserLogin>) {
  const { userId, name } = action.payload

  const account = yield* call(userAPI.subUserLogin, userId)

  yield* put(
    login({
      type: 'sso',
      sso: name,
      t: account,
    }),
  )
}

function* tokenLoginSaga({
  payload: { userId, vehicleId, token, clientId, getSharedVehicleData },
}: ReturnType<typeof tokenLogin>) {
  const currentUser = yield* select(getUser)

  /**
   * ? TODO: Fix redux persist not persisting full user object
   * [FABF-797] Fixes refresh on share link requiring login
   * This fix is a workaround, it however does not resolve the root cause
   * of the issue. Further investigation is required to understand why Redux persist
   * returns a user object with a single property from local/sessionStorage
   */
  if (
    currentUser &&
    Object.keys(currentUser).length > 1 &&
    !currentUser.isTokenAccount
  ) {
    const appMainUrl = yield* select(getAppMainUrl)
    yield* put(replace(appMainUrl))
    yield makeMessage(
      ctIntl.formatMessage({ id: 'Already Logged In' }),
      ctIntl.formatMessage({
        id: 'Please log out first to preview this shared vehicle link.',
      }),
      ctIntl.formatMessage({ id: 'Got it' }),
    )
    return
  }

  try {
    const { user, settings } = yield* call(userAPI.tokenLogin, {
      userId,
      vehicleId,
      token,
      clientId,
    })

    const jsonUser = JSON.stringify({ authToken: user.authToken })
    sessionStorage.setItem('user', jsonUser)

    yield* put(
      tokenLoginSucceeded({
        user,
        settings,
      }),
    )

    if (getSharedVehicleData) {
      yield* spawn(fetchSharedVehicleDataSaga, {
        type: '',
        payload: { vehicleId },
      })
    }
  } catch (error) {
    yield* put(replace('/login'))
    if (error.message === 'WRONG_CREDENTIALS') {
      throw new Error(
        ctIntl.formatMessage({
          id: 'login.token.invalid',
        }),
      )
    }
    throw error
  }
}

function* setUserPasswordSaga({
  payload: { currentPassword, password },
}: ReturnType<typeof setUserPassword>) {
  const response = yield* call(userAPI.setUserPassword, password, currentPassword)

  const isSuccess =
    response === 'Password update successful' ||
    response === 'Password changed. Please log in again'
  const shouldLogout = response === 'Password changed. Please log in again'

  yield* put({
    type: PASSWORD_UPDATE_STATUS,
    payload: {
      updatedTS: new Date(),
      isSuccess,
      response: isSuccess ? '' : response,
    },
  })
  if (isSuccess) yield* call(makeToast, 'success', response)
  if (shouldLogout) yield* putResolve(logout({}))
}

function* loggedOutFromAnotherTabSaga() {
  const user = yield* select(getUser)

  if (user?.isTokenAccount) {
    // ignore logged out from another tab for token accounts
    return
  }

  yield* put(logout({ meta: { logoutFromOtherOpenTabs: true } }))
}

function* logoutSaga(
  { payload }: Pick<ReturnType<typeof logout>, 'payload'>,
  retriesNum = 0,
): Generator<any, unknown, unknown> {
  const { meta = {} } = payload
  if (retriesNum > 2) {
    // Pointless to keep retrying a logout operation after a retry
    return
  }
  const { refreshTokenTimeoutId } = yield* select(getUserStateFromSaga)

  yield* put(startLogOut())

  const saveLocale = localStorage.getItem('locale')
  const saveloginSelectedTab = localStorage.getItem('loginSelectedTab')

  // Use localStorage to communicate logout to other tabs if this was a manual event
  if (!meta.logoutFromOtherOpenTabs) {
    // calling backend logout only in current tab to prevent conflict of fn cookie
    try {
      yield* call(userAPI.backendLogout)
      localStorage.setItem('logoutFromOtherOpenTabs', Date.now() as FixMeAny)
    } catch {
      yield* put(logoutFailed())
      return yield logoutSaga({ payload }, retriesNum + 1)
    }
  }

  resetPosthog()

  if (refreshTokenTimeoutId !== null) {
    // Clean up the refresh token when logout is successful
    window.clearTimeout(refreshTokenTimeoutId)
  }

  // Save existing user preferences
  const cachedUser = (JSON.parse as FixMeAny)(localStorage.getItem('user'))

  let cachedUserPreferencesFromReduxKey = null
  let cachedUserPreferencesFromRedux = null
  if (cachedUser) {
    cachedUserPreferencesFromReduxKey = 'userPreferences' + cachedUser.id
    cachedUserPreferencesFromRedux = localStorage.getItem(
      cachedUserPreferencesFromReduxKey,
    )
  }

  // Clear all persisted/stored data in the browser
  sessionStorage.clear()
  for (const key of Object.keys(localStorage)) {
    // Check if the key does NOT start with 'userPreferences'
    if (!key.startsWith('userPreferences')) {
      localStorage.removeItem(key)
    }
  }
  // API chache
  cache.clear()

  // Put back cached user preferences
  if (cachedUserPreferencesFromReduxKey && cachedUserPreferencesFromRedux) {
    localStorage.setItem(
      cachedUserPreferencesFromReduxKey,
      cachedUserPreferencesFromRedux,
    )
  }

  localStorage.setItem('locale', saveLocale as FixMeAny)
  localStorage.setItem('loginSelectedTab', saveloginSelectedTab as LoginTabOptionValue)

  for (const [, value] of idbStateKeysToDeleteOnLogout) {
    value.delete()
  }
  idbStateKeysToDeleteOnLogout.clear()

  //Clear all react query cache
  queryClient.removeQueries()

  cleanupMswWorkerAndFFmpeg()

  yield* put({ type: LOGGED_OUT })
  if (!meta.preventRedirect) {
    yield* put(replace('/login'))
  }
  return
}

function* updateUserPreferencesSaga({
  payload: { key, value },
}: ReturnType<typeof savePreferences>) {
  let preferences = yield* select((state: AppState) => state.user.preferences)
  const user = yield* select((state: AppState) => state.user.user)
  if (user) {
    preferences = Object.assign(preferences, { [key]: value })
    localStorage.setItem('userPreferences' + user.id, JSON.stringify(preferences))
    sessionStorage.setItem('userPreferences', JSON.stringify(preferences))
  }
}

function* resetLeftPanelVehicleFiltersPreferencesSaga() {
  const { ...preferences } = yield* select((state: AppState) => state.user.preferences)
  const user = yield* select((state: AppState) => state.user.user)
  if (user) {
    const keysToReset = ['vehicleIconColors', 'geofenceColors', 'poiColors'] as const

    for (const key of keysToReset) {
      preferences[key] = defaultPreferences[key]
    }

    const mapState = yield* select(getMapStateFromSaga)
    yield* put(
      batchReduxStateUpdatesFromSaga({
        mapState: {
          activeFilters: { ...mapState.activeFilters, vehicles: {}, drivers: {} },
          carpoolActiveFilters: {
            ...mapState.carpoolActiveFilters,
            carpool: {},
          },
        },
        userState: { preferences },
      }),
    )

    localStorage.setItem('userPreferences' + user.id, JSON.stringify(preferences))
    sessionStorage.setItem('userPreferences', JSON.stringify(preferences))
  }
}

function* submitHelpRequestSaga(action: ReturnType<typeof submitHelpRequest>) {
  yield* call(userAPI.submitHelpRequest, action.payload.request)
  yield* call(makeToast, 'success', 'Message sent!')
}

export function* maybeConnectLoggedInUserToHubWhenTokenIsAvailableSaga() {
  const vehicles = yield* select(getVehicles)

  yield* waitForValidJwtAccessToken()

  // Token is now available
  const accessToken = yield* select(getJwtAccessToken)
  if (!accessToken) {
    return // extra check to make ts happy
  }

  yield* call(maybeSetupUserHubConnectionOrFallbackToPolling, {
    initialSubscribedTerminalSerials: vehicles.map((v) => v.terminalSerial),
    initialAccessToken: accessToken,
    userSettings: yield* select(getSettings),
  })
}

function* maybeFetchPreLoginData({
  payload: { history },
}: ReturnType<typeof fetchPreLoginData>) {
  const preLoginQuery = yield* select(getPreLoginQuery)
  const isAlreadyFetching = preLoginQuery.fetchStatus === 'fetching'
  if (!isAlreadyFetching) {
    try {
      yield* put(startedPreLoginFetch())

      const {
        ctCountries,
        languages,
        federatedLogins,
        settings: rawPreLoginSettings,
        countriesWebsites,
      } = yield* call(userAPI.fetchPreLoginData)

      // Check if is first time fetching prelogin data
      if (preLoginQuery.status === 'pending' && ENV.NODE_ENV === 'production') {
        // Init Sentry to log errors (only in production)
        Sentry.init({
          enabled: !rawPreLoginSettings.disableThirdPartyLogging,
          dsn: ENV.SENTRY_DSN,
          release: isNilOrEmptyTrimmedStringOrFalseish(ENV.APP_VERSION_WITH_METADATA)
            ? ENV.APP_VERSION
            : ENV.APP_VERSION_WITH_METADATA,
          environment: ENV.DEPLOYMENT_ENV,
          ignoreErrors: errorMonitoringToolIgnoredErrors,
          integrations: [Sentry.captureConsoleIntegration({ levels: ['error'] })],
        })

        if (!rawPreLoginSettings.disableThirdPartyLogging) {
          initPosthog()
        }

        if (
          rawPreLoginSettings.disableThirdPartyLogging &&
          // https://developers.google.com/analytics/devguides/collection/gtagjs/user-opt-out
          // Disables Google Analytics if the user has opted out
          GA4.tagId
        ) {
          ;(window as any)[`ga-disable-${GA4.tagId}`] = true
        }
        // Initialize google analytics
        GA4.initialize()
      }

      const userAuthenticatedExists = !isNil(yield* select(getUser))
      const currentKarooUiTheme = yield* select(getKarooUiTheme)

      // This can happen when we refresh the page while authenticated
      if (userAuthenticatedExists) {
        yield* fork(maybeConnectLoggedInUserToHubWhenTokenIsAvailableSaga)
        logUserDeviceMetadataToAnalytics()

        // Settings should be available, since there is a user authenticated
        maybeInitializeMediaSegmentProcessorSingletonAfterUserSettingsAreAvailable()
      }

      const preLoginSettings = (() => {
        const { styleProperties, ...remainingPreLoginSettings } = rawPreLoginSettings

        // When fetchPreLoginData is called AND the user is already authenticated (can happen when we refresh the page while authenticated, for instance), we don't want to override the user theme
        const karooUiTheme: ParsedKarooUiCustomizableTheme =
          userAuthenticatedExists && currentKarooUiTheme !== undefined
            ? currentKarooUiTheme
            : styleProperties.karooUiTheme

        return {
          ...remainingPreLoginSettings,
          styleProperties: { ...styleProperties, karooUiTheme },
        }
      })()

      const currentSettings =
        JSON.parse(sessionStorage.getItem('userSettings') as FixMeAny) || {}

      sessionStorage.setItem(
        'userSettings',
        JSON.stringify({ ...currentSettings, ...preLoginSettings }),
      )

      const defaultLocale = languages.options.find((o) => o.default)?.value ?? 'en-ZA'
      const fromLocal = localStorage.getItem('locale')
      const locale =
        fromLocal && languages.options.some((o) => o.value === fromLocal) // verify if the local locale is included in the language list
          ? (fromLocal as (typeof languages.options)[number]['value'])
          : defaultLocale

      yield* put(
        receivePreLoginData({
          languageList: languages,
          styleProperties: preLoginSettings.styleProperties,
          locale,
          ctCountries,
          federatedLogins,
          countriesWebsites,
        }),
      )

      yield* put({
        type: RECEIVE_USER_SETTINGS,
        payload: { settings: preLoginSettings },
      })

      const enableWelcomePageSetting = yield* select(getEnableWelcomePageSetting)

      if (localStorage.getItem('userAccessedTheSiteAtLeastOnce') === null) {
        localStorage.setItem('userAccessedTheSiteAtLeastOnce', 'true')

        const { location } = history
        if (
          enableWelcomePageSetting &&
          !preLoginSettings.useFederationLoginOnly &&
          location.pathname === '/' &&
          location.search === ''
        ) {
          history.replace('/welcome')
        }
      }
    } catch {
      yield* put(failedPreLoginFetch())
    }
  }
}

function* submitThirdPartyLoginSaga({
  payload: { vehicle },
}: ReturnType<typeof submittedThirdPartyLogin>) {
  const user = yield* select(getThirdPartyUser)
  if (user === null) {
    return
  }

  const payload = {
    ...user.loginPayloadData,
    vehicle,
  }

  yield* put(login(payload))
}

function* handleSaveProfileSuccessSaga() {
  const oldSettings = yield* select(getSettings_UNSAFE)
  const newSettings = yield* call(userAPI.fetchUserSettings)
  const settings = { ...oldSettings, ...newSettings }
  sessionStorage.setItem('userSettings', JSON.stringify(settings))
  yield* put({ type: ON_SAVE_PROFILE, payload: { settings } })
}

function* onUrlLoginSearchParamsChangeSaga(
  injectedDeps: AppSagaInjectedDeps,
  { payload: payload_ }: ReturnType<typeof onUrlLoginSearchParamsChange>,
) {
  const loginPayload = match(payload_)
    .returnType<ReturnType<typeof login>['payload']>()
    .with({ otp: P.nonNullable }, (payload) => ({
      type: 'otp',
      otp: payload.otp,
      username: payload.account,
      vehicleIdToSelectOnMap: payload.ot_vehicle_id,
    }))
    .with({ sso: P.nonNullable }, (payload) => ({
      type: 'sso',
      sso: payload.sso,
      t: payload.t,
    }))
    .with({ wresult: P.nonNullable }, ({ wresult }) => ({
      type: 'federated',
      federatedResponse: buildQueryStringFromObject({
        wresult,
      }),
    }))
    .with({ code: P.nonNullable }, ({ code, state }) => ({
      type: 'federated',
      federatedResponse: buildQueryStringFromObject({
        code,
        state,
      }),
    }))
    .exhaustive()

  if (loginPayload.type === 'federated') {
    yield* put(federatedLogin(loginPayload))
    return
  }

  // This saga logs out first if needed
  yield loginSaga(injectedDeps, { payload: loginPayload })
}

function* setupDelayedTaskForRefreshToken({
  accessToken,
  storeDispatch,
}: {
  accessToken: string
  storeDispatch: AppSagaInjectedDeps['storeDispatch']
}) {
  const { refreshTokenTimeoutId } = yield* select(getUserStateFromSaga)
  if (refreshTokenTimeoutId !== null) {
    // Make sure we __always__ clear the previous timeout before setting a new one
    window.clearTimeout(refreshTokenTimeoutId)
  }

  const { exp: expUnixInSeconds } = jwtDecode(accessToken)
  let timeoutId: number | null = null
  if (expUnixInSeconds) {
    const expUnixInMs = expUnixInSeconds * 1000
    const timeToExpireInMs = expUnixInMs - Date.now()
    if (timeToExpireInMs < 0) {
      throw new Error('JWT token expired')
    }

    // Subtract some seconds to make sure we refresh the token before it expires
    // This is important because jsonrpc runs one endpoint at a time. If vehiclelist_v3, for instance, takes too long to respond, it might delay the refresh_token call
    // And we'd have a period of time where the token would be expired (which should not be possible).
    const timeoutTimeInMs = timeToExpireInMs - secondsToMs(60)
    timeoutId = window.setTimeout(() => {
      storeDispatch(triggerRefreshJWTAccessToken())
    }, timeoutTimeInMs)

    return { timeoutId: timeoutId }
  }

  return { timeoutId: null }
}

function* refreshJWTAccessTokenSaga({ storeDispatch }: AppSagaInjectedDeps) {
  const { refreshTokenTimeoutId } = yield* select(getUserStateFromSaga)
  try {
    if (refreshTokenTimeoutId) {
      // Clear the timeout so that it doesn't refresh the token while we're refreshing manually, for instance
      window.clearTimeout(refreshTokenTimeoutId)
    }

    yield* put(
      batchReduxStateUpdatesFromSaga({
        userState: {
          refreshJwtMutation: { status: 'pending' },
        },
      }),
    )

    /** refresh_token is included on an https cookie that this endpoint reads from */
    const { accessToken } = yield* call(userAPI.refreshJwtToken)
    const { timeoutId } = yield* setupDelayedTaskForRefreshToken({
      accessToken,
      storeDispatch,
    })

    yield* put(
      batchReduxStateUpdatesFromSaga({
        userState: {
          refreshJwtMutation: { status: 'success' },
          jwtAccessToken: accessToken,
          refreshTokenTimeoutId: timeoutId,
        },
      }),
    )
  } catch {
    yield* put(
      batchReduxStateUpdatesFromSaga({
        userState: {
          refreshJwtMutation: { status: 'error' },
        },
      }),
    )
  }
}

function* onAppMountedSaga(injectedDeps: AppSagaInjectedDeps) {
  const user = yield* select(getUser)
  if (!user) {
    // No need to refresh token if user is not logged in
    return
  }
  const appMainUrl = yield* select(getAppMainUrl)
  if (appMainUrl === APP_MAIN_URL_NOT_AVAILABLE_VALUE) {
    /**
     * This can happen sometimes, in very weird states, if all required permissions are gone (settings are in a messed up state without any permissions) and we could not calculate the main url.
     * EVEN THOUGH the user is still "logged in", we cannot proceed with showing anything to the user.
     *
     * CAN BE MANUALLY REPRODUCED by logging in, then clearing "userSettings" and "persist:userSettings" from localStorage and then refreshing the page.
     * Of course the user will never do this but it was the only way that I could reproduce the problem in order to fix it.
     */
    yield logoutSaga({ payload: {} })
    return
  }

  // If the user refreshes the page for instance or opens it in a new tab, the token will be refreshed (if they are still logged in)
  yield refreshJWTAccessTokenSaga(injectedDeps)
}

function* onUpdateVehicleLivePositionSuccessSaga({
  payload,
}: ReturnType<typeof onUpdateVehicleLivePositionSuccess>) {
  const vehicles = yield* select(getVehicles)
  const {
    currentVehicleLivePositionStrategy,
    settings: prevSettings,
    jwtAccessToken,
  } = yield* select(getUserStateFromSaga)

  const newSettings = {
    ...prevSettings,
    privacyHideLocationsFromDay: payload.newPrivacyHideLocationsFromDay,
  }
  yield* put(
    batchReduxStateUpdatesFromSaga({
      userState: { settings: newSettings },
    }),
  )

  yield* put({ type: TRY_REFETCH_VEHICLES_POSITIONS })

  const newShowLivePositions = getShowLivePositionsStoreless(newSettings)
  if (!newShowLivePositions) {
    // Always revert to polling if live positions are disabled because some things like clusters rely on polling, even when live positions are disabled
    yield* put(
      batchReduxStateUpdatesFromSaga({
        userState: {
          currentVehicleLivePositionStrategy: createVehicleLivePositionStrategy({
            currentValue: currentVehicleLivePositionStrategy,
            newValue: { type: 'polling_or_idle' },
          }),
        },
      }),
    )
    return
  }

  // Show live position is enabled now

  if (currentVehicleLivePositionStrategy.type !== 'signalr') {
    // If we didn't have a connection yet, try to create one if every requirement is met
    yield* call(maybeSetupUserHubConnectionOrFallbackToPolling, {
      initialAccessToken: jwtAccessToken,
      initialSubscribedTerminalSerials: vehicles.map((v) => v.terminalSerial),
      userSettings: newSettings,
    })
    return
  }

  const terminalSerials = vehicles.map((v) => v.terminalSerial)
  if (!isNonEmptyArray(terminalSerials)) {
    // If our vehicle list is empty, we can't connect to the stream
    return
  }
  // We have a signalr connection already but we need to create a new stream subscription because it was most likely turned off
  const { streamSubscription } = createSingletonDevicesPositionsStream({
    hubConnection: currentVehicleLivePositionStrategy.hubConnection,
    terminalSerials,
  })

  yield* put(
    batchReduxStateUpdatesFromSaga({
      userState: {
        currentVehicleLivePositionStrategy: {
          ...currentVehicleLivePositionStrategy,
          devicePositionStreamSubscription: streamSubscription,
        },
      },
    }),
  )
}

function* setKeyIdbStateWithReduxSyncSaga({
  payload: { key, store, setStateAction },
}: ReturnType<typeof setKeyIdbStateWithReduxSync>) {
  async function setValueOnIdb(newValue: unknown) {
    try {
      await updateIdb(key, () => newValue, store)
    } catch {
      return
    }
  }
  const currentValue = yield* select((state: AppState) => getIdbStateForKey(state, key))
  const newValue = setStateAction(currentValue)
  setValueOnIdb(newValue)
  yield* put(setKeyIdbStateOnRedux({ key, value: newValue }))
}

function* onSetNewPlatformAnnouncementSeenMutationStartedSaga() {
  const userState = yield* select(getUserStateFromSaga)

  yield* put(
    batchReduxStateUpdatesFromSaga({
      userState: {
        settings: {
          ...userState.settings,
          hasNewPlatformAnnouncementBeenSeen: true,
        },
      },
    }),
  )
}

function* onSetVisionPromotionalModalSeenMutationStartedSaga() {
  const userState = yield* select(getUserStateFromSaga)

  yield* put(
    batchReduxStateUpdatesFromSaga({
      userState: {
        settings: {
          ...userState.settings,
          hasVisionPromotionalModalBeenSeen: true,
        },
      },
    }),
  )
}

function* onSetInvoicingUpdateModalSeenMutationStartedSaga() {
  const userState = yield* select(getUserStateFromSaga)

  yield* put(
    batchReduxStateUpdatesFromSaga({
      userState: {
        settings: {
          ...userState.settings,
          showInvoiceUpdateModal: false,
        },
      },
    }),
  )
}

export default function* userSaga(injectedDeps: AppSagaInjectedDeps) {
  yield* takeLeading(LOG_IN, createLoadableSaga(loginSaga), injectedDeps)
  yield* takeLeading(
    FEDERATED_LOG_IN,
    createLoadableSaga(federatedLoginSaga),
    injectedDeps,
  )
  yield* takeLatest(submittedThirdPartyLogin, makeLoadable, submitThirdPartyLoginSaga)
  yield* takeLatest(TOKEN_LOG_IN, tokenLoginSaga)
  yield* takeLeading(SUB_USER_LOGIN, makeLoadable, subUserLoginSaga)
  yield* takeLeading(logout, makeLoadable, logoutSaga)
  yield* takeLeading(loggedOutFromAnotherTab, loggedOutFromAnotherTabSaga)
  yield* takeLatest(SAVE_PREFERENCE, updateUserPreferencesSaga)
  yield* takeLatest(
    onLeftPanelResetAllVehicleFiltersButtonClick,
    resetLeftPanelVehicleFiltersPreferencesSaga,
  )
  yield* takeLatest(SUBMIT_HELP_REQUEST, submitHelpRequestSaga)
  yield* takeLeading(fetchPreLoginData, createLoadableSaga(maybeFetchPreLoginData))
  yield* takeLatest(TOGGLE_SSO_HASH, toggleSsoHashSaga)
  yield* takeLatest(SUBMIT_CLEAR_FUEL, UserClearDataSaga)
  yield* takeLatest(SET_USER_PASSWORD, setUserPasswordSaga)
  yield* takeLatest(
    handleSaveProfileSuccess,
    makeLoadable,
    handleSaveProfileSuccessSaga,
  )
  yield* takeLatest(
    onUrlLoginSearchParamsChange,
    onUrlLoginSearchParamsChangeSaga,
    injectedDeps,
  )
  yield* takeLeading(onAppMounted, onAppMountedSaga, injectedDeps)
  yield* takeLeading(
    triggerRefreshJWTAccessToken,
    refreshJWTAccessTokenSaga,
    injectedDeps,
  )

  yield* takeLatest(
    onUpdateVehicleLivePositionSuccess,
    onUpdateVehicleLivePositionSuccessSaga,
  )
  yield* takeLatest(setKeyIdbStateWithReduxSync, setKeyIdbStateWithReduxSyncSaga)
  yield* takeLatest(
    onSetNewPlatformAnnouncementSeenMutationStarted,
    onSetNewPlatformAnnouncementSeenMutationStartedSaga,
  )
  yield* takeLatest(
    onSetVisionPromotionalModalSeenMutationStarted,
    onSetVisionPromotionalModalSeenMutationStartedSaga,
  )
  yield* takeLatest(
    onSetInvoicingUpdateModalSeenMutationStarted,
    onSetInvoicingUpdateModalSeenMutationStartedSaga,
  )
}

const logUserDeviceMetadataToAnalytics = () => {
  const category = 'user_device_metadata'
  GA4.event({
    category,
    action: 'canPlayHEVCUsingMediaSource',
    metaData: canPlayHEVCUsingMediaSourceResult,
  })
  GA4.event({
    category,
    action: 'hardwareAccelerationSupportInfo',
    metaData: hardwareAccelerationSupportInfo.status,
  })
}

async function maybeInitializeMediaSegmentProcessorSingletonAfterUserSettingsAreAvailable() {
  // Vision setting should be available, since there is a user authenticated
  const visionSetting = selectState(getVisionSetting)
  const liveStreamingStrategy = selectState(getVisionLiveStreamingStrategy)
  const noNativeH265Support =
    hardwareAccelerationSupportInfo.status !== 'likely_supported' ||
    canPlayHEVCUsingMediaSourceResult !== 'supported'

  if (visionSetting && liveStreamingStrategy === 'hls' && noNativeH265Support) {
    if (mswWorker.listHandlers().includes(hlsMSWHandler) === false) {
      mswWorker.use(hlsMSWHandler)
    }
    // Only start MSW worker when needed to intercept HLS requests and transcode them to H264
    await mswWorker.start({
      serviceWorker: { url: '/mockServiceWorker.js' },
      quiet: true,
      onUnhandledRequest: 'bypass',
    })

    // Non-blocking warm-up;
    // ffmpeg wasm will be loaded and that is a large wasm
    FFmpegManagerSingleton.initialize().catch((error) => {
      console.error('[Cartrack] - FFmpeg pre-warm failed (non-fatal)', { error })
    })
  }
}

function cleanupMswWorkerAndFFmpeg() {
  mswWorker.stop()
  FFmpegManagerSingleton.cleanup()
}
