import { useMemo } from 'react'
import { Button, IconButton } from '@karoo-ui/core'
import CloseIcon from '@mui/icons-material/Close'
import {
  closeSnackbar,
  enqueueSnackbar,
  type OptionsObject,
  type OptionsWithExtraProps,
  type SnackbarMessage,
  type VariantType,
} from 'notistack'
import type { Except, RequireAtLeastOne } from 'type-fest'

import type { ExcludeStrict } from 'src/types/utils'

export type SnackbarVariantsExceptCustom = ExcludeStrict<
  VariantType,
  'backgroundImportProgress'
>

export const enqueueSnackbarWithCloseAction = (
  message: SnackbarMessage,
  options: RequireAtLeastOne<
    OptionsWithExtraProps<SnackbarVariantsExceptCustom>,
    'variant'
  >,
) =>
  enqueueSnackbar(message, {
    action: (snackbarId) => (
      <>
        <IconButton
          size="small"
          onClick={() => closeSnackbar(snackbarId)}
        >
          <CloseIcon
            fontSize="small"
            sx={{ color: options.variant === 'light' ? 'black' : 'white' }}
          />
        </IconButton>
      </>
    ),
    ...options,
  })

export const enqueueErrorSnackbarWithCloseAction = (
  message: SnackbarMessage,
  options: Except<OptionsWithExtraProps<SnackbarVariantsExceptCustom>, 'variant'>,
) =>
  enqueueSnackbar(message, {
    ...options,
    style: {
      ...options.style,
      maxWidth: 400,
      display: 'flex',
      flexWrap: 'nowrap',
    },
    variant: 'error',
    action: (snackbarId) => (
      <>
        <IconButton
          size="small"
          onClick={() => closeSnackbar(snackbarId)}
        >
          <CloseIcon
            fontSize="small"
            sx={{ color: 'white' }}
          />
        </IconButton>
      </>
    ),
  })

export type EnqueueSnackbarWithCloseAction = typeof enqueueSnackbarWithCloseAction

export const enqueueSnackbarWithButtonAction = ({
  message,
  snackBarOptions,
  buttonAction,
  buttonText,
}: {
  message: SnackbarMessage
  snackBarOptions: RequireAtLeastOne<
    OptionsObject<SnackbarVariantsExceptCustom>,
    'variant'
  >
  buttonText: string
  buttonAction: () => void
}) =>
  enqueueSnackbar(message, {
    action: (snackbarId) => (
      <>
        <Button
          variant="text"
          color="inherit"
          onClick={() => {
            buttonAction()
            closeSnackbar(snackbarId)
          }}
        >
          {buttonText}
        </Button>
        <IconButton
          size="small"
          onClick={() => closeSnackbar(snackbarId)}
        >
          <CloseIcon
            fontSize="small"
            sx={{ color: 'white' }}
          />
        </IconButton>
      </>
    ),
    ...snackBarOptions,
  })

/**
 * @deprecated Import `enqueueSnackbarWithCloseAction` directly
 */
export const useSnackbarWithCloseAction = () =>
  useMemo(
    () => ({
      enqueueSnackbarWithCloseAction,
    }),
    [],
  )

export type UseSnackbarWithCloseActionReturnType = ReturnType<
  typeof useSnackbarWithCloseAction
>
