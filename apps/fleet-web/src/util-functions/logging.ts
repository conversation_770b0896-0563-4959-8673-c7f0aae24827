import { LRUCache } from '@karoo/utils'
import type { Jsonifiable } from 'type-fest'

export type DebugLoggerAllowList = Array<'debug' | 'error' | 'warn'> | 'all'

const logOnceCache = new LRUCache<string, boolean>(700)

export function createDebugLogger(
  prefix: string,
  allowList: DebugLoggerAllowList = 'all',
) {
  const runOrIgnore = ({
    type,
    args,
    once,
    fn,
  }: {
    type: 'debug' | 'error' | 'warn'
    args: Array<Jsonifiable>
    once: boolean | undefined
    fn: () => void
  }) => {
    if (once) {
      const key = `${prefix}-${type}-${JSON.stringify(args)}`
      if (logOnceCache.has(key)) {
        return
      }
      logOnceCache.set(key, true)
    }
    if (allowList === 'all' || allowList.includes(type)) {
      fn()
    }
  }
  return {
    debug<Once extends boolean>(
      args: Array<Once extends true ? Jsonifiable : any>,
      { once }: { once?: Once } = {},
    ) {
      runOrIgnore({
        type: 'debug',
        args,
        once,
        fn: () => {
          // eslint-disable-next-line no-console
          console.log(`${prefix}`, ...args)
        },
      })
    },
    error<Once extends boolean>(
      args: Array<Once extends true ? Jsonifiable : any>,
      { once }: { once?: Once } = {},
    ) {
      runOrIgnore({
        type: 'error',
        args,
        once,
        fn: () => {
          // eslint-disable-next-line no-console
          console.error(`${prefix}`, ...args)
        },
      })
    },
    warn<Once extends boolean>(
      args: Array<Once extends true ? Jsonifiable : any>,
      { once }: { once?: Once } = {},
    ) {
      runOrIgnore({
        type: 'warn',
        args,
        once,
        fn: () => {
          // eslint-disable-next-line no-console
          console.warn(`${prefix}`, ...args)
        },
      })
    },
  }
}
