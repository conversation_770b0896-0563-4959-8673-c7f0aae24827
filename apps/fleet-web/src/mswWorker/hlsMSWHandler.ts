import { bypass, HttpResponse, http } from 'msw'
import { initSegmentProcessor } from 'src/modules/vision/VisionLiveStream/transcoding/initSegmentProcessor'
import { ManifestProcessorSingleton } from 'src/modules/vision/VisionLiveStream/transcoding/ManifestProcessorSingleton'
import { MediaSegmentProcessorSingleton } from 'src/modules/vision/VisionLiveStream/transcoding/MediaSegmentProcessorSingleton'
import { createVisionLiveStreamDebugLogger } from 'src/modules/vision/VisionLiveStream/utils'

// Always enable HLS interception, but only modify/transcode when needed
const isM3U8 = (url: string) => /\.m3u8(\?|$)/i.test(url)
const isInitSeg = (url: string) => /(init|map)\.(mp4|m4s)(\?|$)/i.test(url)
const isFmp4Seg = (url: string) => /\.m4s(\?|$)/i.test(url)
const isMp4Seg = (url: string) => /\.mp4(\?|$)/i.test(url)

// In-memory cache for RAW init segments per track key (used only for H.265 demux)
const initCache = new Map<string, ArrayBuffer>()

const deriveStreamKey = (url: string): string => {
  // Extract basename like BC003280_1_ from .../BC003280_1_init.mp4 or .../BC003280_1_123.mp4
  const m = url.match(/\/([^/]+?)_(?:init|map|\d+)\.(?:mp4|m4s)(?:\?|$)/i)
  return m ? m[1] : url
}

// We no longer parse MPEG-TS; TS segments are bypassed and assumed H.264 by BE contract.

// Singleflight + short-lived cache to avoid duplicate work on the same segment
const inflightSegments = new Map<string, Promise<ArrayBuffer>>()
const recentSegmentCache = new Map<string, { data: ArrayBuffer; expireAt: number }>()
const RECENT_CACHE_TTL_MS = 15000

const logger = createVisionLiveStreamDebugLogger('[MSW][HLS]')

let intervalId: number | null = null

const setIntervalForCleanupOnce = () => {
  if (intervalId) {
    return
  }
  intervalId = window.setInterval(() => {
    cleanup()
  }, 15_000)
}
const cleanup = () => {
  const logPrefix = '[cleanup]'
  logger.debug([logPrefix, { size: recentSegmentCache.size }])
  const now = Date.now()
  try {
    // It is safe to delete elements from a Map while iterating with for...of over .entries().
    // However, to avoid any confusion and to ensure compatibility, you can collect keys to delete first, then delete them after iteration.
    const expiredKeys: Array<string> = []
    for (const [key, value] of recentSegmentCache.entries()) {
      if (value.expireAt < now) {
        expiredKeys.push(key)
        logger.debug([logPrefix, 'recentSegmentCache expired', { key }])
      }
    }
    for (const key of expiredKeys) {
      recentSegmentCache.delete(key)
    }

    logger.debug([logPrefix, 'recentSegmentCache', { size: recentSegmentCache.size }])
  } catch (error) {
    logger.error([logPrefix, { error }])
  }
}

// Unified handler for HLS: manifest, init segment, and media segments
export const hlsMSWHandler = http.get(
  // Takes into account urls with search params too.
  // We intentionally ignore TS here since only an H.264 stream is supported. And we don't care about transcoding h264 streams.
  /\.(m3u8|mp4|m4s)(\?.*)?$/i,
  async ({ request }) => {
    const url = request.url
    setIntervalForCleanupOnce()

    // Manifest rewrite (best-effort). If we cannot read due to CORS/cert, passthrough.
    if (isM3U8(url)) {
      try {
        const res = await fetch(bypass(request), { signal: request.signal })
        if (!res.ok || res.type === 'opaque') {
          logger.warn([
            'Manifest passthrough due to unreadable response',
            {
              url,
              status: res.status,
              type: res.type,
            },
          ])
          return res
        }

        const originalText = await res.text()
        logger.debug(['🎬 PROCESSING MANIFEST from', url])
        const processedRaw = ManifestProcessorSingleton.processManifest(originalText)
        return new HttpResponse(processedRaw, {
          status: 200,
          headers: res.headers,
        })
      } catch (err) {
        logger.error(['Manifest processing failed', { url, error: err }])
        return undefined
      }
    }

    // Init segment conversion
    if (isInitSeg(url)) {
      const res = await fetch(bypass(request), { signal: request.signal })
      const buf = await res.arrayBuffer()
      try {
        const processed = initSegmentProcessor.processInitSegment(buf, url)
        // Cache RAW HEVC init for transcoder (processed init is for HLS.js only)
        try {
          const key = deriveStreamKey(url)
          initCache.set(key, buf)
        } catch {
          logger.error(['Failed to cache init segment', { url }])
        }
        return new HttpResponse(processed, {
          status: 200,
          headers: res.headers,
        })
      } catch (error: any) {
        logger.error(['Init segment processing failed', { url, error }])
        return new HttpResponse(error?.message || 'Init segment processing failed', {
          status: 500,
        })
      }
    }

    // Media segment transcoding (synchronous; MVP reliability over speed)
    if (isFmp4Seg(url) || isMp4Seg(url)) {
      try {
        // Serve from short-lived cache if present
        const cached = recentSegmentCache.get(url)
        const now = Date.now()
        if (cached && cached.expireAt > now) {
          return new HttpResponse(cached.data, {
            status: 200,
            headers: {
              'content-type': 'video/mp4',
              'cache-control': 'no-store',
            },
          })
        }

        // Singleflight: if already processing this URL, await the same promise
        const urlSegment = inflightSegments.get(url)
        if (urlSegment !== undefined) {
          const data = await urlSegment
          return new HttpResponse(data, {
            status: 200,
            headers: {
              'content-type': 'video/mp4',
              'cache-control': 'no-store',
            },
          })
        }

        const work = (async (): Promise<ArrayBuffer> => {
          const res = await fetch(bypass(request), { signal: request.signal })
          if (!res.ok) {
            throw new Error(`Upstream fetch failed: ${res.status}`)
          }
          const buf = await res.arrayBuffer()

          // Prepend cached init for fMP4/mp4 so FFmpeg can demux
          let input = buf
          const key = deriveStreamKey(url)
          const cachedInit = initCache.get(key)
          if (cachedInit && cachedInit.byteLength > 0) {
            const combined = new Uint8Array(cachedInit.byteLength + buf.byteLength)
            combined.set(new Uint8Array(cachedInit), 0)
            combined.set(new Uint8Array(buf), cachedInit.byteLength)
            input = combined.buffer
          } else {
            logger.warn(['Missing init segment for fMP4 fragment; retrying later'])
          }

          const processed = await MediaSegmentProcessorSingleton.transcodeSegment(
            input,
            url,
            {
              outputQuality: 'ultrafast',
              maxResolution: '480p',
              preserveTiming: true,
              // Let fps pass through unless detected
              // targetFps: 30,
              // Let x264 auto thread
              encoderThreads: 0,
            },
          )
          return processed
        })()

        inflightSegments.set(url, work)
        try {
          const out = await work
          recentSegmentCache.set(url, {
            data: out,
            expireAt: Date.now() + RECENT_CACHE_TTL_MS,
          })
          return new HttpResponse(out, {
            status: 200,
            headers: {
              'content-type': 'video/mp4',
              'cache-control': 'no-store',
            },
          })
        } finally {
          inflightSegments.delete(url)
        }
      } catch (error: any) {
        logger.error(['Segment transcoding failed', { url, error }])
        return new HttpResponse(error?.message || 'Segment transcoding failed', {
          status: 500,
        })
      }
    }

    // Not HLS-related: pass through
    return
  },
)
