import type { MergeExclusive } from 'type-fest'

export type Option<T> = T | undefined | null

type DropdownOption<V = string> = {
  name: string
  value: V
}

type ActionStatus =
  | 'idle' // Has not processed once yet (e.g - has not fetched an endpoint yet)
  | 'processing' // Is now processing (e.g - waiting for endpoint response)
  | 'succeeded' // Can only go to 'processing' from here
  | 'failed' // Can only go to 'processing' from here

export type ValueOf<T> = T[keyof T]

export type KeysOfUnion<T> = T extends T ? keyof T : never

export type MergeMultipleExclusive<T extends Array<any>> = T extends [infer Only]
  ? Only
  : T extends [infer A, infer B, ...infer Rest]
    ? MergeMultipleExclusive<[MergeExclusive<A, B>, ...Rest]>
    : never

/** Validates T type against Shape type.
 * It must be an EXACT match (T must not have any other properties other than the ones that exist in Shape)
 */
type ValidateShape<T, Shape> = T extends Shape
  ? // eslint-disable-next-line @typescript-eslint/no-restricted-types
    Exclude<keyof T, keyof Shape> extends never
    ? T
    : never
  : never

export type PromiseType<T extends Promise<any>> = T extends Promise<infer U> ? U : never

export type PromiseResolvedType<
  FuncReturningPromise extends (...args: any) => Promise<any>,
> = PromiseType<ReturnType<FuncReturningPromise>>

/**
 * USE AS A LAST RESORT
 *
 * Alias for "any" to be used at an early Typescript migration stage when making types is too hard and/or time spending.
 * Later it will be easier to identify cases where we used this for a temporary fix and distinguish those cases from real "any" types
 */
export type FixMeAny = any

declare global {
  // eslint-disable-next-line @typescript-eslint/consistent-type-definitions
  interface Window {
    Sisense: FixMeAny
    prism: FixMeAny
    L: FixMeAny
    rucEnableDebug: boolean | undefined
    liveStreamPlayerDebug: boolean | 'warn_error' | undefined
    signalRConnectionDebug: boolean | undefined
  }
}

// Should only be used to type API output
export type BEBooleanShort = 't' | 'f' | null
export type BEBooleanLong = 'true' | 'false' | null
export type BEBoolean = BEBooleanShort | BEBooleanLong | boolean

/**
 * The BE sometimes changes some DB query while making a change to the API and something that was previously a number becomes a string, e.g. 1024 becomes "1024"
 * Since we are tired of having subtle bugs like that one, we decided to use this type to make it explicit that the BE might be sending us numbers as strings.
 */
export type BENumber = number | `${number}`

export type CardinalDirection = 'N' | 'NE' | 'E' | 'SE' | 'S' | 'SW' | 'W' | 'NW'

export type TripsDownloadFileExtension = 'xls' | 'gpx' | 'kml'

/**
 * https://en.wikipedia.org/wiki/ISO_3166-1_alpha-2#Officially_assigned_code_elements
 */
export type ISO3166_1Alpha2CountryCode =
  | 'AC'
  | 'AD'
  | 'AE'
  | 'AF'
  | 'AG'
  | 'AI'
  | 'AL'
  | 'AM'
  | 'AO'
  | 'AQ'
  | 'AR'
  | 'AS'
  | 'AT'
  | 'AU'
  | 'AW'
  | 'AX'
  | 'AZ'
  | 'BA'
  | 'BB'
  | 'BD'
  | 'BE'
  | 'BF'
  | 'BG'
  | 'BH'
  | 'BI'
  | 'BJ'
  | 'BL'
  | 'BM'
  | 'BN'
  | 'BO'
  | 'BQ'
  | 'BR'
  | 'BS'
  | 'BT'
  | 'BW'
  | 'BY'
  | 'BZ'
  | 'CA'
  | 'CC'
  | 'CD'
  | 'CF'
  | 'CG'
  | 'CH'
  | 'CI'
  | 'CK'
  | 'CL'
  | 'CM'
  | 'CN'
  | 'CO'
  | 'CR'
  | 'CU'
  | 'CV'
  | 'CW'
  | 'CX'
  | 'CY'
  | 'CZ'
  | 'DE'
  | 'DJ'
  | 'DK'
  | 'DM'
  | 'DO'
  | 'DZ'
  | 'EC'
  | 'EE'
  | 'EG'
  | 'EH'
  | 'ER'
  | 'ES'
  | 'ET'
  | 'FI'
  | 'FJ'
  | 'FK'
  | 'FM'
  | 'FO'
  | 'FR'
  | 'GA'
  | 'GB'
  | 'GD'
  | 'GE'
  | 'GF'
  | 'GG'
  | 'GH'
  | 'GI'
  | 'GL'
  | 'GM'
  | 'GN'
  | 'GP'
  | 'GQ'
  | 'GR'
  | 'GT'
  | 'GU'
  | 'GW'
  | 'GY'
  | 'HK'
  | 'HN'
  | 'HR'
  | 'HT'
  | 'HU'
  | 'ID'
  | 'IE'
  | 'IL'
  | 'IM'
  | 'IN'
  | 'IO'
  | 'IQ'
  | 'IR'
  | 'IS'
  | 'IT'
  | 'JE'
  | 'JM'
  | 'JO'
  | 'JP'
  | 'KE'
  | 'KG'
  | 'KH'
  | 'KI'
  | 'KM'
  | 'KN'
  | 'KP'
  | 'KR'
  | 'KW'
  | 'KY'
  | 'KZ'
  | 'LA'
  | 'LB'
  | 'LC'
  | 'LI'
  | 'LK'
  | 'LR'
  | 'LS'
  | 'LT'
  | 'LU'
  | 'LV'
  | 'LY'
  | 'MA'
  | 'MC'
  | 'MD'
  | 'ME'
  | 'MF'
  | 'MG'
  | 'MH'
  | 'MK'
  | 'ML'
  | 'MM'
  | 'MN'
  | 'MO'
  | 'MP'
  | 'MQ'
  | 'MR'
  | 'MS'
  | 'MT'
  | 'MU'
  | 'MV'
  | 'MW'
  | 'MX'
  | 'MY'
  | 'MZ'
  | 'NA'
  | 'NC'
  | 'NE'
  | 'NF'
  | 'NG'
  | 'NI'
  | 'NL'
  | 'NO'
  | 'NP'
  | 'NR'
  | 'NU'
  | 'NZ'
  | 'OM'
  | 'PA'
  | 'PE'
  | 'PF'
  | 'PG'
  | 'PH'
  | 'PK'
  | 'PL'
  | 'PM'
  | 'PR'
  | 'PS'
  | 'PT'
  | 'PW'
  | 'PY'
  | 'QA'
  | 'RE'
  | 'RO'
  | 'RS'
  | 'RU'
  | 'RW'
  | 'SA'
  | 'SB'
  | 'SC'
  | 'SD'
  | 'SE'
  | 'SG'
  | 'SH'
  | 'SI'
  | 'SJ'
  | 'SK'
  | 'SL'
  | 'SM'
  | 'SN'
  | 'SO'
  | 'SR'
  | 'SS'
  | 'ST'
  | 'SV'
  | 'SX'
  | 'SY'
  | 'SZ'
  | 'TA'
  | 'TC'
  | 'TD'
  | 'TG'
  | 'TH'
  | 'TJ'
  | 'TK'
  | 'TL'
  | 'TM'
  | 'TN'
  | 'TO'
  | 'TR'
  | 'TT'
  | 'TV'
  | 'TW'
  | 'TZ'
  | 'UA'
  | 'UG'
  | 'US'
  | 'UY'
  | 'UZ'
  | 'VA'
  | 'VC'
  | 'VE'
  | 'VG'
  | 'VI'
  | 'VN'
  | 'VU'
  | 'WF'
  | 'WS'
  | 'XK'
  | 'YE'
  | 'YT'
  | 'ZA'
  | 'ZM'
  | 'ZW'

// See https://www.techonthenet.com/js/language_tags.php and https://en.wikipedia.org/wiki/ISO_3166-1_alpha-2
export type BCP47LanguageTag =
  | 'ar-SA'
  | 'bn-BD'
  | 'bn-IN'
  | 'cs-CZ'
  | 'da-DK'
  | 'de-AT'
  | 'de-CH'
  | 'de-DE'
  | 'el-GR'
  | 'en-AU'
  | 'en-CA'
  | 'en-GB'
  | 'en-IE'
  | 'en-IN'
  | 'en-NZ'
  | 'en-SG'
  | 'en-US'
  | 'en-ZA'
  | 'es-AR'
  | 'es-CL'
  | 'es-CO'
  | 'es-ES'
  | 'es-MX'
  | 'es-US'
  | 'fi-FI'
  | 'fil'
  | 'fr-BE'
  | 'fr-CA'
  | 'fr-CH'
  | 'fr-FR'
  | 'he-IL'
  | 'hi-IN'
  | 'hu-HU'
  | 'id-ID'
  | 'it-CH'
  | 'it-IT'
  | 'ja-JP'
  | 'km'
  | 'ko-KR'
  | 'ms'
  | 'nl-BE'
  | 'nl-NL'
  | 'no-NO'
  | 'pl-PL'
  | 'pt-BR'
  | 'pt-MZ'
  | 'pt-PT'
  | 'ro-RO'
  | 'ru-RU'
  | 'sk-SK'
  | 'sv-SE'
  | 'th-TH'
  | 'tr-TR'
  | 'vi'
  | 'zh-CN'
  | 'zh-HK'
  | 'zh-TW'
