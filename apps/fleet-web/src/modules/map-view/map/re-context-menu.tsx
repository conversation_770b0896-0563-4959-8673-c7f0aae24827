import type * as React from 'react'
import AlbumOutlinedIcon from '@mui/icons-material/AlbumOutlined'
import DeleteOutlineOutlinedIcon from '@mui/icons-material/DeleteOutlineOutlined'
import InfoOutlinedIcon from '@mui/icons-material/InfoOutlined'
import LocationOnOutlinedIcon from '@mui/icons-material/LocationOnOutlined'
import { useDispatch } from 'react-redux'
import { useHistory } from 'react-router-dom'
import * as R from 'remeda'

import type { VehicleId } from 'api/types'
import { deleteGeofence } from 'duxs/geofences'
import { changeMapCenterZoom, getCenter, getZoom, setFocusedPoint } from 'duxs/map'
import { getCurrentLayerVisibility } from 'duxs/map-timeline'
import { getSettings_UNSAFE } from 'duxs/user'
import {
  getGeofencesAddGeofenceSetting,
  getLandmarksAddPOISetting,
  type UserAvailableMapApiProvider,
} from 'duxs/user-sensitive-selectors'
import { getFocusedVehicle } from 'duxs/vehicles'
import { MapApiProvider } from 'src/api/user/types'
import SVGIcon from 'src/components/Icon/SVGIcon'
import { getVehicleDetailsModalMainPath } from 'src/modules/app/GlobalModals/VehicleDetails/utils'
import { clickedMapContextMenuWhatsNearby } from 'src/modules/map-view/actions'
import { useTypedSelector } from 'src/redux-hooks'
import type { MapsExtended } from 'src/types/extended/google-maps'
import {
  reContextMenuOptions,
  type ReContextMenuOptions,
} from 'src/util-components/map/shared/context-menu-utils/re-data'
import ReBaseContextMenu from 'src/util-components/map/shared/re-context-menu'
import { getWgs84ValueFromRenderedCoordsOnMap } from 'src/util-functions/china-map-utils'

import GeofenceIcon from 'assets/svg/geofence.svg'

import type { GeofenceId } from '../../../api/types'
import { getGeofenceDetailsModalMainPath } from '../FleetMapView/Geofence/utils'
import { getPOIDetailsModalMainPath } from '../FleetMapView/POI/utils'

type Position = {
  x: number
  y: number
  lat: number
  lng: number
}

type Props = {
  mapApiProviderId: MapApiProvider
  showDirections?: boolean
  contextMenuElement: {
    type: string
    id: string
    position: Position | null
  } | null
  mapRef: React.RefObject<HTMLDivElement>
  mapsApi: MapsExtended.MapObject['maps']
  position: Position
  mapTypeId: google.maps.MapTypeId
  onCloseContextMenu: () => void
  onMeasureDistance: (lat: number, lng: number) => void
  onShowGPSCoordinates: (position: Position) => void
  onDirectionsChange?: (
    direction: 'start' | 'end' | 'clean',
    position?: Position,
  ) => void
  directions: {
    start: google.maps.LatLngLiteral | undefined
    end: google.maps.LatLngLiteral | undefined
  }
}

function ReContextMenu({
  mapApiProviderId,
  mapTypeId,
  showDirections = true,
  contextMenuElement,
  onCloseContextMenu,
  onDirectionsChange,
  onMeasureDistance,
  onShowGPSCoordinates,
  position,
  mapRef,
  mapsApi,
  directions,
}: Props) {
  const history = useHistory()
  const dispatch = useDispatch()

  const { geofencesDeleteGeofence } = useTypedSelector(getSettings_UNSAFE)
  const center = useTypedSelector(getCenter)
  const zoom = useTypedSelector(getZoom)
  const landmarksAddPOI = useTypedSelector(getLandmarksAddPOISetting)
  const geofencesAddGeofence = useTypedSelector(getGeofencesAddGeofenceSetting)
  const currentLayerVisibility = useTypedSelector(getCurrentLayerVisibility)
  const focusedVehicle = useTypedSelector(getFocusedVehicle)

  const mapAssets = {
    focusedVehicle,
    showGeofences: currentLayerVisibility.userGeofences,
    showVehicles: currentLayerVisibility.livePositions,
  }

  const mapState = { center, zoom }

  const getMenuElements = () => {
    const element =
      contextMenuElement && contextMenuElement.type
        ? contextMenuElement
        : { type: null }

    switch (element.type) {
      case 'geofence': {
        return {
          items: [
            {
              icon: <InfoOutlinedIcon />,
              message: 'More Info',
              handleOnClick: () => {
                history.push(
                  getGeofenceDetailsModalMainPath(history.location, {
                    geo: 'edit',
                    id: element.id as GeofenceId,
                  }),
                  { mapAssets, mapState },
                )
              },
            },
            {
              icon: <AlbumOutlinedIcon />,
              message: "What's Nearby?",
              handleOnClick: () => handleNearby(),
            },
            {
              icon: <DeleteOutlineOutlinedIcon />,
              message: 'Delete Geofence',
              handleOnClick: () => handleDeleteGeofence(element.id),
              disabled: !geofencesDeleteGeofence,
            },
          ],
        }
      }
      case 'landmark': {
        return {
          withStreetView: mapApiProviderId === MapApiProvider.GOOGLE,
          items: [
            {
              icon: <InfoOutlinedIcon />,
              message: 'More Info',
              handleOnClick: () => {
                history.push(
                  getPOIDetailsModalMainPath(history.location, {
                    poi: 'edit',
                    id: element.id as GeofenceId,
                  }),
                )
              },
            },
            {
              icon: <AlbumOutlinedIcon />,
              message: "What's Nearby?",
              handleOnClick: () => handleNearby(),
            },
          ],
        }
      }
      case 'vehicle': {
        return {
          withStreetView: mapApiProviderId === MapApiProvider.GOOGLE,
          items: [
            {
              icon: <InfoOutlinedIcon />,
              message: 'More Info',
              handleOnClick: () => {
                history.push(
                  getVehicleDetailsModalMainPath(
                    history.location,
                    element.id as VehicleId,
                  ),
                )
              },
            },
            {
              icon: <AlbumOutlinedIcon />,
              message: "What's Nearby?",
              handleOnClick: () => handleNearby(),
            },
          ],
        }
      }
      default: {
        const items: ReContextMenuOptions = (
          [
            ...(showDirections && onDirectionsChange
              ? [
                  {
                    ...reContextMenuOptions.directions.start,
                    handleOnClick: () => {
                      onCloseContextMenu()
                      onDirectionsChange('start', position)
                    },
                  },
                  {
                    ...reContextMenuOptions.directions.finish,
                    handleOnClick: () => {
                      onCloseContextMenu()
                      onDirectionsChange('end', position)
                    },
                  },
                  {
                    ...reContextMenuOptions.directions.clean,
                    handleOnClick: () => {
                      onCloseContextMenu()
                      onDirectionsChange('clean', position)
                    },
                    disabled:
                      R.isNullish(directions.start) && R.isNullish(directions.end),
                  },
                ]
              : []),
            landmarksAddPOI
              ? {
                  icon: <LocationOnOutlinedIcon />,
                  message: 'Add Landmark',
                  handleOnClick: () => handleCreateLandmark(),
                }
              : null,
            geofencesAddGeofence
              ? {
                  icon: (
                    <SVGIcon
                      width="20"
                      height="20"
                      svg={GeofenceIcon}
                    />
                  ),
                  message: 'Add Geofence',
                  handleOnClick: () => handleCreateGeofence(),
                }
              : null,
            {
              icon: <AlbumOutlinedIcon />,
              message: "What's Nearby?",
              handleOnClick: () => handleNearby(),
            },
            {
              ...reContextMenuOptions.distanceMeasuring,
              handleOnClick: () => onMeasureDistance(position.lat, position.lng),
            },
            {
              ...reContextMenuOptions.gpsCoordinates,
              handleOnClick: () => {
                onCloseContextMenu()
                onShowGPSCoordinates(position)
              },
            },
          ] satisfies Array<ReContextMenuOptions[number] | null>
        ).filter((item) => R.isNonNullish(item))

        if (mapApiProviderId !== MapApiProvider.GOOGLE) {
          items.push(
            reContextMenuOptions.getStreetViewInNewTab({
              latLng: position,
              onClick: () => onCloseContextMenu(),
            }),
          )
        }

        return { items }
      }
    }
  }

  const handleCreateGeofence = () => {
    history.push(
      getGeofenceDetailsModalMainPath(history.location, {
        geo: 'add',
        initialMapData: 'none',
      }),
      {
        mapAssets,
        mapState: {
          center: position,
          zoom: zoom,
        },
      },
    )
  }

  const handleCreateLandmark = () => {
    const coordsInWgs84 = getWgs84ValueFromRenderedCoordsOnMap({
      renderedCoordsInWgsOrGcj: position,
      mapApiProviderId,
      mapTypeId,
    })

    history.push(
      getPOIDetailsModalMainPath(history.location, {
        poi: 'add',
        initialMapData: {
          zoom: zoom,
          coordsInWgs84,
          mapApiProvider: mapApiProviderId as UserAvailableMapApiProvider,
          mapTypeId: mapTypeId,
        },
      }),
    )
  }

  const handleNearby = () => {
    const { lat, lng } = position
    onCloseContextMenu()
    dispatch(setFocusedPoint(position))
    dispatch(clickedMapContextMenuWhatsNearby())
    dispatch(changeMapCenterZoom(lat, lng, undefined, 'handleNearby'))
  }

  const handleDeleteGeofence = (id: string) => {
    dispatch(deleteGeofence(id as GeofenceId, null, 'map'))
    onCloseContextMenu()
  }

  return (
    <ReBaseContextMenu
      menuElements={getMenuElements()}
      {...{
        mapRef,
        mapsApi,
        contextMenuElement,
        onCloseContextMenu,
        position,
      }}
    />
  )
}

export default ReContextMenu
