import { PureComponent, type ComponentProps, type ComponentType } from 'react'
import { isEmpty, isNil } from 'lodash'
import memoizeOne from 'memoize-one'
import moment from 'moment-timezone'
import supercluster, {
  type Cluster,
  type ClusterGenerator,
  type Points,
} from 'points-cluster'
import { connect } from 'react-redux'
import type { RouteComponentProps } from 'react-router-dom'
import type { Except } from 'type-fest'

import type { VehicleId } from 'api/types'
import type { MapApiProvider } from 'api/user/types'
import {
  getFocusedPoint,
  getFocusedVehicleGroupId,
  getFocusedVehicleLastKnownPosition,
  getHoveredItem,
  getIsMapTypeFleetTab,
  getMapVehicles,
  getShouldDisplayVehicleLastKnownPosition,
  getTimelineProgress,
} from 'duxs/map'
import { getIsComparingTrips } from 'duxs/map-timeline'
import type { MapType } from 'duxs/map-types'
import {
  getActiveTimelineEventByActivity,
  getMultipleDaysPathComponentsFilteredEventsCoords,
  getSelectedTrip,
  getTimelineEventsByActivityAndMapType,
  getTimelineTripsMarkerData,
  type getTimelineEventsRaw,
  type TimelineEventWithRoadSpeed,
} from 'duxs/timeline'
import { getTrafficAlerts } from 'duxs/traffic-alerts'
import {
  getComparedTrips,
  getHoveredComparedTripIndex,
  getSelectedComparedTripIndex,
  getSelectedCompareTripActiveEvent,
} from 'duxs/trip-compare'
import { getMapZoomOptions } from 'duxs/user'
import type { UserAvailableMapApiProvider } from 'duxs/user-sensitive-selectors'
import { getFocusedVehicle } from 'duxs/vehicles'
import { TripPolyline } from 'src/components/_map/Polyline/Trip'
import type { EventHandlerBranded } from 'src/hooks/useEventHandler'
import { useVehiclesGroupedEventTypesQuery } from 'src/modules/api/useEventTypes'
import { SVRGPSMarker } from 'src/modules/map-view/map/svr-marker'
import { useTypedSelector } from 'src/redux-hooks'
import type { AppState } from 'src/root-reducer'
import type { FixMeAny, ValueOf } from 'src/types'
import type { MapsExtended } from 'src/types/extended/google-maps'
import type { ExcludeStrict } from 'src/types/utils'
import BaseMap from 'src/util-components/map/google/base-map'
import GoogleContextMenuDirections from 'src/util-components/map/google/GoogleContextMenuDirections'
import type { OnVehicleHoverHandler } from 'src/util-components/map/google/layers/vehicle-components'
import type { VehicleMarkerProps } from 'src/util-components/map/google/layers/vehicle-marker'
import mapEvents, {
  type MapEventsInjectedProps,
  type MapEventsWrappedComponentRequiredProps,
} from 'src/util-components/map/shared/map-events'
import type { MapProviderMetaData } from 'src/util-components/map/shared/types'
import { is2DimensionalArray } from 'src/util-functions/array-utils'

import {
  checkForValidCoords,
  generateClusters,
  getDateDiffByDays,
  MAP_CLUSTER_MIN_ZOOM,
  MAP_CLUSTER_RADIUS,
  zoomToBounds,
  type VIEW_MODE,
} from 'cartrack-utils'
import { RenderIfInBounds, VehicleComponents, VehicleMarker } from 'cartrack-ui-kit'
import type { FullscreenMapNavigationState } from '../fullscreen-map/slice'
import type { MapPlaceResult } from '../types'
import OrientationButtons from './orientation-buttons'
import PathComponents from './path-components/path-components'
import PointMarker from './point-marker'
import ReContextMenu from './re-context-menu'
import sensorComponents from './sensor-components'
import { useFocusedVehicleSensorMarkerEvents } from './sensor-marker/useSensorMarkerEvents'
import TrafficAlert from './traffic-alert'
import { TripCompareComponents } from './TripCompareComponents'

export type VehiclePoints = ReadonlyArray<
  Points[number] & {
    vehicle: MapClassProps['vehicles'][number]
  }
>
type VehicleClusterGenerator = ClusterGenerator<VehiclePoints>
type VehicleCluster = Cluster<VehiclePoints>

type SensorClusterGenerator = ReturnType<typeof generateSensorsClusterData>[1]

const mapVehicleCluster = (c: VehicleCluster) => ({
  id: `${c.numPoints}_${c.points[0].vehicle.id}`,
  vehiclePoints: c.points,
  lat: c.wy,
  lng: c.wx,
  numPoints: c.numPoints,
})

export type MappedVehicleCluster = ReturnType<typeof mapVehicleCluster>

type FocusedVehicleSensorMarkerEvents = ReturnType<
  typeof useFocusedVehicleSensorMarkerEvents
>['events']

const generateVehiclesClusterData = (props: MapClassProps) =>
  generateClusters(props, {
    itemName: 'vehicle',
    accessor: 'vehicles',
    mapCluster: mapVehicleCluster,
  })

const mapSensorCluster = (
  c: Cluster<
    Array<{
      event: FocusedVehicleSensorMarkerEvents[number]
      lat: number
      lng: number
    }>
  >,
) => ({
  id: `${c.numPoints}_${c.points[0].event.id}`,
  sensorPoints: c.points,
  lat: c.wy,
  lng: c.wx,
  numPoints: c.numPoints,
})

const generateSensorsClusterData = ({
  mapState,
  sensorEvents,
  mapClusterMaxZoom,
}: {
  mapState: MapClassProps['mapState']
  sensorEvents: FocusedVehicleSensorMarkerEvents
  mapClusterMaxZoom: number
}) => {
  const filteredItems = sensorEvents.filter(
    (i) => checkForValidCoords(i).hasValidCoords,
  )
  const clusterFunc = supercluster(
    filteredItems.map((event) => ({ event, lat: event.lat, lng: event.lng })),
    {
      minZoom: MAP_CLUSTER_MIN_ZOOM,
      maxZoom: mapClusterMaxZoom,
      radius: MAP_CLUSTER_RADIUS,
    },
  )
  const clusters = mapState ? clusterFunc(mapState).map((c) => mapSensorCluster(c)) : []
  return [clusters, clusterFunc] as const
}

export type MappedSensorCluster = ReturnType<
  typeof generateSensorsClusterData
>[0][number]

export type MapComponentProps = {
  mapApiProviderId?: UserAvailableMapApiProvider
  isComparingTrips?: boolean
  mapObject: MapsExtended.MapObject | undefined
  changeViewMode: (mode: ValueOf<typeof VIEW_MODE>) => void
  disableZoomOnMount?: boolean
  focusedItem?: Record<string, FixMeAny> | null
  onVehicleMarkerClick: (vehicleId: VehicleId) => void
  onEventMarkerClick?: (...args: Array<FixMeAny>) => void
  onFullscreenClick?: (...args: Array<FixMeAny>) => any
  onFullscreenEscape?: (...args: Array<FixMeAny>) => any
  onVehicleHover?: OnVehicleHoverHandler
  selectedType: 'vehicle'
  type: MapType | ''
  useVehicleIconColor?: boolean
  vehicleDisplayName?: string
  viewMode: ValueOf<typeof VIEW_MODE>

  mapTypeId: google.maps.MapTypeId
  onChangeMapTypeId: (type: google.maps.MapTypeId) => void

  // Timeline
  focusedItemStartDate?: Date | null
  timelineSliderPlaying?: boolean
  timelineStartTime: number

  isLandmarkEnable?: boolean
  isFullscreen?: boolean

  /* Props that are prop drilled to BaseMap. */
  onMapsApiLoaded: MapsExtended.OnGoogleApiLoaded
  mapProviderMetaData: MapProviderMetaData<typeof MapApiProvider.GOOGLE>
  places: Array<MapPlaceResult>
  forceMapUpdate?: number | null
  extraContent?: React.ReactNode
  mapHeight: string
} & Pick<
  RouteComponentProps<any, any, { pathname?: string } | undefined>,
  'location' | 'history'
>

type MapClassProps = MapEventsInjectedProps &
  ReduxProps &
  MapComponentProps & {
    focusedVehicleSensorMarkerEvents: FocusedVehicleSensorMarkerEvents | undefined
    vehiclesGroupedEventTypesQuery: ReturnType<typeof useVehiclesGroupedEventTypesQuery>
  }

type State = {
  vehicleClustersFunc: VehicleClusterGenerator | null
  vehicleClusters: ReadonlyArray<MappedVehicleCluster>
  sensorClustersFunc: SensorClusterGenerator | null
  sensorClusters: Array<MappedSensorCluster>
}

const defaultProps = {
  disableZoomOnMount: false,
  isFullscreen: false,
  onVehicleHover: {
    onMouseEnter: () => {},
    onMouseLeave: () => {},
  } as ExcludeStrict<MapClassProps['onVehicleHover'], undefined>,
  onEventMarkerClick: () => {},
}

export default function MapComponent(props: MapComponentProps) {
  const isMapTypeFleetTab = useTypedSelector(getIsMapTypeFleetTab)
  const focusedVehicle = useTypedSelector(getFocusedVehicle)
  const focusedVehicleSensorMarkerEvents = useFocusedVehicleSensorMarkerEvents()
  const vehiclesGroupedEventTypesQuery = useVehiclesGroupedEventTypesQuery()

  return (
    <EnhancedComponent
      {...props}
      vehiclesGroupedEventTypesQuery={vehiclesGroupedEventTypesQuery}
      focusedVehicleSensorMarkerEvents={
        focusedVehicle && isMapTypeFleetTab
          ? focusedVehicleSensorMarkerEvents.events
          : undefined
      }
    />
  )
}

class MapClass extends PureComponent<MapClassProps & typeof defaultProps, State> {
  static defaultProps = defaultProps

  state: State = {
    vehicleClustersFunc: null,
    vehicleClusters: [],
    sensorClustersFunc: null,
    sensorClusters: [],
  }

  componentDidUpdate(prevProps: MapClassProps) {
    if (
      this.props.mapsApi &&
      this.props.mapState &&
      // Fit bounds to compared trips
      this.props.isComparingTrips &&
      this.props.comparedTrips.length > 0 &&
      prevProps.comparedTrips !== this.props.comparedTrips
    ) {
      const allEvents = this.props.comparedTrips.reduce<
        MapClassProps['comparedTrips'][number]['events']
      >((acc, t) => {
        acc.push(...t.events)
        return acc
      }, [])
      if (allEvents.length > 0) {
        const newBounds = zoomToBounds(allEvents, this.props.mapState.size)
        this.props.changeMapCenterZoom(
          ...([...newBounds, 'comparedTripBounds'] as const),
        )
      }
    }

    if (
      // Or create clusters and fit bounds to vehicles
      this.props.vehicles !== prevProps.vehicles ||
      this.props.currentLayerVisibility.livePositionClusters !==
        prevProps.currentLayerVisibility.livePositionClusters
    ) {
      this.generateVehicleClusters(this.props)
    }

    if (
      this.props.focusedVehicleSensorMarkerEvents !==
        prevProps.focusedVehicleSensorMarkerEvents ||
      this.props.currentLayerVisibility.sensors !==
        prevProps.currentLayerVisibility.sensors
    ) {
      this.generateSensorClusters(this.props)
    }
  }

  generateVehicleClusters = (props = this.props) => {
    const [vehicleClusters, vehicleClustersFunc, filteredVehicles] =
      generateVehiclesClusterData(props)

    if (
      props.mapsApi &&
      props.mapState &&
      (this.props.vehicles.length === 0 ||
        this.props.focusedVehicleGroupId !== props.focusedVehicleGroupId) &&
      filteredVehicles.length > 0 &&
      !this.props.disableZoomOnMount
    ) {
      // Will only run when:
      // - map is loaded
      // - the last vehicle list was empty
      // - not flagged to keep old position
      props.changeMapCenterZoom(
        ...([
          ...zoomToBounds(filteredVehicles, props.mapState.size, {
            maxZoom: props.focusedVehicleGroupId
              ? undefined
              : props.mapZoomOptions.maxZoom,
          }),
          'newVehicleList',
        ] as const),
      )
    }

    this.setState({ vehicleClusters, vehicleClustersFunc })
  }

  handleGoogleApiLoaded = () => {
    if (!this.state.vehicleClustersFunc) this.generateVehicleClusters()
    if (!this.state.sensorClustersFunc) this.generateSensorClusters()

    if (
      this.props.vehicles.length > 0 &&
      this.props.mapState &&
      !this.props.focusedVehicle &&
      !this.props.isComparingTrips &&
      !this.props.disableZoomOnMount
    ) {
      const mapSetting = zoomToBounds(this.props.vehicles, this.props.mapState.size, {
        maxZoom: this.props.mapZoomOptions.maxZoom,
      })

      if (this.props.center && this.props.center.lat && this.props.center.lng) {
        const { lat, lng } = this.props.center
        mapSetting[0] = lat
        mapSetting[1] = lng
      }

      mapSetting[2] = this.props.zoom === 1 ? mapSetting[2] : this.props.zoom

      // NOTE: After we set the default center for Google Maps, if it waits too long until we
      // calculate the center(by vehicles after they're loaded, and the Google Map is nearly finished loading already),
      // it will revert back to the default center value.
      // So we setTimeout to 50 milliseconds here to ensure that the default center of the map value doesn't override the calculated center value
      window.setTimeout(
        () =>
          this.props.changeMapCenterZoom(
            ...([...mapSetting, 'handleGoogleApiLoaded'] as const),
          ),
        50,
      )
    }
  }

  handleMapChange = (obj: Parameters<VehicleClusterGenerator>[0]) => {
    this.setState((prevState) => ({
      vehicleClusters: prevState.vehicleClustersFunc
        ? prevState.vehicleClustersFunc(obj).map((c) => mapVehicleCluster(c))
        : [],
      sensorClusters: prevState.sensorClustersFunc
        ? prevState.sensorClustersFunc(obj).map((c) => mapSensorCluster(c))
        : [],
    }))
  }

  handleVehicleMarkerClick = (
    _event: React.MouseEvent<HTMLDivElement, MouseEvent>,
    { vehicleId }: { vehicleId: VehicleId },
  ) => {
    if (!this.props.isComparingTrips) {
      // Unmount measure path component when vehicle is clicked
      this.props.onMeasureDistance()
      this.props.onVehicleMarkerClick(vehicleId)
    }
  }

  handleVehicleContextMenuClick: NonNullable<
    VehicleMarkerProps['onVehicleContextMenuClick']
  > = (vehicleId, { event, positionInWgs84 }) => {
    const { mapObject } = this.props
    if (!mapObject?.map) return

    const rect = mapObject.map.getDiv().getBoundingClientRect()
    // Relative position of the event in the map
    const xY = {
      x: event.clientX - rect.left,
      y: event.clientY - rect.top,
    }

    this.props.onContextMenuClick('vehicle', vehicleId, {
      ...positionInWgs84,
      ...xY,
    })
  }
  handleVehicleClusterClick = (event: React.MouseEvent<HTMLDivElement, MouseEvent>) => {
    event.stopPropagation()
    this.props.onClusterClick(event, {
      items: this.state.vehicleClusters,
      accessor: 'vehiclePoints',
      origin: 'handleVehicleClusterClick',
    })
  }

  /**
   * Needs to be refactored
   */
  getVehicleMarkers(): {
    focusedVehicleMarker: React.ReactNode
    ghostVehicleMarker: React.ReactNode
  } {
    const {
      activeTimelineEvent,
      activeCompareEvent,
      focusedVehicle,
      isFullscreen,
      isComparingTrips,
      currentLayerVisibility,
      selectedComparedTrip,
      timelineSliderPlaying,
      timelineStartTime,
      timelineProgress,
      timelineEventsByActivity,
      useVehicleIconColor,
      vehicleDisplayName,
      shouldDisplayVehicleLastKnownPosition,
      focusedVehicleLastKnownPosition,
      zoom,
      type: mapType,
      mapObject,
      mapTypeId,
    } = this.props

    if (!mapObject) {
      return { focusedVehicleMarker: null, ghostVehicleMarker: null }
    }

    let focusedVehicleMarker = null
    let ghostVehicleMarker = null

    const hasComparedTripInMap =
      (isComparingTrips || isFullscreen) && selectedComparedTrip && activeCompareEvent

    if (focusedVehicle !== null || hasComparedTripInMap) {
      const commonVehicleMarkerProps = {
        isHovered: false,
        vehicleDisplayName,
        showLabel: currentLayerVisibility.livePositionLabels,
        onVehicleClick: this.handleVehicleMarkerClick,
      }

      // Do not display ghost and focused vehicle marker if privacy mode is enabled
      if (
        focusedVehicle !== null &&
        !focusedVehicle.privacyModeEnabled &&
        !hasComparedTripInMap
      ) {
        const todayDate = moment().format()
        const selectedTimelineDate = moment(timelineStartTime).format()
        const isTodaySelected = getDateDiffByDays(todayDate, selectedTimelineDate) === 0

        if (
          mapType !== 'svr-units' &&
          timelineEventsByActivity.type === 'daily' &&
          timelineEventsByActivity.events.length > 0 &&
          isTodaySelected &&
          timelineProgress !== 0 &&
          timelineProgress !== 1
        ) {
          const lastEvent =
            timelineEventsByActivity.events[timelineEventsByActivity.events.length - 1]

          ghostVehicleMarker = (
            <VehicleMarker
              key={`ghost-${focusedVehicle.id}`}
              bearing={lastEvent.bearing}
              showBearing
              map={mapObject.map}
              mapTypeId={mapTypeId}
              positionInWgs84={{
                lat: lastEvent.latitude,
                lng: lastEvent.longitude,
              }}
              statusClassName={focusedVehicle?.statusClassName}
              timelineSliderPlaying={timelineSliderPlaying}
              width={focusedVehicle.width}
              positionType={focusedVehicle.positionType}
              radius={focusedVehicle.radius}
              zoom={zoom}
              isFocused
              ghost
              vehicle={{ ...focusedVehicle, gpsFixType: lastEvent.gpsFixType }}
              {...commonVehicleMarkerProps}
            />
          )
        }

        if (
          shouldDisplayVehicleLastKnownPosition &&
          focusedVehicleLastKnownPosition !== null
        ) {
          // Vehicle last known position marker
          focusedVehicleMarker =
            mapType === 'svr-units' ? (
              <SVRGPSMarker
                key={`svr-${focusedVehicle.id}`}
                positionInWgs84={focusedVehicleLastKnownPosition}
                map={mapObject.map}
                mapTypeId={mapTypeId}
                isFocused
              />
            ) : (
              <VehicleMarker
                key={`focused-${focusedVehicle.id}`}
                map={mapObject.map}
                ghost
                positionInWgs84={focusedVehicleLastKnownPosition}
                statusClassName={
                  focusedVehicleLastKnownPosition.statusClassName ?? 'ignition-off'
                }
                mapTypeId={mapTypeId}
                onVehicleContextMenuClick={this.handleVehicleContextMenuClick}
                useIconColor={useVehicleIconColor}
                width={focusedVehicle.width}
                positionType={focusedVehicle.positionType}
                radius={focusedVehicle.radius}
                zoom={zoom}
                vehicle={focusedVehicle}
                {...commonVehicleMarkerProps}
              />
            )
        } else if (mapType !== 'svr-units') {
          if (
            activeTimelineEvent.type === 'daily' &&
            activeTimelineEvent.data !== null
          ) {
            focusedVehicleMarker = (
              <VehicleMarker
                map={mapObject.map}
                key={`focused-${focusedVehicle.id}`}
                bearing={activeTimelineEvent.data.bearing}
                showBearing
                positionInWgs84={{
                  lat: activeTimelineEvent.data.lat,
                  lng: activeTimelineEvent.data.lng,
                }}
                statusClassName={activeTimelineEvent.data.statusClassName}
                isFocused
                mapTypeId={mapTypeId}
                onVehicleContextMenuClick={this.handleVehicleContextMenuClick}
                useIconColor={useVehicleIconColor}
                width={focusedVehicle.width}
                positionType={focusedVehicle.positionType}
                radius={focusedVehicle.radius}
                zoom={zoom}
                vehicle={{
                  ...focusedVehicle,
                  gpsFixType: activeTimelineEvent.data.gpsFixType,
                }}
                {...commonVehicleMarkerProps}
              />
            )
          } else if (
            activeTimelineEvent.type === 'multipleDays' &&
            activeTimelineEvent.data !== null &&
            activeTimelineEvent.data.coords !== null
          ) {
            focusedVehicleMarker = (
              <VehicleMarker
                map={mapObject.map}
                key={`focused-${focusedVehicle.id}`}
                mapTypeId={mapTypeId}
                positionInWgs84={activeTimelineEvent.data.coords}
                statusClassName={activeTimelineEvent.data.vehicleStatus}
                isFocused
                onVehicleContextMenuClick={this.handleVehicleContextMenuClick}
                useIconColor={useVehicleIconColor}
                width={focusedVehicle.width}
                positionType={focusedVehicle.positionType}
                radius={focusedVehicle.radius}
                zoom={zoom}
                vehicle={{
                  ...focusedVehicle,
                  // Remove any when backed returns gpsFixType for multiple days endpoint
                  gpsFixType: (activeTimelineEvent.data as any).gpsFixType,
                }}
                {...commonVehicleMarkerProps}
              />
            )
          }
        }
      } else if (hasComparedTripInMap) {
        const vehicle = selectedComparedTrip.vehicle
        const event = activeCompareEvent
        const statusClassName = activeCompareEvent.statusClassName

        focusedVehicleMarker = (
          <VehicleMarker
            map={mapObject.map}
            key={`focused-${vehicle.id}`}
            bearing={(event && event.bearing) || (vehicle as FixMeAny).bearing}
            showBearing
            positionInWgs84={{
              lat: event.latitude,
              lng: event.longitude,
            }}
            statusClassName={statusClassName}
            isFocused
            mapTypeId={mapTypeId}
            onVehicleContextMenuClick={this.handleVehicleContextMenuClick}
            useIconColor={useVehicleIconColor}
            width={(event as FixMeAny).width}
            positionType={(event as FixMeAny).positionType}
            radius={(event as FixMeAny).radius}
            zoom={zoom}
            vehicle={vehicle}
            {...commonVehicleMarkerProps}
          />
        )
      }
    }

    return { focusedVehicleMarker, ghostVehicleMarker }
  }

  renderVehicleClusters = () => {
    const {
      currentLayerVisibility,
      focusedVehicle,
      hoveredItem,
      isComparingTrips,
      onVehicleHover,
      useVehicleIconColor,
      mapObject,
      mapTypeId,
    } = this.props

    const showVehicles =
      focusedVehicle !== null
        ? currentLayerVisibility.livePositions &&
          currentLayerVisibility.livePositionShowWhileAVehicleIsSelected
        : currentLayerVisibility.livePositions

    return (
      mapObject &&
      !isComparingTrips &&
      showVehicles && (
        <VehicleComponents
          key="vehicle-components"
          mapTypeId={mapTypeId}
          map={mapObject.map}
          vehicleClusters={this.state.vehicleClusters}
          useVehicleIconColor={useVehicleIconColor}
          hoveredVehicle={hoveredItem}
          showLabels={currentLayerVisibility.livePositionLabels}
          onVehicleClick={this.handleVehicleMarkerClick}
          onVehicleClusterClick={this.handleVehicleClusterClick}
          onVehicleContextMenuClick={this.handleVehicleContextMenuClick}
          onVehicleHover={onVehicleHover}
        />
      )
    )
  }

  renderTripCompare = () => {
    const {
      mapObject,
      mapState,
      mapProviderMetaData,
      comparedTrips,
      hoveredComparedTripIndex,
      isComparingTrips,
      selectedComparedTrip,
      timelineTripsMarkerData,
      mapTypeId,
    } = this.props

    return (
      isComparingTrips &&
      mapObject &&
      mapState && (
        <TripCompareComponents
          key="trip-compare-components"
          hoveredComparedTripIndex={hoveredComparedTripIndex}
          comparedTrips={comparedTrips}
          selectedComparedTrip={selectedComparedTrip}
          timelineTripsMarkerData={timelineTripsMarkerData}
          mapProps={{
            mapApiProviderId: mapProviderMetaData.currentMapProvider,
            mapObject,
            mapTypeId,
          }}
        />
      )
    )
  }

  renderPointMarker = () =>
    this.props.focusedPoint && (
      <PointMarker
        lat={this.props.focusedPoint.lat}
        lng={this.props.focusedPoint.lng}
      />
    )

  renderTrafficAlerts = () => {
    if (this.props.mapState === null) {
      return null
    }

    const mapState = this.props.mapState

    return (
      this.props.mapsApi &&
      this.props.currentLayerVisibility.maps &&
      this.props.currentLayerVisibility.alerts &&
      this.props.trafficAlerts.map((t) => (
        <RenderIfInBounds
          key={t.id}
          bounds={mapState.bounds}
          lat={t.lat}
          lng={t.lng}
        >
          <TrafficAlert {...t} />
        </RenderIfInBounds>
      ))
    )
  }

  // It's safe to cast this to EventHandlerBranded because this function is stable.
  handleEventMarkerClick = ((id: string) => {
    this.props.onEventMarkerClick?.(id, this.props.timelineEventsByActivity.events)
  }) as EventHandlerBranded<[id: string]>

  memoizedDailyRenderPath = memoizeOne(
    ({
      mapObject,
      mapApiProviderId,
      currentLayerVisibility,
      focusedVehicle,
      selectedTripEvents,
      timelineEventsByActivityEvents,
      timelineTripsMarkerData,
      zoom,
      useSVREvents,
      mapTypeId,
    }: {
      mapObject: MapClassProps['mapObject']
      mapApiProviderId: MapClassProps['mapProviderMetaData']['currentMapProvider']
      currentLayerVisibility: MapClassProps['currentLayerVisibility']
      focusedVehicle: MapClassProps['focusedVehicle']
      selectedTripEvents: Array<TimelineEventWithRoadSpeed>
      timelineEventsByActivityEvents: Readonly<ReturnType<typeof getTimelineEventsRaw>>
      timelineTripsMarkerData: MapClassProps['timelineTripsMarkerData']
      zoom: MapClassProps['zoom']
      useSVREvents: boolean
      mapTypeId: MapClassProps['mapTypeId']
    }) =>
      mapObject && (
        <PathComponents
          key="path-components"
          mapProps={{ mapApiProviderId, mapObject, mapTypeId }}
          focusedVehicle={focusedVehicle}
          events={timelineEventsByActivityEvents}
          selectedTripEvents={selectedTripEvents}
          timelineTripsMarkerData={timelineTripsMarkerData}
          currentLayerVisibility={currentLayerVisibility}
          zoom={zoom}
          onEventMarkerClick={this.handleEventMarkerClick}
          useSVREvents={useSVREvents}
        />
      ),
    ([newArgs], [prevArgs]) =>
      Object.keys(newArgs).every((key) => newArgs[key] === prevArgs[key]),
  )

  handleOnSensorClick = (event: FixMeAny) => this.props.onMarkerItemClick(event)

  handleSensorClusterClick: React.MouseEventHandler<HTMLDivElement> = (event) =>
    this.props.onClusterClick(event, {
      items: this.state.sensorClusters,
      accessor: 'sensorPoints',
    })

  generateSensorClusters = (props = this.props) => {
    if (
      props.focusedVehicleSensorMarkerEvents &&
      props.focusedVehicleSensorMarkerEvents.length > 0
    ) {
      const [sensorClusters, sensorClustersFunc] = generateSensorsClusterData({
        mapState: props.mapState,
        sensorEvents: props.focusedVehicleSensorMarkerEvents,
        mapClusterMaxZoom: props.mapZoomOptions.mapClusterMaxZoom,
      })

      this.setState({ sensorClusters, sensorClustersFunc })
    }
  }

  renderSensorClusters = () => {
    const {
      isComparingTrips,
      focusedItem,
      focusedVehicleSensorMarkerEvents,
      vehiclesGroupedEventTypesQuery,
    } = this.props

    return !isComparingTrips &&
      focusedItem &&
      focusedVehicleSensorMarkerEvents &&
      focusedVehicleSensorMarkerEvents.length > 0 &&
      vehiclesGroupedEventTypesQuery.data !== undefined
      ? sensorComponents({
          vehicleId: focusedItem.id,
          onSensorClick: this.handleOnSensorClick,
          sensorClusters: this.state.sensorClusters,
          onSensorClusterClick: this.handleSensorClusterClick,
          getSensorMarkerEventLabel: vehiclesGroupedEventTypesQuery.data.getEventLabel,
        })
      : null
  }

  render() {
    const {
      activeTimelineEvent,
      center,
      changeMapCenterZoom,
      currentLayerVisibility,
      focusedItem,
      focusedItemStartDate,
      focusedVehicle,
      followFocusedItem,
      history,
      isComparingTrips,
      isLandmarkEnable,
      isMeasuring,
      location,
      map,
      mapProviderMetaData,
      mapObject,
      mapRef,
      mapState,
      mapsApi,
      multipleDaysPathComponentsEventsCoords,
      onCloseContextMenu,
      onClusterClick,
      onContextMenuClick,
      onFollowFocusedItemClick,
      onFullscreenClick,
      onMapClick,
      onMapRightClick,
      onMapsApiLoaded,
      onMeasureDistance,
      places,
      selectedTripEvents,
      selectedType,
      setLayerVisibility,
      setMapState,
      setVehicleFocusedLayerVisibility,
      timelineTripsMarkerData,
      type,
      viewMode,
      zoom,
      extraContent,
      mapTypeId,
      onChangeMapTypeId,
      timelineEventsByActivity,
    } = this.props

    const prevPathName = location.state?.pathname

    const fullscreenState: FullscreenMapNavigationState = {
      focusedItem,
      focusedItemStartDate,
      type,
      selectedType,
    }

    const mapApiProviderId = mapProviderMetaData.currentMapProvider

    return (
      <>
        {extraContent}
        <GoogleContextMenuDirections>
          {({ renderDirectionsMarkers, onDirectionsChange, directions }) => {
            const renderExtraMapElements = () => {
              const { focusedVehicleMarker, ghostVehicleMarker } =
                this.getVehicleMarkers()

              let memoizedPath

              if (!isComparingTrips) {
                if (timelineEventsByActivity.type === 'daily') {
                  if (is2DimensionalArray(selectedTripEvents)) {
                    memoizedPath = selectedTripEvents.map((tripEvents) =>
                      this.memoizedDailyRenderPath({
                        mapObject,
                        mapApiProviderId,
                        currentLayerVisibility,
                        focusedVehicle,
                        selectedTripEvents: tripEvents,
                        timelineEventsByActivityEvents: timelineEventsByActivity.events,
                        timelineTripsMarkerData,
                        zoom,
                        useSVREvents: focusedVehicle !== null && type === 'svr-units',
                        mapTypeId,
                      }),
                    )
                  } else {
                    memoizedPath = this.memoizedDailyRenderPath({
                      mapObject,
                      mapApiProviderId,
                      currentLayerVisibility,
                      focusedVehicle,
                      selectedTripEvents,
                      timelineEventsByActivityEvents: timelineEventsByActivity.events,
                      timelineTripsMarkerData,
                      zoom,
                      useSVREvents: focusedVehicle !== null && type === 'svr-units',
                      mapTypeId,
                    })
                  }
                } else {
                  memoizedPath = mapObject && (
                    <TripPolyline
                      key="multipleDaysGooglePathComponents"
                      mapObject={mapObject}
                      pathInWgs84={multipleDaysPathComponentsEventsCoords}
                      mapTypeId={mapTypeId}
                    />
                  )
                }
              }

              return [
                this.renderVehicleClusters(),
                ghostVehicleMarker,
                focusedVehicleMarker,
                renderDirectionsMarkers(),
                this.renderTripCompare(),
                this.renderPointMarker(),
                memoizedPath,
                // this.renderDriverEvents(),
                this.renderTrafficAlerts(),
                this.renderSensorClusters(),
              ]
            }

            const maybeRenderContextMenu = () => {
              if (this.props.mapsApi && this.props.contextMenuCoords) {
                return (
                  <ReContextMenu
                    mapTypeId={mapTypeId}
                    mapApiProviderId={mapApiProviderId}
                    contextMenuElement={this.props.contextMenuElement}
                    mapRef={this.props.mapRef}
                    mapsApi={this.props.mapsApi}
                    position={this.props.contextMenuCoords}
                    onCloseContextMenu={this.props.onCloseContextMenu}
                    onMeasureDistance={this.props.onMeasureDistance}
                    onShowGPSCoordinates={this.props.onShowGPSCoordinates}
                    onDirectionsChange={onDirectionsChange}
                    directions={directions}
                  />
                )
              }
              return null
            }

            const vehicleActiveEventCoords =
              // eslint-disable-next-line no-nested-ternary
              activeTimelineEvent.data !== null
                ? activeTimelineEvent.type === 'daily'
                  ? {
                      lat: activeTimelineEvent.data.lat,
                      lng: activeTimelineEvent.data.lng,
                    }
                  : activeTimelineEvent.data.coords
                : focusedVehicle !== null
                  ? { lat: focusedVehicle.latitude, lng: focusedVehicle.longitude }
                  : null

            const isAVehicleTripWithMultipleDaysVisibleOnMap =
              type === 'fleet' &&
              focusedVehicle !== null &&
              activeTimelineEvent.type === 'multipleDays'

            return (
              <>
                <BaseMap
                  mapProviderMetaData={mapProviderMetaData}
                  showMapOptions={
                    isAVehicleTripWithMultipleDaysVisibleOnMap ? 'viewTray' : true
                  }
                  fullscreenPath="/map/fullscreen"
                  fullscreenState={fullscreenState}
                  hasFocusedItem={!isEmpty(focusedItem)}
                  showVisionOptions={!!focusedItem?.['isCamera']}
                  showDefaultMarkers={isNil(isLandmarkEnable) ? true : isLandmarkEnable}
                  onGoogleApiLoaded={this.handleGoogleApiLoaded}
                  onMapChange={this.handleMapChange}
                  onMapCenterChanged={onCloseContextMenu}
                  vehicleActiveEventCoords={vehicleActiveEventCoords}
                  viewTrayControls={(() => {
                    if (type === 'fleet' || prevPathName === '/map/fleet') {
                      return ({ fullscreenButton }) => (
                        <>
                          {isAVehicleTripWithMultipleDaysVisibleOnMap
                            ? null // Fullscreen currently does not render trips with multiple days (View all)
                            : fullscreenButton}
                          <OrientationButtons
                            changeViewMode={this.props.changeViewMode}
                            viewMode={this.props.viewMode}
                          />
                        </>
                      )
                    }
                    return undefined
                  })()}
                  changeViewMode={this.props.changeViewMode}
                  places={places}
                  {...{
                    isComparingTrips,
                    history,
                    location,
                    currentLayerVisibility,
                    onMapsApiLoaded,
                    changeMapCenterZoom,
                    setLayerVisibility,
                    onMeasureDistance,
                    mapState,
                    mapsApi,
                    mapRef,
                    map,
                    zoom,
                    setMapState,
                    onClusterClick,
                    onContextMenuClick,
                    onMapRightClick,
                    center,
                    isMeasuring,
                    onMapClick,
                    setVehicleFocusedLayerVisibility,
                    followFocusedItem,
                    onFollowFocusedItemClick,
                    onFullscreenClick,
                    viewMode,
                    mapTypeId,
                    onChangeMapTypeId,
                  }}
                >
                  {renderExtraMapElements()}
                </BaseMap>

                {maybeRenderContextMenu()}
              </>
            )
          }}
        </GoogleContextMenuDirections>
      </>
    )
  }
}

function mapStateToProps(state: AppState) {
  const comparedTrips = getComparedTrips(state)
  const selectedComparedTripIndex = getSelectedComparedTripIndex(state)
  const selectedComparedTrip =
    selectedComparedTripIndex === null ? null : comparedTrips[selectedComparedTripIndex]
  const focusedVehicle = getFocusedVehicle(state)

  return {
    activeTimelineEvent: getActiveTimelineEventByActivity(state),
    focusedPoint: getFocusedPoint(state),
    focusedVehicle,
    focusedVehicleGroupId: getFocusedVehicleGroupId(state),
    hoveredItem: getHoveredItem(state),
    selectedTripEvents: getSelectedTrip(state)?.events ?? [],
    trafficAlerts: getTrafficAlerts(state) as Array<FixMeAny>,
    vehicles: getMapVehicles(state),
    multipleDaysPathComponentsEventsCoords:
      getMultipleDaysPathComponentsFilteredEventsCoords(state),

    // Timeline
    timelineProgress: getTimelineProgress(state),
    timelineEventsByActivity: getTimelineEventsByActivityAndMapType(state),
    shouldDisplayVehicleLastKnownPosition:
      getShouldDisplayVehicleLastKnownPosition(state),
    focusedVehicleLastKnownPosition: getFocusedVehicleLastKnownPosition(state),
    timelineTripsMarkerData: getTimelineTripsMarkerData(state),

    // Trip Compare
    activeCompareEvent: getSelectedCompareTripActiveEvent(state),
    hoveredComparedTripIndex: getHoveredComparedTripIndex(state),
    isComparingTrips: getIsComparingTrips(state),
    selectedComparedTrip,
    comparedTrips,
    mapZoomOptions: getMapZoomOptions(state),
  }
}

type ReduxProps = ReturnType<typeof mapStateToProps>

const EnhancedComponent = connect(mapStateToProps)(
  mapEvents(MapClass as FixMeAny) as FixMeAny,
) as ComponentType<
  Except<
    ComponentProps<typeof MapClass>,
    keyof MapEventsInjectedProps | keyof ReduxProps | keyof typeof defaultProps
  > &
    Pick<MapClassProps, keyof typeof defaultProps> & // Makes props defined in "defaultProps" to show as optional props externally (if specified as optional in Props type)
    MapEventsWrappedComponentRequiredProps
>
