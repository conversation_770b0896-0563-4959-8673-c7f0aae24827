import { createSlice, type PayloadAction } from '@reduxjs/toolkit'
import type { ChangeEventValue, Coords, Size } from 'google-map-react'
import type { Points } from 'points-cluster'
import * as R from 'remeda'
import type { ReadonlyDeep } from 'type-fest'

import type { VehicleId } from 'api/types'
import type { AppState } from 'src/root-reducer'
import { MapTypeId, type MapsExtended } from 'src/types/extended/google-maps'
import {
  ImmutableDict,
  Obj_mapWithKey,
} from 'src/util-functions/performance-critical-utils'

import {
  setVehicleClusterPanelClosedState,
  setVehicleClusterPanelInitialOpenState,
} from '../../actions'
import type { FetchVehiclePositionsResolved } from '../api/queries'
import type { VehicleCluster, VehicleClusterId } from '../api/types'
import { clusterMapAreaUtils } from '../utils'
import { generateClusterMapFocusStateFrom } from './state-utils'

export type VehicleClusterPanelMapData = {
  // Instance with data of the maps api. DO NOT rely on them to update react state!
  readonlyInstance: {
    state: ChangeEventValue | null
    object: MapsExtended.MapObject | null
  }
  zoom: number
  center: Coords
  size: Size | null
  mapTypeId: google.maps.MapTypeId
  isFollowingVehicles: boolean
}

type ClusterData = {
  expanded: boolean
  mapData: VehicleClusterPanelMapData
}

type State = ReadonlyDeep<{
  clustersById: ImmutableDict<VehicleClusterId, ClusterData>
}>

export const initialState: State = {
  clustersById: ImmutableDict(),
}

const closedState = initialState

export const generateInitialOpenState = (
  state: State,
  {
    clusterIdToExpand,
    initialCenter,
    initialZoom,
  }: {
    clusterIdToExpand: VehicleClusterId
    initialCenter: Coords
    initialZoom: number
  },
) =>
  ({
    ...state,
    clustersById: state.clustersById.set(clusterIdToExpand, (current) => {
      const currentMapData = current?.mapData
      return {
        expanded: true,
        mapData: currentMapData
          ? {
              ...currentMapData,
              center: currentMapData.center ?? initialCenter,
              zoom: currentMapData.zoom ?? initialZoom,
            }
          : getInitialClusterMapData({ center: initialCenter, zoom: initialZoom }),
      }
    }),
  }) satisfies State

const getInitialClusterMapData = ({
  center,
  zoom,
}: {
  center: 'reset' | Coords
  zoom: 'reset' | number
}) => ({
  center: center === 'reset' ? { lat: 0, lng: 0 } : center,
  mapTypeId: MapTypeId.ROADMAP,
  readonlyInstance: { state: null, object: null },
  zoom: zoom === 'reset' ? 1 : zoom,
  size: null,
  isFollowingVehicles: true,
})

const initialClusterData = {
  expanded: true,
  mapData: getInitialClusterMapData({ center: 'reset', zoom: 'reset' }),
} satisfies ClusterData

const slice = createSlice({
  name: 'vehicleClusterPanel',
  initialState,
  reducers: {
    vehicleClusterPanel_cluster_mapOnDragEnd(
      draft,
      { payload: { clusterId } }: PayloadAction<{ clusterId: VehicleClusterId }>,
    ) {
      draft.clustersById = draft.clustersById.set(clusterId, (current) =>
        current
          ? {
              ...current,
              mapData: {
                ...current.mapData,
                isFollowingVehicles: false,
              },
            }
          : initialClusterData,
      )
    },
    vehicleClusterPanel_cluster_mapThrottledOnWheel(
      draft,
      { payload: { clusterId } }: PayloadAction<{ clusterId: VehicleClusterId }>,
    ) {
      draft.clustersById = draft.clustersById.set(clusterId, (current) =>
        current
          ? {
              ...current,
              mapData: {
                ...current.mapData,
                isFollowingVehicles: false,
              },
            }
          : initialClusterData,
      )
    },
    vehicleClusterPanel_cluster_clickedFollowUpButton(
      draft,
      {
        payload: { clusterId, vehiclesPoints },
      }: PayloadAction<{
        clusterId: VehicleClusterId
        vehiclesPoints: Array<google.maps.LatLngLiteral>
      }>,
    ) {
      draft.clustersById = draft.clustersById.set(clusterId, (current) => {
        if (
          !current ||
          !current.mapData.readonlyInstance.object ||
          R.isNullish(current.mapData.size)
        ) {
          return initialClusterData
        }

        const newIsFollowingVehicles = !current.mapData.isFollowingVehicles

        const { center, zoom } = newIsFollowingVehicles
          ? clusterMapAreaUtils.getBoundsFromWithExtraPadding({
              mapObject: current.mapData.readonlyInstance.object,
              mapSize: current.mapData.size,
              pathItems: vehiclesPoints,
            })
          : { center: current.mapData.center, zoom: current.mapData.zoom }

        return {
          ...current,
          mapData: {
            ...current.mapData,
            center,
            zoom,
            isFollowingVehicles: newIsFollowingVehicles,
          },
        }
      })
    },
    vehicleClusterPanel_cluster_clickedMapTypeButton(
      draft,
      {
        payload: { mapTypeId, clusterId },
      }: PayloadAction<{
        mapTypeId: google.maps.MapTypeId
        clusterId: VehicleClusterId
      }>,
    ) {
      draft.clustersById = draft.clustersById.set(clusterId, (current) =>
        current
          ? {
              ...current,
              mapData: {
                ...current.mapData,
                mapTypeId,
              },
            }
          : initialClusterData,
      )
    },
    vehicleClusterPanel_cluster_GoogleApiHasLoaded(
      draft,
      {
        payload: { map, clusterId, clusterVehicleIds, vehiclePositionsByVehicleId },
      }: PayloadAction<{
        map: MapsExtended.MapObject
        clusterId: VehicleClusterId
        vehiclePositionsByVehicleId: FetchVehiclePositionsResolved['vehiclePositionsByVehicleId']
        clusterVehicleIds: Array<VehicleId>
      }>,
    ) {
      draft.clustersById = draft.clustersById.set(clusterId, (current) => {
        if (!current) {
          return initialClusterData
        }

        const currentFocusState = {
          center: current.mapData.center,
          zoom: current.mapData.zoom,
        }

        const newFocusState = generateClusterMapFocusStateFrom({
          clusterVehicleIds,
          currentFocusState,
          currentIsFollowingVehicles: current.mapData.isFollowingVehicles,
          vehiclePositionsByVehicleId,
          mapObject: map,
        })

        return {
          ...current,
          mapData: {
            ...current.mapData,
            ...newFocusState,
            readonlyInstance: {
              state: current.mapData.readonlyInstance.state,
              object: map,
            },
          },
        }
      })
    },
    vehicleClusterPanel_cluster_mapHasChanged(
      draft,
      {
        payload: { eventValue, clusterId },
      }: PayloadAction<{ eventValue: ChangeEventValue; clusterId: VehicleClusterId }>,
    ) {
      draft.clustersById = draft.clustersById.set(clusterId, (current) => {
        if (!current) {
          return initialClusterData
        }

        return {
          ...current,
          mapData: {
            ...current.mapData,
            zoom: eventValue.zoom,
            center: eventValue.center,
            size: eventValue.size,
            readonlyInstance: {
              ...current.mapData.readonlyInstance,
              state: eventValue,
            },
          },
        }
      })
    },
    vehicleClusterPanel_cluster_clickedUICluster(
      draft,
      {
        payload: { points, clusterId },
      }: PayloadAction<{ points: Points; clusterId: VehicleClusterId }>,
    ) {
      draft.clustersById = draft.clustersById.set(clusterId, (current) => {
        if (!current) {
          return initialClusterData
        }
        if (
          !current.mapData.readonlyInstance.object ||
          R.isNullish(current.mapData.size)
        ) {
          return initialClusterData
        }

        const { center, zoom } = clusterMapAreaUtils.getBoundsFromWithExtraPadding({
          mapObject: current.mapData.readonlyInstance.object,
          mapSize: current.mapData.size,
          pathItems: points,
        })

        return {
          ...current,
          mapData: {
            ...current.mapData,
            center,
            zoom,
          },
        }
      })
    },
    vehicleClusterPanel_cluster_clickedZoomInButton(
      draft,
      { payload: { clusterId } }: PayloadAction<{ clusterId: VehicleClusterId }>,
    ) {
      draft.clustersById = draft.clustersById.set(clusterId, (current) => {
        if (!current) {
          return initialClusterData
        }

        return {
          ...current,
          mapData: {
            ...current.mapData,
            isFollowingVehicles: false,
            zoom: clusterMapAreaUtils.clickedZoomInButton(current.mapData.zoom),
          },
        }
      })
    },
    vehicleClusterPanel_cluster_clickedZoomOutButton(
      draft,
      { payload: { clusterId } }: PayloadAction<{ clusterId: VehicleClusterId }>,
    ) {
      draft.clustersById = draft.clustersById.set(clusterId, (current) => {
        if (!current) {
          return initialClusterData
        }

        return {
          expanded: current.expanded,
          mapData: {
            ...current.mapData,
            isFollowingVehicles: false,
            zoom: clusterMapAreaUtils.clickedZoomOutButton(current.mapData.zoom),
          },
        }
      })
    },
    onVehicleClusterPanelSectionHeaderClick(
      draft,
      { payload: { clusterId } }: PayloadAction<{ clusterId: VehicleClusterId }>,
    ) {
      draft.clustersById = draft.clustersById.set(clusterId, (current) => {
        if (!current) {
          return initialClusterData
        }

        return {
          ...current,
          expanded: !current.expanded,
        }
      })
    },
    onVehicleClusterDeleteSuccess(
      state,
      {
        payload: { clustersAfterDeletion },
      }: PayloadAction<{
        clustersAfterDeletion: ReadonlyDeep<Array<VehicleCluster>> | undefined
      }>,
    ): State {
      if (clustersAfterDeletion !== undefined && clustersAfterDeletion.length === 0) {
        return closedState
      }
      return state
    },
    onVehiclePositionsUpdate(
      state,
      {
        payload: { vehiclePositionsByVehicleId, clustersById },
      }: PayloadAction<{
        vehiclePositionsByVehicleId: FetchVehiclePositionsResolved['vehiclePositionsByVehicleId']
        clustersById: Map<VehicleClusterId, VehicleCluster>
      }>,
    ) {
      const newClustersById = ImmutableDict<VehicleClusterId, ClusterData>(
        Obj_mapWithKey(state.clustersById.asObject(), (clusterId, current) => {
          const cluster = clustersById.get(clusterId)
          if (!current) {
            return initialClusterData
          }

          if (!current.mapData.readonlyInstance.object || !cluster) {
            return current
          }

          const newFocusState = generateClusterMapFocusStateFrom({
            clusterVehicleIds: cluster.vehicles.map((v) => v.id),
            currentFocusState: current.mapData,
            vehiclePositionsByVehicleId,
            currentIsFollowingVehicles: current.mapData.isFollowingVehicles,
            mapObject: current.mapData.readonlyInstance.object,
          })

          return {
            ...current,
            mapData: {
              ...current.mapData,
              ...newFocusState,
            },
          }
        }),
      )

      return {
        ...state,
        clustersById: newClustersById,
      }
    },
  },
  extraReducers: (builder) =>
    builder
      .addCase(
        setVehicleClusterPanelInitialOpenState,
        (state, { payload: { clusterId, initialCenter, initialZoom } }) =>
          generateInitialOpenState(state, {
            clusterIdToExpand: clusterId,
            initialCenter,
            initialZoom,
          }),
      )
      .addCase(setVehicleClusterPanelClosedState, () => closedState),
})

export const {
  onVehicleClusterPanelSectionHeaderClick,
  onVehicleClusterDeleteSuccess,
  onVehiclePositionsUpdate,
  vehicleClusterPanel_cluster_clickedMapTypeButton,
  vehicleClusterPanel_cluster_GoogleApiHasLoaded,
  vehicleClusterPanel_cluster_clickedZoomInButton,
  vehicleClusterPanel_cluster_clickedZoomOutButton,
  vehicleClusterPanel_cluster_mapHasChanged,
  vehicleClusterPanel_cluster_clickedUICluster,
  vehicleClusterPanel_cluster_clickedFollowUpButton,
  vehicleClusterPanel_cluster_mapOnDragEnd,
  vehicleClusterPanel_cluster_mapThrottledOnWheel,
} = slice.actions

export default slice.reducer

const getState = (state: AppState) => state.vehicleClusterPanel

export const getVehicleClusterPanelClustersById = (state: AppState) =>
  getState(state).clustersById
