import { useEffect, useMemo, useState } from 'react'
import { CircularProgressDelayedAbsolute } from '@karoo-ui/core'
import type {
  DefinedQueryObserverResult,
  QueryObserverPlaceholderResult,
} from '@tanstack/react-query'
import type { ChangeEventValue, Coords, Size } from 'google-map-react'
import type { Points } from 'points-cluster'
import { Redirect } from 'react-router'
import * as R from 'remeda'
import { match } from 'ts-pattern'
import { z } from 'zod'

import { getAppMainUrl } from 'duxs/app-routes-selectors'
import { useEventHandlerBranded } from 'src/hooks/useEventHandler'
import { useValidatedSearchParams } from 'src/hooks/useValidatedSearchParams'
import { useTypedSelector } from 'src/redux-hooks'
import type { MapsExtended } from 'src/types/extended/google-maps'
import { Array_filterMap } from 'src/util-functions/performance-critical-utils'

import {
  useRealTimeVehiclePositionsQuery,
  useVehicleClusterDetailsQueryWithRefreshInterval,
  type ClustersDetailsQueryResolved,
  type UseRealTimeVehiclePositionsQueryData,
  type UseRealTimeVehiclePositionsQueryWithDataReturn,
} from '../VehicleCluster/api/queries'
import {
  vehicleClusterIdSchema,
  type VehicleClusterId,
} from '../VehicleCluster/api/types'
import type { NormalizedVehiclePoint } from '../VehicleCluster/types'
import {
  clusterMapAreaUtils,
  useVehicleClusterMapAreaInitialMapOptions,
} from '../VehicleCluster/utils'
import { generateClusterMapFocusStateFrom } from '../VehicleCluster/VehicleClusterPanel/state-utils'
import MapArea from './MapArea'
import type { MapReadonlyInstance } from './types'

type UserEvent =
  | {
      type: 'on_useRealTimeVehiclePositionsQuery_data_change'
      data: UseRealTimeVehiclePositionsQueryData | undefined
    }
  | {
      type: 'onMapGoogleApiLoaded'
      mapObject: MapsExtended.MapObject
    }
  | {
      type: 'clickedMapTypeButton'
      mapTypeId: google.maps.MapTypeId
    }
  | {
      type: 'clickedZoomInButton'
    }
  | {
      type: 'clickedZoomOutButton'
    }
  | {
      type: 'onMapChange'
      eventValue: ChangeEventValue
    }
  | {
      type: 'clickedVehicleCluster'
      info: { points: Points }
    }
  | {
      type: 'clickedFollowUpButton'
      vehiclesPoints: Array<NormalizedVehiclePoint>
    }
  | {
      type: 'onDragEnd'
    }
  | {
      type: 'onThrottledOnWheel'
    }

type MapState = {
  zoom: number
  center: Coords
  mapTypeId: google.maps.MapTypeId
  size: Size | null
  isFollowingVehicles: boolean
}

export const vehicleClustersMapSearchParamsSchema = z.object({
  clusterId: vehicleClusterIdSchema,
})

export default function VehicleClustersMapPageIndex() {
  const validatedParams = useValidatedSearchParams(
    () => vehicleClustersMapSearchParamsSchema,
  )
  const appMainUrl = useTypedSelector(getAppMainUrl)

  if (validatedParams.status === 'invalid') {
    return <Redirect to={appMainUrl} />
  }

  return <VehicleClustersMapPage clusterId={validatedParams.data.clusterId} />
}

function VehicleClustersMapPage({ clusterId }: { clusterId: VehicleClusterId }) {
  const vehicleClusterDetailsQuery = useVehicleClusterDetailsQueryWithRefreshInterval({
    clusterId,
  })

  const initialMapOptions = useVehicleClusterMapAreaInitialMapOptions()

  // _ is used to indicate that _setMapState is not meant to be used directly outside of dispatch
  // eslint-disable-next-line sonarjs/hook-use-state
  const [mapState, _setMapState] = useState<MapState>({
    center: initialMapOptions.center,
    zoom: initialMapOptions.zoom,
    mapTypeId: initialMapOptions.mapTypeId,
    size: null,
    isFollowingVehicles: true,
  })

  // _ is used to indicate that _setMapReadonlyInstance is not meant to be used directly outside of dispatch
  // eslint-disable-next-line sonarjs/hook-use-state
  const [mapReadonlyInstance, _setMapReadonlyInstance] = useState<MapReadonlyInstance>({
    state: null,
    object: null,
  })

  const dispatch = useEventHandlerBranded((event: UserEvent) => {
    match(event)
      .with({ type: 'on_useRealTimeVehiclePositionsQuery_data_change' }, ({ data }) => {
        if (!vehicleClusterDetailsQuery.data || !mapReadonlyInstance.object || !data) {
          return
        }
        const newFocusState = generateClusterMapFocusStateFrom({
          clusterVehicleIds: vehicleClusterDetailsQuery.data.vehicleIds,
          mapObject: mapReadonlyInstance.object,
          currentIsFollowingVehicles: mapState.isFollowingVehicles,
          currentFocusState: { center: mapState.center, zoom: mapState.zoom },
          vehiclePositionsByVehicleId: data.vehiclePositionsByVehicleId,
        })

        _setMapState({ ...mapState, ...newFocusState })
      })
      .with({ type: 'onMapGoogleApiLoaded' }, ({ mapObject }) => {
        _setMapReadonlyInstance((current) => ({
          object: mapObject,
          state: current.state,
        }))
      })
      .with({ type: 'clickedMapTypeButton' }, ({ mapTypeId }) => {
        _setMapState((state) => ({
          ...state,
          mapTypeId,
        }))
      })
      .with({ type: 'clickedZoomInButton' }, () => {
        _setMapState((state) => ({
          ...state,
          isFollowingVehicles: false,
          zoom: clusterMapAreaUtils.clickedZoomInButton(state.zoom),
        }))
      })
      .with({ type: 'clickedZoomOutButton' }, () => {
        _setMapState((state) => ({
          ...state,
          isFollowingVehicles: false,
          zoom: clusterMapAreaUtils.clickedZoomOutButton(state.zoom),
        }))
      })
      .with({ type: 'onMapChange' }, ({ eventValue }) => {
        _setMapState({
          center: eventValue.center,
          mapTypeId: mapState.mapTypeId,
          zoom: eventValue.zoom,
          size: eventValue.size,
          isFollowingVehicles: mapState.isFollowingVehicles,
        })
        _setMapReadonlyInstance((current) => ({
          ...current,
          state: eventValue,
        }))
      })
      .with({ type: 'clickedVehicleCluster' }, ({ info }) => {
        const { points } = info

        if (!mapReadonlyInstance.object || R.isNullish(mapState.size)) {
          return
        }
        const { center, zoom } = clusterMapAreaUtils.getBoundsFromWithExtraPadding({
          pathItems: points,
          mapObject: mapReadonlyInstance.object,
          mapSize: mapState.size,
        })

        _setMapState({
          center,
          mapTypeId: mapState.mapTypeId,
          zoom,
          size: mapState.size,
          isFollowingVehicles: false,
        })
      })
      .with({ type: 'clickedFollowUpButton' }, ({ vehiclesPoints }) => {
        if (!mapReadonlyInstance.object || R.isNullish(mapState.size)) {
          return
        }

        const newIsFollowingVehicles = !mapState.isFollowingVehicles

        const { center, zoom } = newIsFollowingVehicles
          ? clusterMapAreaUtils.getBoundsFromWithExtraPadding({
              mapObject: mapReadonlyInstance.object,
              mapSize: mapState.size,
              pathItems: vehiclesPoints,
            })
          : { center: mapState.center, zoom: mapState.zoom }

        _setMapState({
          ...mapState,
          center,
          zoom,
          isFollowingVehicles: newIsFollowingVehicles,
        })
      })
      .with({ type: 'onDragEnd' }, () => {
        _setMapState({
          ...mapState,
          isFollowingVehicles: false,
        })
      })
      .with({ type: 'onThrottledOnWheel' }, () => {
        _setMapState({
          ...mapState,
          isFollowingVehicles: false,
        })
      })
      .exhaustive()
  })

  const vehiclePositionsQuery = useRealTimeVehiclePositionsQuery({
    vehicleIds: vehicleClusterDetailsQuery.data?.vehicleIds ?? [],
  })

  useEffect(() => {
    dispatch({
      type: 'on_useRealTimeVehiclePositionsQuery_data_change',
      data: vehiclePositionsQuery.data,
    })
  }, [dispatch, vehiclePositionsQuery.data])

  if (vehicleClusterDetailsQuery.data && vehiclePositionsQuery.data) {
    return (
      <Content
        definedClustersDetailsQuery={vehicleClusterDetailsQuery}
        definedVehiclePositionsQuery={vehiclePositionsQuery}
        mapState={mapState}
        mapReadonlyInstance={mapReadonlyInstance}
        dispatch={dispatch}
      />
    )
  }

  if (
    vehicleClusterDetailsQuery.status === 'pending' ||
    vehiclePositionsQuery.status === 'pending'
  ) {
    return <CircularProgressDelayedAbsolute />
  }

  return null
}

const Content = ({
  definedClustersDetailsQuery,
  definedVehiclePositionsQuery,
  dispatch,
  mapState,
  mapReadonlyInstance,
}: {
  definedClustersDetailsQuery:
    | DefinedQueryObserverResult<ClustersDetailsQueryResolved>
    | QueryObserverPlaceholderResult<ClustersDetailsQueryResolved>
  definedVehiclePositionsQuery: UseRealTimeVehiclePositionsQueryWithDataReturn
  dispatch: (action: UserEvent) => void
  mapState: MapState
  mapReadonlyInstance: MapReadonlyInstance
}) => {
  const clustersDetailsQuery = definedClustersDetailsQuery

  const mapAreaThrottledOnWheel = useEventHandlerBranded(() => {
    dispatch({ type: 'onThrottledOnWheel' })
  })

  const vehiclesPoints = useMemo(
    (): Array<NormalizedVehiclePoint> =>
      Array_filterMap(
        clustersDetailsQuery.data.vehicleIds,
        (vehicleId, { RemoveSymbol }) => {
          const position =
            definedVehiclePositionsQuery.data.vehiclePositionsByVehicleId.get(vehicleId)
          if (!position || position.coords === null) {
            return RemoveSymbol
          }

          return {
            id: position.vehicleId,
            lat: position.coords.lat,
            lng: position.coords.lng,
            statusClassName: position.statusClassName,
            bearing: position.bearing,
            gpsFixType: position.gpsFixType,
            type: position.vehicleType,
            registration: position.registration,
          }
        },
      ),
    [
      clustersDetailsQuery.data.vehicleIds,
      definedVehiclePositionsQuery.data.vehiclePositionsByVehicleId,
    ],
  )

  return (
    <MapArea
      height="100%"
      vehicles={vehiclesPoints}
      mapReadonlyInstance={mapReadonlyInstance}
      mapState={mapState}
      onMapGoogleApiLoaded={(mapObject) =>
        dispatch({ type: 'onMapGoogleApiLoaded', mapObject })
      }
      clickedMapTypeButton={(mapTypeId) =>
        dispatch({ type: 'clickedMapTypeButton', mapTypeId })
      }
      clickedZoomInButton={() => dispatch({ type: 'clickedZoomInButton' })}
      clickedZoomOutButton={() => dispatch({ type: 'clickedZoomOutButton' })}
      onMapChange={(eventValue) => dispatch({ type: 'onMapChange', eventValue })}
      clickedVehicleCluster={({ points }) =>
        dispatch({ type: 'clickedVehicleCluster', info: { points } })
      }
      clickedFollowUpButton={() =>
        dispatch({ type: 'clickedFollowUpButton', vehiclesPoints })
      }
      onDragEnd={() => dispatch({ type: 'onDragEnd' })}
      throttledOnWheel={mapAreaThrottledOnWheel}
      viewFullScreen={() => {}}
      viewFullScreenNewTab={() => {}}
    />
  )
}
