import { useCallback, useEffect, useMemo } from 'react'
import type * as React from 'react'
import { difference, flatten, isEmpty, isNil } from 'lodash'
import {
  Box,
  ButtonGroup,
  Collapse,
  DataGrid,
  IconButton,
  Button as MuiButton,
  Stack,
  styled as styledMui,
  Tooltip,
  Typography,
  type ButtonProps,
  type GridRowParams,
  type TypographyProps,
} from '@karoo-ui/core'
import AddRoadIcon from '@mui/icons-material/AddRoad'
import CircleIcon from '@mui/icons-material/Circle'
import DownloadOutlinedIcon from '@mui/icons-material/DownloadOutlined'
import FlagIcon from '@mui/icons-material/Flag'
import FlagOutlinedIcon from '@mui/icons-material/FlagOutlined'
import KeyboardArrowDownOutlinedIcon from '@mui/icons-material/KeyboardArrowDownOutlined'
import KeyboardArrowUpOutlinedIcon from '@mui/icons-material/KeyboardArrowUpOutlined'
import QueryStatsOutlinedIcon from '@mui/icons-material/QueryStatsOutlined'
import RouteOutlinedIcon from '@mui/icons-material/RouteOutlined'
import { DateTime } from 'luxon'
import { rgba } from 'polished'
import { FormattedMessage } from 'react-intl'
import { useDispatch } from 'react-redux'
import { Link } from 'react-router-dom'

import type { FetchTimelineEventsUI } from 'api/timeline/types'
import type { DriverId, VehicleId, VehicleTripId } from 'api/types'
import {
  getTimelineTripsSensors,
  //deleteVehicleTripDetailsAction,
  type getTimelineEventsRaw,
} from 'duxs/timeline'
import {
  getMapZoomOptions,
  getSettings_UNSAFE,
  getShowAverageFuelConsumption,
  getShowTripsFuelConsumptionMetaDataSetting,
  getVisionSetting,
} from 'duxs/user'
import {
  getMapTripDownloadPermission,
  getPreferences,
  getVehiclesViewStatus,
} from 'duxs/user-sensitive-selectors'
import { getVehiclesById } from 'duxs/vehicles'
import { useEffectEvent } from 'src/hooks/useEventHandler'
import DriverWithChip from 'src/modules/app/components/driverChip/DriverWithChip'
import { LIST } from 'src/modules/app/components/routes/list'
import { UserDataGridWithSavedSettingsOnIDB } from 'src/modules/components/connected/UserDataGridWithSavedSettingsOnIDB'
import { UserFormattedPositionAddress } from 'src/modules/components/connected/UserFormattedPositionAddress'
import { useTypedSelector } from 'src/redux-hooks'
import { GA4 } from 'src/shared/google-analytics4'
import ActionIconButton from 'src/util-components/actionIconButton'
import { ctIntl } from 'src/util-components/ctIntl'
import SingleInfo from 'src/util-components/singleInfo'
import TooltipWrap from 'src/util-components/tooltipWrap'

import { FormattedDistance } from 'cartrack-ui-kit'
import { zoomToEvents } from '../actions'
import CameraFootageContainer from './CameraFootageContainer'
import {
  MAP_BOTTOM_PANEL_TABLES_GRID_ID,
  useMapBottomPanelTripTablesContext,
} from './MapBottomPanelTable/MapBottomPanelTripTablesContext'
import type { TripEventWithExtraFields } from './TimelineTables'
import TripDetails from './TripDetails'
import type { TimelineTableTripToSelect } from './types'

type TooltipBlockProps = {
  tooltipMessage: string | number
  value: React.ReactNode | string | number | undefined
  label: React.ReactNode | string
}

const TooltipBlock = ({ tooltipMessage, value, label }: TooltipBlockProps) => (
  <Tooltip
    arrow
    title={ctIntl.formatMessage({
      id:
        typeof tooltipMessage === 'number' ? tooltipMessage.toString() : tooltipMessage,
    })}
    placement="top"
  >
    <StyledSingleInfo>
      <Typography variant="body2">{value ?? '--'}</Typography>
      <Typography
        sx={{ color: 'rgba(0, 0, 0, 0.60)' }}
        variant="caption"
      >
        {typeof label === 'string' ? ctIntl.formatMessage({ id: label }) : label}
      </Typography>
    </StyledSingleInfo>
  </Tooltip>
)

type TripInfo = {
  startTime: DateTime
  endTime: DateTime
  vehicleId: VehicleId
}

type TripUI = FetchTimelineEventsUI.ParsedApiTrip

type Props = {
  onDataExportRequest: (tripId: string) => void
  onTripFlagClick: (trip: TripUI) => void
  onTripSelect: (trip: TimelineTableTripToSelect, index: number) => void
  isSelected: boolean
  distanceInMiles: boolean | undefined
  displayDownload: boolean | undefined
  tripData: Array<TripEventWithExtraFields> | undefined
  dayTripEvents: ReturnType<typeof getTimelineEventsRaw> | undefined
  onTripStatsShow: (tripId: VehicleTripId) => void
  tripUI: TripUI
  vehicleId: VehicleId
  isExpanded: boolean
  toggleTripVisibility: (
    e: React.MouseEvent<HTMLButtonElement, MouseEvent>,
    tripId: VehicleTripId,
  ) => void
  disableDownload: boolean
  index: number
  timelineTripsWithExtraFields: Array<Array<TripEventWithExtraFields>>
  selectedCompactTripIndex: Array<number>
  initialSelectedTripId: string | undefined
  onTableEventRowClick: (eventId: string) => void
}

const TimelineTablesTrip = ({
  onDataExportRequest,
  onTripFlagClick,
  onTripSelect,
  isSelected,
  distanceInMiles = false,
  displayDownload = false,
  tripData = [],
  dayTripEvents = [],
  onTripStatsShow,
  tripUI,
  vehicleId,
  isExpanded,
  toggleTripVisibility,
  index,
  timelineTripsWithExtraFields,
  selectedCompactTripIndex,
  initialSelectedTripId,
  onTableEventRowClick,
}: Props) => {
  const dispatch = useDispatch()
  const { vehicle_enable_max_speed: shouldShowMaxSpeed } =
    useTypedSelector(getSettings_UNSAFE)
  const vehicleById = useTypedSelector(getVehiclesById)
  const selectedVehicle = vehicleById.get(vehicleId)
  const vehiclesViewStatus = useTypedSelector(getVehiclesViewStatus)
  const mapTripDownloadPermission = useTypedSelector(getMapTripDownloadPermission)
  const {
    tripsTablesPreferences: { visibility: tripsVisibility },
  } = useTypedSelector(getPreferences)
  const { maxZoom } = useTypedSelector(getMapZoomOptions)
  const { hasFuelSensors } = useTypedSelector(getTimelineTripsSensors)
  const showTripsFuelConsumptionMetaDataSetting = useTypedSelector(
    getShowTripsFuelConsumptionMetaDataSetting,
  )
  const showAverageFuelConsumption = useTypedSelector(getShowAverageFuelConsumption)
  const { listRoutes } = useTypedSelector(getSettings_UNSAFE)
  const visionSetting = useTypedSelector(getVisionSetting)

  const { gridApiRef, setGridApi, getColumns } = useMapBottomPanelTripTablesContext()

  const onMount = useEffectEvent(() => {
    if (
      !isSelected &&
      initialSelectedTripId &&
      tripUI.tripId === initialSelectedTripId
    ) {
      handleSelectTrip()
    }
  })
  useEffect(() => {
    onMount()
  }, [])

  const getTripDataWithGeofencesWhenSelectMultiple = useCallback(() => {
    let newIndex: Array<number>

    if (
      selectedCompactTripIndex.length === 1 &&
      selectedCompactTripIndex.includes(index)
    ) {
      newIndex = [...selectedCompactTripIndex]
    } else {
      newIndex = selectedCompactTripIndex.includes(index)
        ? difference(selectedCompactTripIndex, [index])
        : [...selectedCompactTripIndex, index]
    }

    const calculatedTripData = timelineTripsWithExtraFields.filter((_, index) =>
      newIndex.includes(index),
    )

    return calculatedTripData
  }, [index, selectedCompactTripIndex, timelineTripsWithExtraFields])

  const handleFlagClick: ButtonProps['onClick'] = (e) => {
    e.stopPropagation()
    if (!isEmpty(tripUI)) {
      onTripFlagClick({ ...tripUI, flagged: !tripUI.flagged })
    }
  }

  const handleSelectTrip = (
    e?: React.MouseEvent<HTMLDivElement | HTMLButtonElement, MouseEvent>,
    viewPathLine = false,
  ) => {
    if (!tripsVisibility.flagged) {
      const isSelectMultiple = !!(e?.ctrlKey || e?.metaKey)

      if (!isExpanded || viewPathLine) {
        const tripToSelect = ((): TimelineTableTripToSelect => {
          if (isSelected && !isSelectMultiple) {
            return null
          }

          return isSelectMultiple
            ? {
                id: tripUI.tripId,
                selectionType: 'multiple' as const,
                events: getTripDataWithGeofencesWhenSelectMultiple(),
                isTripAlreadySelected: isSelected,
              }
            : {
                id: tripUI.tripId,
                selectionType: 'single' as const,
                events: tripData,
              }
        })()

        onTripSelect(tripToSelect, index)

        dispatch(
          zoomToEvents({
            events: (() => {
              if (isSelected && !isSelectMultiple) {
                return dayTripEvents
              }

              return isSelectMultiple
                ? flatten(getTripDataWithGeofencesWhenSelectMultiple())
                : tripData
            })(),
            maxZoom,
          }),
        )
      }
    }
  }

  const defaultTime = '00:00'
  const {
    tripId,
    startTime: startTripDate,
    endTime: endTripDate,
    startLocation,
    startGpsFixType,
    endLocation,
    endGpsFixType,
    maxSpeed = 0,
    totalDistance = 0,
    fuelUsed,
    drivingTime: driving = defaultTime,
    idlingTime: idling = defaultTime,
    ignitionTime: ignition = defaultTime,
    startGeofence,
    endGeofence,
    flagged,
    driverName,
    driverId,
    identificationTag,
  } = tripUI

  const tripDataWithGeofences = tripData

  const info: TripInfo = {
    startTime: DateTime.fromJSDate(startTripDate),
    endTime: DateTime.fromJSDate(endTripDate),
    vehicleId,
  }

  const startTimeAndEndTimeDateDifference = useMemo(() => {
    const startDate = DateTime.fromJSDate(startTripDate).startOf('day')
    const endDate = DateTime.fromJSDate(endTripDate).startOf('day')

    return startDate.isValid && endDate.isValid
      ? endDate.diff(startDate, 'days').as('days')
      : 0
  }, [startTripDate, endTripDate])

  const currentVehicleStatus =
    tripDataWithGeofences.length > 0
      ? tripDataWithGeofences[tripDataWithGeofences.length - 1].statusClassName
      : ''

  const isCurrentTrip = index === 0
  const isTripOngoing = isCurrentTrip && currentVehicleStatus !== 'ignition-off'
  const isMovingIgnitionOff = currentVehicleStatus === 'moving-ignition-off'

  return (
    <Stack
      key={tripId}
      onClick={(e) => handleSelectTrip(e, false)}
    >
      <Section
        isSelected={isSelected}
        sx={({ palette }) => ({
          backgroundColor: isMovingIgnitionOff ? palette.grey[100] : 'transparent',
        })}
      >
        <SectionHeader>
          <SpacedWrap>
            <Stack
              direction="row"
              alignItems="center"
              gap={1}
            >
              <StyledIconButton onClick={handleFlagClick}>
                {flagged ? (
                  <FlagIcon sx={({ palette }) => ({ color: palette.success.main })} />
                ) : (
                  <FlagOutlinedIcon />
                )}
              </StyledIconButton>
              <DriverWithChip
                driverName={{ name: driverName.name, status: driverName.status }}
                driverId={driverId as DriverId}
                linkingMethod={{
                  type: driverName.linkage_type_enum,
                  value: identificationTag,
                }}
                picture={driverName.logo_image_base64}
              />
            </Stack>
            <Stack direction="row">
              {shouldShowMaxSpeed && (
                <TooltipBlock
                  value={
                    <FormattedDistance
                      value={maxSpeed}
                      perTime
                      round
                    />
                  }
                  label={ctIntl.formatMessage({ id: 'Max Speed' })}
                  tooltipMessage={maxSpeed}
                  // tooltipMessage={
                  //   <FormattedDistance
                  //     value={maxSpeed}
                  //     perTime
                  //     round
                  //   />
                  // }
                />
              )}
              {showTripsFuelConsumptionMetaDataSetting &&
                hasFuelSensors &&
                !isNil(fuelUsed) && (
                  <>
                    {!isNil(totalDistance) && showAverageFuelConsumption && (
                      <TooltipBlock
                        value={
                          totalDistance > 0
                            ? (
                                Math.round(
                                  ((fuelUsed + Number.EPSILON) * 10_000) /
                                    totalDistance,
                                ) / 100
                              ).toFixed(2)
                            : 0
                        }
                        label={ctIntl.formatMessage({
                          id: distanceInMiles ? 'L/100Miles' : 'L/100km',
                        })}
                        tooltipMessage={
                          totalDistance > 0
                            ? (
                                Math.round(
                                  ((fuelUsed + Number.EPSILON) * 10_000) /
                                    totalDistance,
                                ) / 100
                              ).toFixed(2)
                            : 0
                        }
                      />
                    )}
                    {/* - fuelUsed can be 0 and must be shown in that case */}
                    <TooltipBlock
                      value={Math.round((fuelUsed + Number.EPSILON) * 100) / 100}
                      label={ctIntl.formatMessage({ id: 'Litres' })}
                      tooltipMessage={fuelUsed}
                    />
                  </>
                )}
              <TooltipBlock
                value={totalDistance}
                label={
                  <FormattedMessage id={distanceInMiles ? 'Miles' : 'Kilometers'} />
                }
                tooltipMessage={totalDistance ?? '--'}
              />
              <TooltipBlock
                value={driving}
                label={ctIntl.formatMessage({ id: 'Driving' })}
                tooltipMessage={driving ?? ''}
              />
              <TooltipBlock
                value={idling}
                label={ctIntl.formatMessage({ id: 'Idling' })}
                tooltipMessage={idling ?? ''}
              />
              <TooltipBlock
                value={ignition}
                label={ctIntl.formatMessage({ id: 'Ignition' })}
                tooltipMessage={ignition ?? ''}
              />
            </Stack>
          </SpacedWrap>
        </SectionHeader>
        <SectionBody>
          <Stack
            justifyContent="space-between"
            direction="row"
          >
            <Box>
              <Grid>
                <GridItemTall>
                  <TripNumber>{tripUI.tripNumber}</TripNumber>
                  <CircleIcon
                    sx={{
                      fontSize: '8px',
                      color: isTripOngoing ? 'primary.main' : 'success.main',
                      order: 3,
                    }}
                  />
                </GridItemTall>
                <GridItem>
                  <TripState variant="subtitle2">
                    {ctIntl.formatMessage({ id: 'Start' })}
                  </TripState>
                </GridItem>
                <GridItem>
                  <TripTime variant="subtitle2">
                    {info.startTime.toFormat('t')}
                  </TripTime>
                </GridItem>
                <GridItem>
                  <TripLocation>
                    <UserFormattedPositionAddress
                      address={startLocation}
                      gpsFixType={startGpsFixType}
                    />
                    {startGeofence && ` (${startGeofence})`}
                  </TripLocation>
                </GridItem>
                <GridItem>
                  <EndTripState
                    isOngoing={isTripOngoing}
                    variant="subtitle2"
                  >
                    {ctIntl.formatMessage({
                      id: isTripOngoing ? 'map.timelineTable.trip.ongoing' : 'End',
                    })}
                  </EndTripState>
                </GridItem>
                <GridItem>
                  <TripTime variant="subtitle2">{`${info.endTime.toFormat('t')}${
                    startTimeAndEndTimeDateDifference > 0
                      ? ` (+${startTimeAndEndTimeDateDifference})`
                      : ''
                  }`}</TripTime>
                </GridItem>
                <GridItem>
                  <TripLocation>
                    <UserFormattedPositionAddress
                      address={endLocation}
                      gpsFixType={endGpsFixType}
                    />
                    {endGeofence && ` (${endGeofence})`}
                  </TripLocation>
                </GridItem>
              </Grid>
              <TripDetails
                trip={tripUI}
                info={{ vehicleId }}
                isTripOngoing={isTripOngoing}
                vehiclesViewStatus={vehiclesViewStatus}
              />
            </Box>
            {visionSetting && selectedVehicle?.isCamera && (
              <CameraFootageContainer tripInfo={info} />
            )}
          </Stack>
        </SectionBody>
        <SectionFooter>
          <SpacedWrap>
            <MuiButton
              color="secondary"
              size="small"
              startIcon={
                isExpanded ? (
                  <KeyboardArrowUpOutlinedIcon />
                ) : (
                  <KeyboardArrowDownOutlinedIcon />
                )
              }
              onClick={(e) => toggleTripVisibility(e, tripId)}
            >
              {ctIntl.formatMessage({
                id: isExpanded ? 'Hide trip history' : 'View trip history',
              })}
            </MuiButton>
            <Stack
              direction="row"
              gap={1}
            >
              {listRoutes && (
                <Link
                  to={{
                    pathname: LIST.subMenusRoutes.ROUTES.subPaths.CREATE_ROUTE,
                    search: `?tripId=${tripId}&vehicle=${info.vehicleId}`,
                    state: { info },
                  }}
                >
                  <MuiButton
                    variant="outlined"
                    size="small"
                    color="secondary"
                    startIcon={<AddRoadIcon />}
                    sx={({ palette }) => ({
                      height: '100%',
                      borderColor: palette.grey['A400'],
                    })}
                  >
                    {ctIntl.formatMessage({ id: 'Add Route' })}
                  </MuiButton>
                </Link>
              )}
              <ButtonGroup>
                <TooltipWrap
                  tooltipMessage={ctIntl.formatMessage({ id: 'View Statistics' })}
                >
                  <ActionIconButton
                    variant="outlined"
                    color="secondary"
                    id={tripId}
                    isHalfPadding
                    onClick={() => {
                      GA4.event({
                        category: 'Timeline Table',
                        action: 'Trip "View Statistics" Button Click',
                      })
                      onTripStatsShow(tripId)
                    }}
                  >
                    <QueryStatsOutlinedIcon />
                  </ActionIconButton>
                </TooltipWrap>
                <TooltipWrap
                  tooltipMessage={ctIntl.formatMessage({
                    id: `${isSelected ? 'Hide' : 'View'} Pathline`,
                  })}
                >
                  <ActionIconButton
                    variant="outlined"
                    color="secondary"
                    id={`2-${tripId}`}
                    isHalfPadding
                    onClick={(e) => {
                      GA4.event({
                        category: 'Timeline Table',
                        action: `Trip "${
                          isSelected ? 'Hide' : 'View'
                        } Pathline" Button Click`,
                      })
                      handleSelectTrip(e, true)
                    }}
                  >
                    <RouteOutlinedIcon />
                  </ActionIconButton>
                </TooltipWrap>
                {displayDownload && (
                  <TooltipWrap
                    tooltipMessage={ctIntl.formatMessage({ id: 'Download Trip' })}
                  >
                    <ActionIconButton
                      variant="outlined"
                      color="secondary"
                      id={tripId}
                      isHalfPadding
                      onClick={(e) => onDataExportRequest(e.currentTarget.id)}
                      disabled={!mapTripDownloadPermission}
                    >
                      <DownloadOutlinedIcon />
                    </ActionIconButton>
                  </TooltipWrap>
                )}
              </ButtonGroup>
            </Stack>
          </SpacedWrap>
        </SectionFooter>
        <Collapse
          in={isExpanded}
          timeout="auto"
          unmountOnExit
          onEntered={() => {
            setGridApi(gridApiRef.current)
          }}
        >
          <TableBox>
            {tripDataWithGeofences.length > 0 ? (
              <UserDataGridWithSavedSettingsOnIDB
                Component={DataGrid}
                apiRef={gridApiRef}
                dataGridId={MAP_BOTTOM_PANEL_TABLES_GRID_ID}
                columns={getColumns({
                  tripStartTime: tripUI.startTime,
                })}
                rows={tripDataWithGeofences}
                onRowClick={({
                  row,
                }: GridRowParams<(typeof tripDataWithGeofences)[number]>) => {
                  onTableEventRowClick(row.id)
                }}
                pagination
                pageSizeOptions={[25, 50, 100]}
                initialState={{
                  pagination: {
                    paginationModel: { pageSize: 25, page: 0 },
                  },
                }}
                slotProps={{
                  pagination: { showFirstButton: true, showLastButton: true },
                }}
              />
            ) : (
              <Typography>{ctIntl.formatMessage({ id: 'No Trip Data' })}</Typography>
            )}
          </TableBox>
        </Collapse>
      </Section>
    </Stack>
  )
}

export default TimelineTablesTrip

const TripNumber = (props: TypographyProps) => (
  <Box
    sx={({ palette }) => ({
      border: `1px solid ${palette.grey[600]}`,
      borderRadius: '50%',
      display: 'flex',
      justifyContent: 'center',
      alignItems: 'center',
      width: '14px',
      height: '14px',
      order: 1,
    })}
  >
    <Typography
      variant="caption"
      lineHeight="normal"
      {...props}
    />
  </Box>
)
const SpacedWrap = styledMui(Stack)(({ theme }) =>
  theme.unstable_sx({
    flexDirection: 'row',
    justifyContent: 'space-between',
    width: '100%',
  }),
)
const Section = styledMui(Stack, {
  shouldForwardProp: (prop) => prop !== 'isSelected',
})<{ isSelected: boolean }>(({ theme, isSelected }) =>
  theme.unstable_sx({
    border: `1px solid ${
      isSelected
        ? `${rgba(theme.palette.primary.main, 0.54)}`
        : theme.palette.action.focus
    }`,
    borderRadius: '4px',
    boxShadow: isSelected
      ? `0px 0px 6px 1px ${rgba(theme.palette.primary.main, 0.3)}`
      : 'none',
  }),
)
const SectionBody = styledMui(Stack)(({ theme }) =>
  theme.unstable_sx({
    paddingX: 2,
    paddingY: 1,
  }),
)
const SectionHeader = styledMui(Stack)(({ theme }) =>
  theme.unstable_sx({
    borderBottom: `1px solid ${theme.palette.grey[200]}`,
    paddingX: 2,
    paddingY: 1,
  }),
)

const SectionFooter = styledMui(Stack)(({ theme }) =>
  theme.unstable_sx({
    borderTop: `1px solid ${theme.palette.grey[200]}`,
    paddingX: 1.5,
    paddingY: 0.5,
  }),
)

const Grid = styledMui(Box)(({ theme }) =>
  theme.unstable_sx({
    display: 'grid',
    gridTemplateColumns: 'repeat(4, minmax(0, auto))',
    width: 'fit-content',
    columnGap: 2,
    rowGap: 1,
  }),
)
const GridItem = styledMui(Box)({
  maxWidth: 'fit-content',
})
const GridItemTall = styledMui(Stack)(({ theme }) =>
  theme.unstable_sx({
    maxWidth: 'fit-content',
    gridColumn: '1',
    gridRow: '1/3',
    alignItems: 'center',
    justifyContent: 'center',
    '&:after': {
      content: '""',
      border: `1px dashed ${theme.palette.grey[500]}`,
      height: '10px',
      order: 2,
    },
  }),
)
const TripTime = (props: TypographyProps) => (
  <Typography
    variant="subtitle2"
    sx={{ color: '#000' }}
    {...props}
  />
)
const TripState = (props: TypographyProps) => (
  <Typography
    variant="subtitle2"
    sx={{ color: 'text/primary', textTransform: 'uppercase' }}
    {...props}
  />
)
const EndTripState = (props: TypographyProps & { isOngoing: boolean }) => {
  const { isOngoing, ...rest } = props
  return (
    <Typography
      variant="subtitle2"
      sx={{
        color: isOngoing ? 'primary.main' : 'success.main',
        textTransform: 'uppercase',
      }}
      {...rest}
    />
  )
}
const TripLocation = (props: TypographyProps) => (
  <Typography
    variant="body2"
    sx={{ color: 'text/secondary' }}
    {...props}
  />
)
const StyledSingleInfo = styledMui(SingleInfo)(({ theme }) =>
  theme.unstable_sx({
    flexDirection: 'row',
    gap: 0.5,
  }),
)
const TableBox = styledMui(Box)(({ theme }) =>
  theme.unstable_sx({
    padding: 2,
    background: '#F3F3F3',
    border: `1px solid ${theme.palette.divider}`,
    height: '500px',
  }),
)

const StyledIconButton = styledMui(IconButton)({
  padding: 0,
})
