import { useState } from 'react'
import { Box, CircularProgressDelayedCentered } from '@karoo-ui/core'
import type { DateTime } from 'luxon'
import { match } from 'ts-pattern'

import { useDriversQuery } from 'src/modules/api/useDriversQuery'

import { MAX_SCORE } from '../../constants'
import {
  useScorecardDefaultConfigurationWeightageQuery,
  type FetchScorecardConfigurationWeightageData,
} from '../../Settings/api/queries'
import type { FetchPeriodGroupScoresData } from '../api/queries'
import ScoreRankingChart from './ScoreRankingChart'
import ScoreRankingDataGrid from './ScoreRankingDataGrid' // Import the new component

type Props = {
  dateRange: [DateTime, DateTime]
  configData: FetchScorecardConfigurationWeightageData
  periodGroupDriversData: FetchPeriodGroupScoresData['drivers']
}

const Ranking = ({ dateRange, configData, periodGroupDriversData }: Props) => {
  const [scoreRange, setScoreRange] = useState<Array<number>>([0, MAX_SCORE])
  const driversQuery = useDriversQuery()

  const defaultConfigQuery = useScorecardDefaultConfigurationWeightageQuery()

  return match(defaultConfigQuery)
    .with({ status: 'success' }, ({ data: defaultConfigData }) =>
      match(driversQuery)
        .with({ status: 'pending' }, () => <CircularProgressDelayedCentered />)
        .with({ status: 'error' }, () => null)
        .with({ status: 'success' }, ({ data }) => {
          const weightageRange = configData.configurations?.weightageCustomize.on
            ? configData.configurations?.weightageCustomize.range
            : (defaultConfigData.configurationRules.weightageCustomize.range ?? [])

          return (
            <Box
              id="Scorecard-driverScores-ranking-page"
              sx={{ gap: 2, display: 'flex', flexDirection: 'column' }}
            >
              <ScoreRankingChart
                queryData={periodGroupDriversData}
                scoreRange={scoreRange}
                setScoreRange={setScoreRange}
                dateRange={dateRange}
                weightageRange={weightageRange}
              />
              {/* Render the data grid below the chart */}
              <ScoreRankingDataGrid
                queryData={periodGroupDriversData}
                dateRange={dateRange}
                scoreRange={scoreRange}
                setScoreRange={setScoreRange}
                driversData={data}
                weightageRange={weightageRange}
              />
            </Box>
          )
        })

        .exhaustive(),
    )
    .with({ status: 'pending' }, () => <CircularProgressDelayedCentered />)
    .with({ status: 'error' }, () => null)
    .exhaustive()
}

export default Ranking
