import { useMemo } from 'react'
import {
  Box,
  DataGrid,
  LinearProgress,
  Switch,
  Typography,
  useDataGridColumnHelper,
  useSearchTextField,
  type GridColDef,
} from '@karoo-ui/core'

import { getSettings_UNSAFE } from 'duxs/user'
import { useUsersQuery } from 'src/modules/api/useUsersQuery'
import { mapRulesToTranslationKeys } from 'src/modules/carpool/utils/helpers'
import { UserDataGridWithSavedSettingsOnIDB } from 'src/modules/components/connected/UserDataGridWithSavedSettingsOnIDB'
import { useTypedSelector } from 'src/redux-hooks'
import { ctIntl } from 'src/util-components/ctIntl'
import IntlTypography from 'src/util-components/IntlTypography'
import {
  generateItemMatchesWithTextAndFilters,
  type Filters,
} from 'src/util-functions/search-utils'

import { useCarpoolRulesQuery, type Rule } from '../../OLD_Settings/Rules/api/queries'
import { RULE_TYPES } from '../../utils/constants'
import { useRuleMutation } from './api/mutations'

type DataGridRow = Rule

const Notifications = () => {
  const { carpoolAppName } = useTypedSelector(getSettings_UNSAFE)
  const searchProps = useSearchTextField('')
  const columnHelper = useDataGridColumnHelper<DataGridRow>({ filterMode: 'client' })

  const carpoolRulesQuery = useCarpoolRulesQuery()
  const updateRuleMutation = useRuleMutation()
  const updateRuleMutate = updateRuleMutation.mutate
  const userQuery = useUsersQuery()

  const columnsGetters = useMemo(
    () => ({
      bookingRule: (rule: Rule) =>
        rule.translationKey
          ? ctIntl.formatMessage({
              id: mapRulesToTranslationKeys(rule.translationKey),
            })
          : rule.bookingRule,
      status: (rule: Rule) => rule.status,
    }),
    [],
  )

  const initialSortCommRules = useMemo(() => {
    if (carpoolRulesQuery.data === undefined) {
      return []
    }
    return carpoolRulesQuery.data.sort((a, b) => a.id - b.id)
  }, [carpoolRulesQuery.data])

  const filteredCommRules = useMemo((): Array<DataGridRow> => {
    const searchFilters: Filters<Rule> = {
      search: [columnsGetters.bookingRule],
    }
    const { itemMatchesWithTextAndFilters } = generateItemMatchesWithTextAndFilters(
      searchProps.value,
    )
    return initialSortCommRules.filter(
      (rule: Rule) =>
        RULE_TYPES.ALERTS.includes(rule.id) &&
        itemMatchesWithTextAndFilters(rule, searchFilters),
    )
  }, [columnsGetters.bookingRule, searchProps.value, initialSortCommRules])

  const columns = useMemo(
    (): Array<GridColDef<DataGridRow>> => [
      columnHelper.string((_, row) => columnsGetters.bookingRule(row), {
        field: 'reason',
        headerName: ctIntl.formatMessage({ id: 'Alert' }),
        flex: 3,
      }),
      columnHelper.boolean((_, row) => columnsGetters.status(row), {
        align: 'left',
        headerAlign: 'left',
        field: 'status',
        headerName: ctIntl.formatMessage({ id: 'Status' }),
        renderCell: ({ row }) => (
          <>
            <Switch
              defaultChecked={row.status}
              checked={row.status}
              onChange={(e) => {
                updateRuleMutate({
                  id: row.id,
                  status: e.target.checked,
                  value: row.value,
                  unit: row.unit,
                })
              }}
            />
            <Typography>
              {row.status
                ? ctIntl.formatMessage({ id: 'Active' })
                : ctIntl.formatMessage({ id: 'Inactive' })}
            </Typography>
          </>
        ),
        flex: 1,
      }),
    ],
    [columnHelper, columnsGetters, updateRuleMutate],
  )

  return (
    <Box
      sx={{
        display: 'flex',
        flexFlow: 'column',
        width: '100%',
        height: '100%',
        gap: 2,
        p: 2,
      }}
    >
      <Typography variant="h5">
        {`${carpoolAppName} ${ctIntl.formatMessage({ id: 'Notifications' })}`}
      </Typography>

      <IntlTypography
        sx={{ color: 'text.secondary' }}
        msgProps={{ id: 'carpool.notifications.subtitle' }}
      />
      <UserDataGridWithSavedSettingsOnIDB
        Component={DataGrid}
        sx={{ '& .MuiDataGrid-row': { cursor: 'pointer' } }}
        RootPaperProps={{ sx: { p: 2 } }}
        dataGridId="carpool-settings-notifications-rules"
        disableVirtualization
        loading={
          carpoolRulesQuery.isPending ||
          updateRuleMutation.isPending ||
          userQuery.fetchStatus === 'fetching'
        }
        autoPageSize
        slots={{ loadingOverlay: LinearProgress }}
        columns={columns}
        rows={filteredCommRules}
        pagination
        showToolbar={false}
        disableRowSelectionOnClick
      />
    </Box>
  )
}

export default Notifications
