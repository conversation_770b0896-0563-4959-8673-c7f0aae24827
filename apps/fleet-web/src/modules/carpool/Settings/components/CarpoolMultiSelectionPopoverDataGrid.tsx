import { DataGrid, type DataGridProps, type GridValidRowModel } from '@karoo-ui/core'
import * as R from 'remeda'
import type { Except } from 'type-fest'

export type CarpoolMultiSelectionPopoverDataGridProps<R extends GridValidRowModel> =
  Except<
    DataGridProps<R>,
    'RootPaperProps' | 'density' | 'disableColumnResize' | 'hideFooter'
  > & { dataGridId: string }

export const CarpoolMultiSelectionPopoverDataGrid = <R extends GridValidRowModel>({
  sx = [],
  dataGridId,
  ...props
}: CarpoolMultiSelectionPopoverDataGridProps<R>) => (
  <DataGrid<R>
    {...props}
    data-testid={dataGridId}
    RootPaperProps={{
      sx: { borderBottom: '1px solid var(--mui-palette-TableCell-border)' },
      square: true,
      elevation: 0,
    }}
    density="compact"
    disableColumnResize
    showToolbar={false}
    hideFooter
    sx={[
      {
        '& .MuiDataGrid-columnSeparator': { display: 'none' },
        '& .MuiDataGrid-columnHeader--last': { pr: 2 },
        '& .MuiDataGrid-cell:last-child': { pr: 2 },
        // Only apply the custom padding on non checkbox columns (they already contain the correct padding)
        '& .MuiDataGrid-columnHeader:not([data-field="__check__"]):not(:last-child)': {
          pl: 0,
        },
        '& .MuiDataGrid-cell:not([data-field="__check__"]):not(:last-child)': {
          pl: 0,
        },
        '& .MuiDataGrid-row': { cursor: 'pointer' },
      },
      ...(R.isArray(sx) ? sx : [sx]),
    ]}
  />
)
