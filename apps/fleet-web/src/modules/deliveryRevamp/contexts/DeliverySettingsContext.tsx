import {
  createContext,
  useCallback,
  useContext,
  useEffect,
  useMemo,
  useState,
} from 'react'
import type { UseMutateFunction } from '@tanstack/react-query'

import { getIsSubUser, getSettings } from 'duxs/user-sensitive-selectors'
import ConfirmationModal from 'src/components/_modals/Confirmation'
import { enqueueSnackbarWithCloseAction } from 'src/components/Snackbar/Notistack/utils'
import { DELIVERY_SYNC_DATA_INTERVAL } from 'src/modules/deliveryRevamp/api/constants'
import { useTypedSelector } from 'src/redux-hooks'
import type { FixMeAny } from 'src/types/global'
import { ctIntl } from 'src/util-components/ctIntl'

import { deliveryUserSettingsDefault } from '../api/settings/constants'
import type { SettingsData } from '../api/settings/types'
import useDeliverySettingsQuery, {
  type DeliverySettings,
} from '../api/settings/useDeliverySettingsQuery'
import {
  useUpdateDeliverySettingsMutation,
  type UpdateDeliverySettings,
} from '../api/settings/useUpdateDeliverySettingsMutation'
import { SUB_USER_ACCESS_SCOPE } from '../constants/app-setting'
import {
  DELIVERY_DEFAULT_DRIVER_LABEL,
  FIELD_SERVICE_DEFAULT_DRIVER_LABEL,
} from '../constants/worker'

type DeliverySettingsData = DeliverySettings.Return & {
  fieldService: boolean
}

export type DeliverySettingsContext = {
  deliverySettings: DeliverySettingsData
  localDesignStyleCurrent: DeliverySettingsData['designStyleCurrent'] | undefined
  setLocalDesignStyleCurrent: (
    designStyleCurrent: DeliverySettingsData['designStyleCurrent'],
  ) => void
  areDeliverySettingsLoading: boolean
  updateDeliverySettings: UseMutateFunction<
    UpdateDeliverySettings.ApiOutput,
    Error,
    UpdateDeliverySettings.MutationInput,
    { previousSettings: unknown }
  >
  areDeliverySettingsUpdating: boolean
  isUpdatingSettingsByKeys: (keys: Array<keyof SettingsData>) => boolean
}

const DeliverySettingsContext = createContext<DeliverySettingsContext | null>(null)

export const DeliverySettingsProvider = ({
  children,
}: {
  children: React.ReactNode
}) => {
  const [isDriverLimitAlertShown, setIsDriverLimitAlertShown] = useState(false)
  const [updatingKeys, setUpdatingKeys] = useState<Array<keyof SettingsData>>([])
  const [localDesignStyleCurrent, setLocalDesignStyleCurrent] =
    useState<DeliverySettings.Return['designStyleCurrent']>()

  const isSubUser = useTypedSelector(getIsSubUser)
  const { fieldService } = useTypedSelector(getSettings)

  const deliverySettingsQuery = useDeliverySettingsQuery({
    refetchInterval: DELIVERY_SYNC_DATA_INTERVAL,
    refetchOnWindowFocus: true,
    structuralSharing: ((
      pre: DeliverySettings.Return | undefined,
      curr: DeliverySettings.Return,
    ): DeliverySettings.Return => {
      if (
        isSubUser &&
        pre &&
        pre.subusersDataAccessScope !== curr.subusersDataAccessScope
      ) {
        enqueueSnackbarWithCloseAction(
          ctIntl.formatMessage({
            id:
              curr.subusersDataAccessScope === SUB_USER_ACCESS_SCOPE.ALL
                ? 'You now have full access to the system'
                : 'You now have limited access to the system',
          }),
          {
            variant: 'warning',
          },
        )
      }

      if (!pre?.designStyleCurrent) {
        // first load
        setLocalDesignStyleCurrent(curr.designStyleCurrent)
      }

      if (!pre) {
        // first load
        const { contractDriversEnabled, contractMaxDriversEnabled } = curr
        if (
          +contractDriversEnabled > +contractMaxDriversEnabled &&
          +contractMaxDriversEnabled > 0
        ) {
          setIsDriverLimitAlertShown(true)
        }
      }

      return curr
    }) as FixMeAny,
  })
  const { mutate, isPending } = useUpdateDeliverySettingsMutation()

  const updateDeliverySettings = useCallback(
    (
      newSettings: Parameters<typeof mutate>[0],
      options?: Parameters<typeof mutate>[1],
    ) => {
      setUpdatingKeys(newSettings.map((setting) => setting.key))
      return mutate(newSettings, {
        ...options,
        onSuccess: (...args) => {
          setUpdatingKeys([])
          options?.onSuccess?.(...args)
        },
        onError: (...args) => {
          setUpdatingKeys([])
          options?.onError?.(...args)
        },
      })
    },
    [mutate],
  )

  useEffect(() => {
    if (deliverySettingsQuery.data) {
      const { designStyleCurrent } = deliverySettingsQuery.data

      if (!localDesignStyleCurrent) {
        setLocalDesignStyleCurrent(designStyleCurrent)
      }
    }
  }, [deliverySettingsQuery.data, localDesignStyleCurrent])

  const memoizedData = useMemo(
    () => ({
      deliverySettings: {
        ...deliveryUserSettingsDefault,
        ...deliverySettingsQuery.data,
        fieldService: fieldService as boolean,
        driverLabel:
          deliverySettingsQuery.data?.driverLabel ||
          (fieldService
            ? FIELD_SERVICE_DEFAULT_DRIVER_LABEL
            : DELIVERY_DEFAULT_DRIVER_LABEL),
      },
      localDesignStyleCurrent,
      setLocalDesignStyleCurrent,
      areDeliverySettingsLoading: deliverySettingsQuery.isLoading,
      updateDeliverySettings,
      areDeliverySettingsUpdating: isPending,
      isUpdatingSettingsByKeys: (keys: Array<keyof SettingsData>) =>
        isPending && keys.some((key) => updatingKeys.includes(key)),
    }),
    [
      deliverySettingsQuery.data,
      deliverySettingsQuery.isLoading,
      fieldService,
      localDesignStyleCurrent,
      updateDeliverySettings,
      isPending,
      updatingKeys,
    ],
  )

  return (
    <DeliverySettingsContext.Provider value={memoizedData}>
      <>
        {isDriverLimitAlertShown && (
          <ConfirmationModal
            title="Upgrade needed"
            open
            onClose={() => setIsDriverLimitAlertShown(false)}
            onConfirm={() => {
              setIsDriverLimitAlertShown(false)
            }}
          >
            {ctIntl.formatMessage(
              {
                id: 'You have reached the maximum driver limit ({count}) on your account. Please disable drivers or contact your account manager to upgrade your package.',
              },
              {
                values: {
                  count: deliverySettingsQuery.data?.contractMaxDriversEnabled,
                },
              },
            )}
          </ConfirmationModal>
        )}

        {children}
      </>
    </DeliverySettingsContext.Provider>
  )
}

export const useDeliverySettingsContext = () => {
  const context = useContext(DeliverySettingsContext)

  if (!context) {
    throw new Error(
      'useDeliverySettingsContext must be used within a DeliverySettingsProvider',
    )
  }

  return context
}
