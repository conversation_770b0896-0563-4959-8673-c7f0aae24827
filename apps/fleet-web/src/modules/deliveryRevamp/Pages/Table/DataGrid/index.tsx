import { use<PERSON><PERSON>back, useEffect, useMemo, useState } from 'react'
import {
  DataGrid,
  getDataGridClientModeIncludedSelectedRowIds,
  GRID_CHECKBOX_SELECTION_FIELD,
  GridRow,
  Stack,
  use<PERSON><PERSON>backB<PERSON>ed,
  useGrid<PERSON><PERSON>R<PERSON>,
  type <PERSON><PERSON>ang<PERSON>,
  type GridEventListener,
  type GridFilterModel,
  type GridPaginationModel,
  type GridRowModel,
  type GridRowProps,
  type GridRowSelectionModel,
  type GridSortModel,
} from '@karoo-ui/core'
import type { DateTime } from 'luxon'
import { rgba } from 'polished'
import { useDispatch } from 'react-redux'
import { difference } from 'remeda'
import { match } from 'ts-pattern'

import DataStatePlaceholder from 'src/components/_data/Placeholder'
import { UserDataGridWithSavedSettingsOnIDB } from 'src/modules/components/connected/UserDataGridWithSavedSettingsOnIDB'
import { DELIVERY_SYNC_DATA_INTERVAL } from 'src/modules/deliveryRevamp/api/constants'
import ExpandableSearchField from 'src/modules/deliveryRevamp/components/ExpandableSearchField'
import { ASSIGN_STATUS } from 'src/modules/deliveryRevamp/components/Filters/filtersSchemas'
import JobActionsMenu from 'src/modules/deliveryRevamp/components/MapPanel/components/JobDetails/EditJob/ActionsMenu'
import { GRID_STYLE_TO_DATA_GRID_DENSITY } from 'src/modules/deliveryRevamp/constants/appearance'
import { JOB_STATUS_ID, STOP_STATUS_ID } from 'src/modules/deliveryRevamp/constants/job'
import { useDeliverySettingsContext } from 'src/modules/deliveryRevamp/contexts/DeliverySettingsContext'
import useClickOrSelectJobs from 'src/modules/deliveryRevamp/hooks/useClickOrSelectJobs'
import { setCompletedJobIds, setStartedJobIds } from 'src/modules/deliveryRevamp/slice'
import { DeliveryDateTime } from 'src/modules/deliveryRevamp/utils/deliveryDateTime'
import KarooToolbar from 'src/shared/data-grid/KarooToolbar'
import { ctIntl } from 'src/util-components/ctIntl'

import useStopsTableListQuery, {
  type FetchStopsTableListQuery,
} from '../../../api/jobs/useStopsTableListQuery'
import type { DeliveryDataGridRow } from './columns/types'
import { useColumns } from './columns/useColumns'
import AssignmentFilter from './components/AssignmentFilter'
import CounterFilter from './components/CounterFilter'
import DriversFilter from './components/DriversFilter'
import GroupStopsToggler from './components/GroupStops'
import ReportsDownload from './components/ReportsDownload'
import StatusFilter from './components/StatusFilter'
import type { ColumnFields, FilterableFields } from './types'
import useDataGridFiltersContext, {
  PAGE_SIZE_OPTIONS,
  type DataGridSortModel,
  type FilterModelFilters,
} from './useDataGridFiltersContext'

const DeliveryDataGrid = () => {
  const gridApiRef = useGridApiRef()
  const dispatch = useDispatch()

  // We need to control the meta to avoid resetting the pagination model while the query is loading
  const [tableMeta, setTableMeta] = useState<FetchStopsTableListQuery.Return['meta']>({
    currentPage: 0,
    lastPage: 0,
    perPage: 0,
    totalCompleted: 0,
    totalRejected: 0,
    totalRows: 0,
    totalUnassigned: 0,
  })

  const [contextMenu, setContextMenu] = useState<{
    position: {
      mouseX: number
      mouseY: number
    }
    stop: DeliveryDataGridRow
  } | null>(null)

  const {
    deliverySettings: { appearanceTableViewDensity, driverTrackLocations },
  } = useDeliverySettingsContext()

  const [density, setDensity] = useState(
    GRID_STYLE_TO_DATA_GRID_DENSITY[appearanceTableViewDensity],
  )
  const {
    selectedTableJobIds,
    selectJobs,
    clickJob,
    handleSetIsSelectingUnassignedJob,
    handleSetIsSelectingUnassignableJob,
    handleSetIsSelectingJobsWithDifferentDates,
    handleSetIsSelectingCanceledJob,
    resetSelectedJobIds,
    handleSetIsSelectingRejectedJob,
  } = useClickOrSelectJobs()

  const {
    filters,
    queryInput,
    sortModel,
    paginationModel,
    setFilters,
    setSortModel,
    setPaginationModel,
  } = useDataGridFiltersContext()

  const onPaginationModelChange = useCallback(
    (newPaginationModel: GridPaginationModel) => {
      setPaginationModel(newPaginationModel)
    },
    [setPaginationModel],
  )

  const jobsTableQuery = useStopsTableListQuery(queryInput, {
    refetchInterval: DELIVERY_SYNC_DATA_INTERVAL,
  })

  useEffect(() => {
    if (jobsTableQuery.data) {
      setTableMeta(jobsTableQuery.data.meta)

      const startedJobIds: Array<number> = []
      const completedJobIds: Array<number> = []

      jobsTableQuery.data.stops.map((stop) => {
        if ([JOB_STATUS_ID.COMPLETED].includes(stop.jobStatusId)) {
          completedJobIds.push(stop.jobId)
        } else if (
          [
            STOP_STATUS_ID.STARTED,
            STOP_STATUS_ID.ARRIVED,
            STOP_STATUS_ID.COMPLETED,
          ].includes(stop.stopStatusId)
        ) {
          startedJobIds.push(stop.jobId)
        }
      })
      dispatch(setStartedJobIds(startedJobIds))
      dispatch(setCompletedJobIds(completedJobIds))
    }
  }, [dispatch, jobsTableQuery.data])

  const rows = useMemo(() => jobsTableQuery.data?.stops ?? [], [jobsTableQuery.data])

  const rowsGroupedByStopId = useMemo(
    () => Object.groupBy(rows, (row) => row.stopId),
    [rows],
  )

  const jobIdsToStopIdsMap = useMemo(() => {
    const map = new Map<
      DeliveryDataGridRow['jobId'],
      Array<DeliveryDataGridRow['stopId']>
    >()

    for (const row of rows) {
      const stopIds = map.get(row.jobId) ?? []
      stopIds.push(row.stopId)
      map.set(row.jobId, stopIds)
    }

    return map
  }, [rows])

  useEffect(() => {
    let isSelectingUnassignedJob = false
    let isSelectingUnassignableJob = false
    let isSelectingJobsWithDifferentDates = false
    let isSelectingCanceledJob = false
    let isSelectingRejectedJob = false

    let foundScheduledDeliveryDateTime: DateTime | undefined = undefined

    for (const jobId of selectedTableJobIds) {
      const stopIds = jobIdsToStopIdsMap.get(jobId) ?? []

      if (stopIds.length > 0) {
        for (const stopId of stopIds) {
          const stops = rowsGroupedByStopId[stopId]

          if (stops) {
            for (const stop of stops) {
              // Handle unassigned job selected
              if (stop.jobStatusId === JOB_STATUS_ID.ASSIGN_LATER) {
                isSelectingUnassignedJob = true
              } else if (stop.jobStatusId === JOB_STATUS_ID.COMPLETED) {
                // Handle unassignable job selected
                isSelectingUnassignableJob = true
              } else if (stop.jobStatusId === JOB_STATUS_ID.CANCELED) {
                isSelectingCanceledJob = true
              } else if (stop.jobStatusId === JOB_STATUS_ID.REJECTED) {
                isSelectingRejectedJob = true
              }

              // Handle jobs with different dates selected
              if (foundScheduledDeliveryDateTime !== undefined) {
                if (stop.deliveryDate !== null) {
                  const stopScheduledDeliveryDateTime = DeliveryDateTime.fromJSDate(
                    stop.deliveryDate.date,
                  )

                  if (
                    !stopScheduledDeliveryDateTime.hasSame(
                      foundScheduledDeliveryDateTime,
                      'day',
                    )
                  ) {
                    isSelectingJobsWithDifferentDates = true
                  }
                } else {
                  isSelectingJobsWithDifferentDates = true
                }
              } else if (stop.deliveryDate !== null) {
                foundScheduledDeliveryDateTime = DeliveryDateTime.fromJSDate(
                  stop.deliveryDate.date,
                )
              }

              if (
                isSelectingUnassignedJob &&
                isSelectingUnassignableJob &&
                isSelectingJobsWithDifferentDates &&
                isSelectingCanceledJob
              ) {
                handleSetIsSelectingUnassignedJob(true)
                handleSetIsSelectingUnassignableJob(true)
                handleSetIsSelectingJobsWithDifferentDates(true)
                handleSetIsSelectingCanceledJob(true)
                return
              }
            }
          }
        }
      }
    }

    handleSetIsSelectingUnassignedJob(isSelectingUnassignedJob)
    handleSetIsSelectingUnassignableJob(isSelectingUnassignableJob)
    handleSetIsSelectingJobsWithDifferentDates(isSelectingJobsWithDifferentDates)
    handleSetIsSelectingCanceledJob(isSelectingCanceledJob)
    handleSetIsSelectingRejectedJob(isSelectingRejectedJob)
  }, [
    rowsGroupedByStopId,
    selectedTableJobIds,
    jobIdsToStopIdsMap,
    handleSetIsSelectingUnassignedJob,
    handleSetIsSelectingUnassignableJob,
    handleSetIsSelectingJobsWithDifferentDates,
    handleSetIsSelectingCanceledJob,
    handleSetIsSelectingRejectedJob,
  ])

  // Handle job hover
  useEffect(() => {
    if (!gridApiRef.current) {
      return
    }
    if (gridApiRef.current.subscribeEvent) {
      const handleRowMouseEnter: GridEventListener<'rowMouseEnter'> = (params) => {
        // Remove hover class from all rows first
        const allHoveredRows = document.querySelectorAll('.MuiDataGrid-row--hover')
        for (const element of allHoveredRows) {
          element.classList.remove('MuiDataGrid-row--hover')
        }

        const row: DeliveryDataGridRow = params.row
        // Apply hover class to all rows with the same jobId
        const elements = document.querySelectorAll(`[data-job-id="${row.jobId}"]`)
        for (const element of elements) {
          element.classList.add('MuiDataGrid-row--hover')
        }
      }

      const handleRowMouseLeave: GridEventListener<'rowMouseLeave'> = (params) => {
        const row: DeliveryDataGridRow = params.row
        // Remove hover class from all rows with the same jobId
        const elements = document.querySelectorAll(`[data-job-id="${row.jobId}"]`)
        for (const element of elements) {
          element.classList.remove('MuiDataGrid-row--hover')
        }
      }

      const unsubscribeMouseEnter = gridApiRef.current.subscribeEvent(
        'rowMouseEnter',
        handleRowMouseEnter,
      )
      const unsubscribeMouseLeave = gridApiRef.current.subscribeEvent(
        'rowMouseLeave',
        handleRowMouseLeave,
      )

      return () => {
        unsubscribeMouseEnter()
        unsubscribeMouseLeave()
      }
    }

    return
  }, [gridApiRef, jobIdsToStopIdsMap])

  const columns = useColumns()

  const initialColumnVisibilityModel = useMemo<
    Partial<Record<keyof DeliveryDataGridRow, boolean>>
  >(
    () => ({
      jobId: false,
      creationTime: false,
      completedTime: false,
      labels: false,
      scheduleTravelDistance: false,
      scheduledTravelTime: false,
      actualDistance: driverTrackLocations,
      travelDistancePercentage: driverTrackLocations,
      actualTravelTime: driverTrackLocations,
      travelTimePercentage: driverTrackLocations,
    }),
    [driverTrackLocations],
  )

  const resetTablePagination = useCallback(() => {
    setPaginationModel((prev) => ({ ...prev, page: 0 }))
    setTableMeta((prev) => ({ ...prev, currentPage: 0 }))
  }, [setPaginationModel])

  const onFilterModelChange = useCallback(
    async (newFilterModel: GridFilterModel) => {
      const filterValuesByField = newFilterModel.items.reduce(
        (acc, item) => {
          acc[item.field as FilterableFields] = item.value
          return acc
        },
        {} as FilterModelFilters & {
          creationTs: DateRange<DateTime> | undefined
          actualArrivalTs: DateRange<DateTime> | undefined
          completedTs: DateRange<DateTime> | undefined
          scheduledArrivalTs: DateRange<DateTime> | undefined
        },
      )

      const parseDateRangeFilter = (
        dateRange: DateRange<DateTime> | undefined,
      ): FilterModelFilters['creationTs'] => {
        if (!dateRange) return undefined

        const [start, end] = dateRange

        if (!start) return undefined

        return {
          from: start.toISO({ suppressMilliseconds: true }),
          to:
            end !== null
              ? end
                  .endOf('day')
                  .set({ millisecond: 0 })
                  .toISO({ suppressMilliseconds: true })
              : start
                  .plus({ years: 1 })
                  .endOf('day')
                  .set({ millisecond: 0 })
                  .toISO({ suppressMilliseconds: true }),
        }
      }

      setFilters({
        referenceNumber: filterValuesByField.referenceNumber,
        customerName: filterValuesByField.customerName,
        driverName: filterValuesByField.driverName,
        planName: filterValuesByField.planName,
        searchInput: filterValuesByField.searchInput,
        labels: filterValuesByField.labels,
        creationTs: parseDateRangeFilter(filterValuesByField.creationTs),
        actualArrivalTs: parseDateRangeFilter(filterValuesByField.actualArrivalTs),
        completedTs: parseDateRangeFilter(filterValuesByField.completedTs),
        scheduledArrivalTs: parseDateRangeFilter(
          filterValuesByField.scheduledArrivalTs,
        ),
      })

      resetTablePagination()
      resetSelectedJobIds('Table')
    },
    [setFilters, resetTablePagination, resetSelectedJobIds],
  )

  const onSortModelChange = useCallback(
    (_newSortModel: GridSortModel) => {
      const newSortModel = _newSortModel as DataGridSortModel

      if (newSortModel.length === 0) {
        setSortModel([])
        return
      }

      const lastSortItem = newSortModel[newSortModel.length - 1]

      resetTablePagination()
      setSortModel([lastSortItem])
    },
    [resetTablePagination, setSortModel],
  )

  const selectedRowIds = useMemo(() => {
    const selectedStopIds = new Set<DeliveryDataGridRow['stopId']>()

    for (const jobId of selectedTableJobIds) {
      const stopIds = jobIdsToStopIdsMap.get(jobId) ?? []
      for (const stopId of stopIds) {
        selectedStopIds.add(stopId)
      }
    }

    return selectedStopIds
  }, [selectedTableJobIds, jobIdsToStopIdsMap])

  const handleRowSelectionModelChange = useCallback(
    (_selectionModel: GridRowSelectionModel) => {
      const selectionModelIds = Array.from(
        getDataGridClientModeIncludedSelectedRowIds(
          _selectionModel as GridRowSelectionModel<DeliveryDataGridRow['stopId']>,
          { gridApiRef },
        ),
      )
      const selectedRowIdsArray = Array.from(selectedRowIds)

      if (selectionModelIds.length === 0) {
        return selectJobs([], 'Table')
      }

      const newSelectedTableJobIds = new Set(selectedTableJobIds)

      const newSelectedStopIds = difference(selectionModelIds, selectedRowIdsArray)

      // New id selected
      if (newSelectedStopIds.length === 1) {
        const newSelectedStopId = newSelectedStopIds[0]
        const job = rowsGroupedByStopId[newSelectedStopId]?.[0]

        if (job) {
          newSelectedTableJobIds.add(job.jobId)
          return selectJobs(Array.from(newSelectedTableJobIds), 'Table')
        }
      }

      const unselectedStopIds = difference(selectedRowIdsArray, selectionModelIds)
      // New id unselected
      if (unselectedStopIds.length === 1) {
        const removedStopId = unselectedStopIds[0]
        const job = rowsGroupedByStopId[removedStopId]?.[0]

        if (job) {
          newSelectedTableJobIds.delete(job.jobId)
          return selectJobs(Array.from(newSelectedTableJobIds), 'Table')
        }
      }

      for (const stopId of selectionModelIds) {
        const job = rowsGroupedByStopId[stopId]?.[0]
        if (job) {
          newSelectedTableJobIds.add(job.jobId)
        }
      }

      return selectJobs(Array.from(newSelectedTableJobIds), 'Table')
    },
    [rowsGroupedByStopId, selectJobs, selectedRowIds, selectedTableJobIds, gridApiRef],
  )

  const handleRowClick: GridEventListener<'rowClick'> = useCallback(
    (params) => {
      const row: DeliveryDataGridRow = params.row
      clickJob(row.jobId, 'Table')
    },
    [clickJob],
  )

  const handleContextMenu = useCallback(
    (event: React.MouseEvent) => {
      event.stopPropagation()
      event.preventDefault()

      const stopId = event.currentTarget.getAttribute('data-id')
      const stop =
        stopId !== null ? (rowsGroupedByStopId[Number(stopId)]?.[0] ?? null) : null

      if (stop !== null) {
        setContextMenu({
          position: {
            mouseX: event.clientX + 2,
            mouseY: event.clientY - 20,
          },
          stop,
        })
      }
    },
    [rowsGroupedByStopId],
  )

  return (
    <>
      <UserDataGridWithSavedSettingsOnIDB<DeliveryDataGridRow>
        apiRef={gridApiRef}
        Component={DataGrid}
        dataGridId="delivery-table-view-data-grid"
        getRowId={useCallbackBranded((row: (typeof rows)[number]) => row.stopId, [])}
        sortingMode="server"
        filterMode="server"
        paginationMode="server"
        checkboxSelection
        pagination
        disableVirtualization
        pageSizeOptions={PAGE_SIZE_OPTIONS}
        paginationModel={paginationModel}
        onPaginationModelChange={onPaginationModelChange}
        filterDebounceMs={FILTER_DEBOUNCE_MS} // since we're doing server side-filtering, we can not afford a lower debounce (to not do too many requests)
        rowCount={tableMeta.totalRows}
        rows={rows}
        loading={jobsTableQuery.isFetching && !jobsTableQuery.isRefetching}
        initialState={{
          columns: { columnVisibilityModel: initialColumnVisibilityModel },
          pinnedColumns: { right: ['actions'] },
        }}
        columns={columns}
        onFilterModelChange={onFilterModelChange}
        sortModel={sortModel}
        onSortModelChange={onSortModelChange}
        density={density}
        onDensityChange={setDensity}
        rowSelectionModel={selectedRowIds}
        onRowSelectionModelChange={handleRowSelectionModelChange}
        disableRowSelectionOnClick
        onRowClick={handleRowClick}
        hideFooterSelectedRowCount
        slots={{
          toolbar: KarooToolbar,
          row: GridRowWithJobId,
          noRowsOverlay: () => <DataStatePlaceholder label="No data available" />,
        }}
        slotProps={{
          row: { onContextMenu: handleContextMenu },
          loadingOverlay: {
            variant: 'linear-progress' as any,
            noRowsVariant: 'skeleton',
          },
          noRowsOverlay: {},
          toolbar: KarooToolbar.createProps({
            slots: {
              settingsButton: {
                show: true,
                props: {
                  getTogglableFields: (columns) =>
                    columns
                      .filter((column) => {
                        const field = column.field as
                          | ColumnFields
                          | typeof GRID_CHECKBOX_SELECTION_FIELD

                        return (
                          field !== GRID_CHECKBOX_SELECTION_FIELD &&
                          field !== 'stopTypeIcon' &&
                          field !== 'actions'
                        )
                      })
                      .map((column) => column.field),
                },
              },
              filterButton: { show: true },
            },
            extraContent: {
              left: (
                <Stack
                  direction="row"
                  gap={1}
                  alignItems="center"
                  flexWrap="wrap"
                >
                  <AssignmentFilter
                    value={filters.assignment}
                    onChange={(value) => setFilters({ assignment: value })}
                  />
                  <DriversFilter
                    value={filters.assignedToDriverIds}
                    onChange={(value) => setFilters({ assignedToDriverIds: value })}
                  />
                  <StatusFilter
                    value={filters.stopStatus}
                    onChange={(value) => setFilters({ stopStatus: value })}
                  />
                  <ExpandableSearchField
                    value={filters.searchInput ?? ''}
                    onChange={(e) =>
                      setFilters({ searchInput: e.target.value || undefined })
                    }
                    onClearIconClick={() => setFilters({ searchInput: undefined })}
                    debounceTime={FILTER_DEBOUNCE_MS}
                  />
                </Stack>
              ),
              middle: (
                <GroupStopsToggler
                  value={filters.groupStops}
                  onChange={(value) => setFilters({ groupStops: value })}
                />
              ),
              right: (
                <Stack
                  direction="row"
                  alignItems="center"
                  gap={2}
                >
                  <CounterFilter
                    counters={[
                      {
                        key: 'unassigned',
                        value: tableMeta.totalUnassigned,
                        label: ctIntl.formatMessage({ id: 'Unassigned' }),
                      },
                      {
                        key: 'rejected',
                        value: tableMeta.totalRejected,
                        label: ctIntl.formatMessage({ id: 'Rejected' }),
                      },
                      {
                        key: 'completed',
                        value: tableMeta.totalCompleted,
                        label: ctIntl.formatMessage({ id: 'Completed' }),
                      },
                    ]}
                    onChange={(key) => {
                      match(key)
                        .with('unassigned', () => {
                          setFilters({ assignment: ASSIGN_STATUS.UNASSIGNED })
                        })
                        .with('rejected', () => {
                          setFilters({ stopStatus: [STOP_STATUS_ID.REJECTED] })
                        })
                        .with('completed', () => {
                          setFilters({ stopStatus: [STOP_STATUS_ID.COMPLETED] })
                        })
                        .exhaustive()
                    }}
                  />

                  <ReportsDownload
                    totalJobs={tableMeta.totalRows}
                    filters={{
                      filters: queryInput.filters,
                      sort: queryInput.sort,
                      url: `${window.location.origin}${window.location.pathname
                        .split('/')
                        .slice(0, 2)
                        .join('/')}`,
                      returnData: false,
                    }}
                  />
                </Stack>
              ),
            },
          }),
          filterPanel: { logicOperators: [] },
          pagination: {
            slotProps: {
              actions: {
                previousButton: {
                  disabled: paginationModel.page === 0,
                },
                nextButton: {
                  disabled:
                    jobsTableQuery.isFetching ||
                    paginationModel.page + 1 >= tableMeta.lastPage,
                },
              },
            },
          },
        }}
        sx={({ palette }) => ({
          '& .MuiDataGrid-main': { paddingX: 2.5 },
          '& .MuiDataGrid-row': { cursor: 'pointer' },
          '& .MuiDataGrid-row--editing': {
            boxShadow: 'none',
          },
          '& .MuiDataGrid-row--editing .MuiDataGrid-cell': {
            backgroundColor: rgba(palette.primary.main, palette.action.selectedOpacity),
          },
          '.MuiCheckbox-root.Mui-checked': {
            color: 'primary.main',
          },
          '.MuiDataGrid-row.Mui-selected': {
            backgroundColor: rgba(palette.primary.main, palette.action.selectedOpacity),
            '&:hover': {
              background: rgba(palette.primary.main, palette.action.selectedOpacity),
            },
          },
          '& .MuiDataGrid-row--hover': {
            backgroundColor: rgba(palette.action.hover, palette.action.hoverOpacity),
          },
          '& .MuiDataGrid-columnHeader.header-blank': {
            border: 'none',
            alignItems: 'start',
            padding: 0,

            '& .MuiDataGrid-columnHeaderTitleContainer': {
              justifyContent: 'start',
            },

            '& .MuiDataGrid-columnSeparator': {
              display: 'none',
            },
          },
          '& .MuiDataGrid-cell.cell-blank': {
            border: 'none',
            padding: 0,
            overflow: 'visible',
          },

          '& .MuiDataGrid-row--lastVisible': {
            '.MuiDataGrid-cell.cell-blank': {
              outline: '1px solid white',
            },

            '& .pd-dashed-line': {
              display: 'none',
            },
          },
        })}
      />

      {contextMenu && (
        <JobActionsMenu
          jobDetails={{
            jobId: contextMenu.stop.jobId,
            jobStatusId: contextMenu.stop.jobStatusId,
            driverId: contextMenu.stop.deliveryDriverId,
            hasJobStarted: contextMenu.stop.hasJobStarted,
          }}
          panel="Table"
          hideIcon
          onClose={() => setContextMenu(null)}
          menuProps={{
            anchorReference: 'anchorPosition',
            anchorPosition:
              contextMenu !== null
                ? {
                    top: contextMenu.position.mouseY,
                    left: contextMenu.position.mouseX,
                  }
                : undefined,
            open: contextMenu !== null,
            slotProps: {
              root: {
                onContextMenu: (e) => {
                  e.preventDefault()
                  setContextMenu(null)
                },
              },
            },
          }}
        />
      )}
    </>
  )
}

export default DeliveryDataGrid

function GridRowWithJobId(props: GridRowProps) {
  const jobId = (props.row as GridRowModel<DeliveryDataGridRow>).jobId

  return (
    <GridRow
      {...props}
      data-job-id={jobId}
    />
  )
}

const FILTER_DEBOUNCE_MS = 500
