import type { ASSIGN_STATUS } from 'src/modules/deliveryRevamp/components/Filters/filtersSchemas'
import type {
  CUSTOM_STOP_STATUS_ID_CANCELED,
  CUSTOM_STOP_STATUS_ID_PICKED_UP,
  STOP_STATUS_ID,
} from 'src/modules/deliveryRevamp/constants/job'
import type { ExtractStrict } from 'src/types/utils'

export type ColumnFields =
  | 'referenceNumber'
  | 'customerName'
  | 'planName'
  | 'driverName'
  | 'creationTs'
  | 'actualArrivalTs'
  | 'completedTs'
  | 'scheduledArrivalTs'
  | 'stopTypeIcon'
  | 'stopTypeId'
  | 'jobId'
  | 'scheduledDeliveryTs'
  | 'stopStatus'
  | 'todos'
  | 'items'
  | 'address'
  | 'actualDuration'
  | 'itemsTrackingNumber'
  | 'labels'
  | 'scheduledTravelDistance'
  | 'actualDistance'
  | 'travelDistancePct'
  | 'scheduledTravelTime'
  | 'actualTravelTime'
  | 'travelTimePct'
  | 'actions'

export type FilterableFields = ExtractStrict<
  ColumnFields,
  | 'referenceNumber'
  | 'customerName'
  | 'driverName'
  | 'creationTs'
  | 'actualArrivalTs'
  | 'completedTs'
  | 'scheduledArrivalTs'
  | 'labels'
>

export type SortableFields = ExtractStrict<
  ColumnFields,
  | 'referenceNumber'
  | 'customerName'
  | 'scheduledDeliveryTs'
  | 'stopStatus'
  | 'driverName'
  | 'creationTs'
  | 'actualArrivalTs'
  | 'completedTs'
  | 'scheduledArrivalTs'
>

export type DataGridToolbarFilters = {
  assignment: ASSIGN_STATUS
  assignedToDriverIds: Array<string>
  stopStatus: Array<
    | STOP_STATUS_ID.CREATED
    | STOP_STATUS_ID.STARTED
    | STOP_STATUS_ID.ARRIVED
    | typeof CUSTOM_STOP_STATUS_ID_PICKED_UP
    | STOP_STATUS_ID.COMPLETED
    | STOP_STATUS_ID.REJECTED
    | typeof CUSTOM_STOP_STATUS_ID_CANCELED
  >
  groupStops: boolean
}
