import {
  createContext,
  useCallback,
  useContext,
  useEffect,
  useMemo,
  useState,
  type Dispatch,
  type ReactNode,
  type SetStateAction,
} from 'react'
import { isNil } from 'lodash'
import type { GridPaginationModel } from '@karoo-ui/core'
import { match } from 'ts-pattern'

import type { FetchStopsTableListQuery } from 'src/modules/deliveryRevamp/api/jobs/useStopsTableListQuery'
import { ASSIGN_STATUS } from 'src/modules/deliveryRevamp/components/Filters/filtersSchemas'
import { useDeliveryMainPageContext } from 'src/modules/deliveryRevamp/contexts/DeliveryMainPageContext'
import { exact } from 'src/util-functions/functional-utils'

import type { DataGridToolbarFilters, SortableFields } from './types'

type DateRangeFilter = { from: string; to: string }

export type FilterModelFilters = {
  referenceNumber: string | undefined
  customerName: string | undefined
  driverName: string | undefined
  planName: string | undefined
  creationTs: DateRangeFilter | undefined
  actualArrivalTs: DateRangeFilter | undefined
  completedTs: DateRangeFilter | undefined
  scheduledArrivalTs: DateRangeFilter | undefined
  searchInput: string | undefined
  labels: Array<string> | undefined
}

type DataGridFilters = DataGridToolbarFilters & FilterModelFilters

const dataGridInitialFilters: DataGridFilters = {
  assignment: ASSIGN_STATUS.ALL,
  assignedToDriverIds: [],
  stopStatus: [],
  labels: [],
  groupStops: true,
  referenceNumber: undefined,
  customerName: undefined,
  driverName: undefined,
  planName: undefined,
  creationTs: undefined,
  actualArrivalTs: undefined,
  completedTs: undefined,
  scheduledArrivalTs: undefined,
  searchInput: undefined,
}

export type DataGridSortModel = Array<{
  field: SortableFields
  sort: 'asc' | 'desc'
}>

export const PAGE_SIZE_OPTIONS = [50, 100, 200]

type DataGridFiltersContextValue = {
  filters: DataGridFilters
  setFilters: (updates: Partial<DataGridFilters>) => void
  sortModel: DataGridSortModel
  setSortModel: Dispatch<SetStateAction<DataGridSortModel>>
  paginationModel: GridPaginationModel
  setPaginationModel: Dispatch<SetStateAction<GridPaginationModel>>
  queryInput: FetchStopsTableListQuery.ApiInput
}

const DataGridFiltersContext = createContext<DataGridFiltersContextValue | undefined>(
  undefined,
)

export const DataGridFiltersProvider = ({ children }: { children: ReactNode }) => {
  const [filters, setFilters] = useState<DataGridFilters>(dataGridInitialFilters)
  const [sortModel, setSortModel] = useState<DataGridSortModel>([])
  const [paginationModel, setPaginationModel] = useState<GridPaginationModel>({
    page: 0,
    pageSize: PAGE_SIZE_OPTIONS[0],
  })

  const {
    data: { selectedDateRange },
  } = useDeliveryMainPageContext()

  const [stableSelectedDateRange, setStableSelectedDateRange] =
    useState(selectedDateRange)

  useEffect(() => {
    if (
      selectedDateRange?.start.toISO() !== stableSelectedDateRange?.start.toISO() ||
      selectedDateRange?.end.toISO() !== stableSelectedDateRange?.end.toISO()
    ) {
      setPaginationModel((prev) => ({
        ...prev,
        page: 0,
      }))
      setStableSelectedDateRange(selectedDateRange)
    }
  }, [selectedDateRange, stableSelectedDateRange?.end, stableSelectedDateRange?.start])

  const queryInput = useMemo((): FetchStopsTableListQuery.ApiInput => {
    const {
      assignment,
      completedTs,
      creationTs,
      planName,
      stopStatus,
      driverName,
      groupStops,
      searchInput,
      customerName,
      referenceNumber,
      actualArrivalTs,
      scheduledArrivalTs,
      assignedToDriverIds,
      labels,
    } = filters

    const scheduledDeliveryTs = !isNil(stableSelectedDateRange)
      ? {
          from: stableSelectedDateRange.start.toISO({ suppressMilliseconds: true }),
          to: stableSelectedDateRange.end
            .startOf('second')
            .toISO({ suppressMilliseconds: true }),
        }
      : null

    return exact({
      filters: {
        scheduledDeliveryTs,
        groupStops,
        assignment,
        labels: labels && labels.length > 0 ? labels : undefined,
        assignedToDriverIds:
          assignedToDriverIds.length > 0 ? assignedToDriverIds : undefined,
        stopStatus:
          stopStatus.length > 0
            ? stopStatus.map<FetchStopsTableListQuery.FilterStopStatus>((status) =>
                match(status)
                  .with(1, () => 'not_started' as const)
                  .with(2, () => 'started' as const)
                  .with(3, () => 'arrived' as const)
                  .with(6, () => 'picked_up' as const)
                  .with(4, () => 'completed' as const)
                  .with(5, () => 'rejected' as const)
                  .with(7, () => 'cancelled' as const)
                  .exhaustive(),
              )
            : undefined,
        searchInput,
        referenceNumber,
        customerName,
        driverName,
        planName,
        creationTs,
        actualArrivalTs,
        scheduledArrivalTs,
        completedTs,
      },
      sort: sortModel.length > 0 ? { [sortModel[0].field]: sortModel[0].sort } : {},
      pagination: {
        page: paginationModel.page + 1,
        perPage: paginationModel.pageSize,
      },
    })
  }, [
    filters,
    sortModel,
    stableSelectedDateRange,
    paginationModel.page,
    paginationModel.pageSize,
  ])

  const updateFilters = useCallback((updates: Partial<DataGridFilters>) => {
    setPaginationModel((prev) => ({
      ...prev,
      page: 0, // Reset pagination to the first page
    }))
    setFilters((prev) => ({ ...prev, ...updates }))
  }, [])

  const value = useMemo<DataGridFiltersContextValue>(
    () => ({
      filters,
      queryInput,
      sortModel,
      setSortModel,
      paginationModel,
      setPaginationModel,
      setFilters: updateFilters,
    }),
    [filters, queryInput, sortModel, paginationModel, updateFilters],
  )

  return (
    <DataGridFiltersContext.Provider value={value}>
      {children}
    </DataGridFiltersContext.Provider>
  )
}

const useDataGridFiltersContext = () => {
  const context = useContext(DataGridFiltersContext)
  if (!context) {
    throw new Error(
      'useDataGridFiltersContext must be used within a DataGridFiltersProvider',
    )
  }
  return context
}

export default useDataGridFiltersContext
