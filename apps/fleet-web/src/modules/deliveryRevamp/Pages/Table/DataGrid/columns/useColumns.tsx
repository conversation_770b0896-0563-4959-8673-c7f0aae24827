import { useMemo } from 'react'
import {
  Box,
  getGridStringOperators,
  GRID_CHECKBOX_SELECTION_COL_DEF,
  GridFilterInputMultipleSingleSelect,
  OverflowTypography,
  Stack,
  Tooltip,
  Typography,
  useDataGridColumnHelper,
  type GridColDef,
} from '@karoo-ui/core'
import { match } from 'ts-pattern'

import { useUserFormatLengthInKmOrMiles } from 'src/modules/components/connected/UserFormattedLengthInKmOrMiles'
import useDriversList from 'src/modules/deliveryRevamp/api/drivers/useDriversList'
import useDeliveryJobLabelsQuery from 'src/modules/deliveryRevamp/api/job-labels/useFetchDeliveryJobLabelsQuery'
import AssignPopover from 'src/modules/deliveryRevamp/components/AssignPopover'
import DriverChip from 'src/modules/deliveryRevamp/components/DriverChip'
import JobStatusChip from 'src/modules/deliveryRevamp/components/JobCard/components/JobStatusChip'
import StopTypeIcon from 'src/modules/deliveryRevamp/components/JobCard/components/StopTypeIcon'
import JobActionsMenu from 'src/modules/deliveryRevamp/components/MapPanel/components/JobDetails/EditJob/ActionsMenu'
import {
  JOB_STATUS_ID,
  STOP_STATUS_ID,
  STOP_STATUS_ID_TO_TIME_KEY,
  STOP_TYPE_ID,
} from 'src/modules/deliveryRevamp/constants/job'
import { useDeliverySettingsContext } from 'src/modules/deliveryRevamp/contexts/DeliverySettingsContext'
import { formatTimeWindow } from 'src/modules/deliveryRevamp/helpers'
import { DeliveryDateTime } from 'src/modules/deliveryRevamp/utils/deliveryDateTime'
import { ctIntl } from 'src/util-components/ctIntl'

import ItemStatus from '../components/ItemStatus'
import TodoStatus from '../components/TodoStatus'
import type { ColumnFields } from '../types'
import type { DeliveryDataGridRow } from './types'

export const useColumns = (): Array<GridColDef<DeliveryDataGridRow>> => {
  const columnHelper = useDataGridColumnHelper<DeliveryDataGridRow>({
    filterMode: 'server',
  })

  const { data: driversList } = useDriversList()

  const { data: jobLabels } = useDeliveryJobLabelsQuery()

  const sortedJobLabels = useMemo(() => {
    if (!jobLabels) return []

    return jobLabels
      .sort((a, b) => {
        const userIdDiff = Number(a.userId) - Number(b.userId)
        return userIdDiff || Number(a.id) - Number(b.id)
      })
      .map((label) => ({ label: label.name, value: label.id }))
  }, [jobLabels])

  const {
    deliverySettings: { driverTrackLocations },
  } = useDeliverySettingsContext()

  const { formatLengthInKmOrMiles } = useUserFormatLengthInKmOrMiles()

  return useMemo(() => {
    const buildDateTimeColumnWithOnlyRangeFilterOperator = (
      params: Parameters<typeof columnHelper.dateTime>[0],
    ) => {
      const column = columnHelper.dateTime(params)
      const { getGridDateColumnRangeOperator } = columnHelper.utils

      return {
        ...column,
        filterOperators: [
          getGridDateColumnRangeOperator({
            InputComponentProps: {
              dateRangePickerProps: {
                disableFuture: true,
              },
            },
          }),
        ],
        resizable: true,
      }
    }

    /** Function to check for field type and improve type safety */
    const createColumn = (
      field: ColumnFields,
      gridColDef: GridColDef<DeliveryDataGridRow>,
    ) => ({
      ...gridColDef,
      field,
    })

    return [
      {
        ...GRID_CHECKBOX_SELECTION_COL_DEF,
        width: 76,
        renderCell: (params) => (
          <Stack
            direction="row"
            alignItems="center"
            justifyContent="space-between"
            width="100%"
            height="100%"
          >
            {GRID_CHECKBOX_SELECTION_COL_DEF.renderCell?.(params)}
            <Stack
              position="relative"
              alignItems="center"
              justifyContent="center"
              height="100%"
              flex="1"
            >
              <StopTypeIcon stopTypeId={params.row.stopTypeId} />
              {params.row.stopTypeId !== STOP_TYPE_ID.SINGLE && (
                <Stack
                  className="pd-dashed-line"
                  sx={({ palette }) => ({
                    height: '50%',
                    position: 'absolute',
                    ...(params.row.stopTypeId === STOP_TYPE_ID.PICKUP && {
                      top: '75%',
                      borderLeft: `2px dashed ${palette.divider}`,
                    }),
                  })}
                />
              )}
            </Stack>
          </Stack>
        ),
        headerClassName: 'header-blank',
        cellClassName: 'cell-blank',
        disableColumnMenu: true,
      },
      createColumn(
        'stopTypeId',
        columnHelper.string(
          (_, row) =>
            ctIntl.formatMessage({
              id: match(row.stopTypeId)
                .with(STOP_TYPE_ID.PICKUP, () => 'Pick up')
                .otherwise(() => 'Deliver To'),
            }),
          {
            field: 'stopTypeId',
            headerName: ctIntl.formatMessage({
              id: 'Stop Type',
            }),
            filterable: false,
            sortable: false,
          },
        ),
      ),
      createColumn(
        'jobId',
        columnHelper.string((_, row) => row.orderId, {
          field: 'jobId',
          headerName: ctIntl.formatMessage({
            id: 'deliver.middlePanel.tableHeader.jobId',
          }),
          filterable: false,
          sortable: false,
        }),
      ),
      createColumn(
        'referenceNumber',
        columnHelper.string((_, row) => row.referenceNumber ?? '-', {
          field: 'referenceNumber',
          headerName: ctIntl.formatMessage({
            id: 'Reference Number',
          }),
          filterOperators: getGridStringOperators<DeliveryDataGridRow>().filter(
            (operator) => operator.value === 'contains',
          ),
        }),
      ),
      createColumn(
        'customerName',
        columnHelper.string((_, row) => (row.customerName ?? row.addressLine1) || '-', {
          field: 'customerName',
          headerName: ctIntl.formatMessage({
            id: 'Customer Name',
          }),
          filterOperators: getGridStringOperators<DeliveryDataGridRow>().filter(
            (operator) => operator.value === 'contains',
          ),
        }),
      ),
      createColumn(
        'planName',
        columnHelper.string((_, row) => String(row.planId || '-'), {
          field: 'planName',
          headerName: ctIntl.formatMessage({
            id: 'Plan/Route Name',
          }),
          renderCell: ({ row }) => row.planName || row.routeName || '-',
          filterOperators: getGridStringOperators<DeliveryDataGridRow>().filter(
            (operator) => operator.value === 'contains',
          ),
        }),
      ),
      createColumn(
        'scheduledDeliveryTs',
        buildDateTimeColumnWithOnlyRangeFilterOperator({
          field: 'scheduledDeliveryTs',
          headerName: ctIntl.formatMessage({
            id: 'Scheduled Delivery Date and Allowed Start',
          }),
          valueGetter: (_, row) => {
            const deliveryDate = row.deliveryDate?.date
            const allowedStart = row.allowedToStartAt?.date

            if (allowedStart && allowedStart instanceof Date) return allowedStart
            if (deliveryDate && deliveryDate instanceof Date) return deliveryDate

            return null
          },
          valueFormatter: (value) => {
            if (value) {
              return DeliveryDateTime.fromJSDate(value).toFormat('D T')
            }

            return '-'
          },
          filterable: false,
        }),
      ),
      createColumn(
        'stopStatus',
        columnHelper.string((_, row) => row.stopStatusId as unknown as string, {
          field: 'stopStatus' as const,
          headerName: ctIntl.formatMessage({
            id: 'Status',
          }),
          minWidth: 130,
          width: 150,
          renderCell: ({ row }) => {
            const activityTimestamp =
              row[STOP_STATUS_ID_TO_TIME_KEY[row.stopStatusId]]?.raw ?? null
            const isInUnassignedRoute =
              row.planId !== null && row.deliveryDriverId === null

            return (
              <JobStatusChip
                jobStatusId={row.jobStatusId}
                stopStatusId={row.stopStatusId}
                stopTypeId={row.stopTypeId}
                rejectedReason={row.statusRemarks}
                activityTimestamp={activityTimestamp}
                expectedDurationInMinutes={row.expectedDurationInMinutes}
                actualDurationInMinutes={row.actualDurationInMinutes}
                isInUnassignedRoute={isInUnassignedRoute}
                deliveryDate={
                  row.activityStartedTs?.raw || row.deliveryDate?.raw || null
                }
                {...formatTimeWindow(row.deliveryWindows, activityTimestamp)}
              />
            )
          },
          filterable: false,
        }),
      ),
      createColumn(
        'driverName',
        columnHelper.string((_, row) => row.driverName ?? '-', {
          field: 'driverName',
          headerName: ctIntl.formatMessage({
            id: 'Driver Name',
          }),
          minWidth: 120,
          width: 150,
          renderCell: ({ row }) => {
            const driverInfo = {
              driver: row.deliveryDriverId
                ? driversList?.byId[row.deliveryDriverId]
                : undefined,
              isDriverDeactivated:
                row.deliveryDriverId && !driversList?.byId[row.deliveryDriverId]
                  ? true
                  : false,
            }

            if (
              [JOB_STATUS_ID.COMPLETED, JOB_STATUS_ID.CANCELED].includes(
                row.jobStatusId,
              )
            ) {
              return (
                <DriverChip
                  driver={
                    driverInfo.driver
                      ? driverInfo.driver
                      : { fullName: row.driverName ?? '' }
                  }
                  canExpand={false}
                  isDriverDeactivated={driverInfo.isDriverDeactivated}
                />
              )
            }

            return (
              <AssignPopover
                jobIds={[row.jobId]}
                deliveryDriverId={row.deliveryDriverId}
                avoidRouteAssignment
                popoverProps={{
                  anchorOrigin: { vertical: 'bottom', horizontal: 'left' },
                  transformOrigin: { vertical: 'top', horizontal: 'left' },
                }}
                scheduledDeliveryTs={row.deliveryDate?.date}
              >
                <DriverChip
                  driver={driverInfo.driver}
                  canExpand
                  isDriverDeactivated={driverInfo.isDriverDeactivated}
                />
              </AssignPopover>
            )
          },
          filterOperators: getGridStringOperators<DeliveryDataGridRow>().filter(
            (operator) => operator.value === 'contains',
          ),
        }),
      ),
      createColumn('todos', {
        field: 'todos',
        headerName: ctIntl.formatMessage({
          id: 'Todos',
        }),
        valueGetter: (_, row) => row.todos,
        renderCell: ({ row }) => {
          if (row.todos.length === 0) {
            return '-'
          }

          return (
            <Stack
              sx={{
                backgroundColor: '#EEEEEE',
                flexDirection: 'row',
                borderRadius: 0.5,
                p: 0.5,
                gap: 0.5,
              }}
            >
              {row.todos.map((todo) => (
                <TodoStatus
                  key={todo.id}
                  todo={todo}
                  jobInfo={row}
                  iconsSize={16}
                />
              ))}
            </Stack>
          )
        },
        sortable: false,
        filterable: false,
      }),
      createColumn('items', {
        field: 'items',
        headerName: ctIntl.formatMessage({
          id: 'delivery.table.header.itemsAndTodos',
        }),
        minWidth: 150,
        flex: 1,
        valueGetter: (_, row) => row.items,
        renderCell: ({ row }) => (
          <Stack
            flexDirection="row"
            gap={0.5}
          >
            {row.items.map((item, itemIndex) => (
              <Stack
                key={item.job_item_id}
                sx={{
                  backgroundColor: '#EEEEEE',
                  flexDirection: 'row',
                  borderRadius: 0.5,
                  p: 0.5,
                }}
              >
                <Stack
                  direction="row"
                  alignItems="center"
                  gap={0.5}
                >
                  <ItemStatus
                    item={item}
                    iconsSize={16}
                  />
                  <Stack
                    direction="row"
                    alignItems="center"
                    gap={0.5}
                  >
                    {item.jobItemTodo.map((todo) => (
                      <TodoStatus
                        key={todo.id}
                        todo={todo}
                        item={item}
                        itemIndex={itemIndex}
                        jobInfo={row}
                        iconsSize={16}
                      />
                    ))}
                  </Stack>
                </Stack>
              </Stack>
            ))}
          </Stack>
        ),
        sortable: false,
        filterable: false,
      }),
      createColumn(
        'address',
        columnHelper.string((_, row) => `${row.addressLine1} ${row.addressLine2}`, {
          field: 'address',
          headerName: ctIntl.formatMessage({ id: 'Address' }),
          minWidth: 150,
          flex: 1,
          renderCell: ({ row }) => (
            <Stack width="100%">
              <OverflowTypography
                typographyProps={{ variant: 'caption', fontStyle: 'italic' }}
              >
                {row.addressLine1}
              </OverflowTypography>
              <OverflowTypography
                typographyProps={{ variant: 'caption', fontStyle: 'italic' }}
              >
                {row.addressLine2}
              </OverflowTypography>
            </Stack>
          ),
          sortable: false,
          filterable: false,
        }),
      ),
      createColumn(
        'creationTs',
        buildDateTimeColumnWithOnlyRangeFilterOperator({
          field: 'creationTs',
          headerName: ctIntl.formatMessage({ id: 'global.creationTime' }),
          flex: 1,
          valueGetter: (_, row) => row.createTs.date,
          valueFormatter: (value) => {
            if (value) {
              return DeliveryDateTime.fromJSDate(value).toFormat('D T')
            }

            return '-'
          },
          minWidth: 150,
        }),
      ),
      createColumn(
        'scheduledArrivalTs',
        columnHelper.string(
          (_, row) => {
            if (row.scheduledArrivalTs === null)
              return ctIntl.formatMessage({ id: 'Anytime' })
            return `${row.scheduledArrivalTs.from} - ${row.scheduledArrivalTs.to}`
          },
          {
            field: 'scheduledArrivalTs',
            headerName: ctIntl.formatMessage({ id: 'Scheduled Arrival Time' }),
            filterable: false,
            sortable: false,
          },
        ),
      ),
      createColumn(
        'actualArrivalTs',
        buildDateTimeColumnWithOnlyRangeFilterOperator({
          field: 'actualArrivalTs',
          headerName: ctIntl.formatMessage({
            id: 'delivery.table.header.actualArrivalTime',
          }),
          valueGetter: (_, row) => row.actualArrivalTs?.date ?? null,
          valueFormatter: (value) => {
            if (value) {
              return DeliveryDateTime.fromJSDate(value).toFormat('D T:ss')
            }

            return '-'
          },
          renderCell: ({ row }) => {
            if (!row?.actualArrivalTs) return '-'

            const { isLate } = formatTimeWindow(
              row.deliveryWindows,
              row.actualArrivalTs.raw,
            )

            return (
              <Typography>
                {DeliveryDateTime.fromJSDate(row.actualArrivalTs.date).toFormat('D')}
                <Tooltip
                  title={match(isLate)
                    .with(true, () =>
                      ctIntl.formatMessage({
                        id: 'The driver arrived later than scheduled.',
                      }),
                    )
                    .otherwise(() => '')}
                >
                  <Box
                    component="span"
                    sx={{ ...(isLate && { color: 'error.main' }) }}
                  >
                    {' '}
                    {DeliveryDateTime.fromJSDate(row.actualArrivalTs.date).toFormat(
                      'T:ss',
                    )}
                  </Box>
                </Tooltip>
              </Typography>
            )
          },
        }),
      ),
      createColumn(
        'actualDuration',
        columnHelper.number((_, row) => row.actualDurationInMinutes ?? 0, {
          field: 'actualDuration',
          headerName: ctIntl.formatMessage({ id: 'Actual Duration' }),
          renderCell: ({
            row: { actualDurationInMinutes, expectedDurationInMinutes },
          }) => {
            if (!actualDurationInMinutes) return '-'

            const isExceeded =
              expectedDurationInMinutes &&
              actualDurationInMinutes > expectedDurationInMinutes

            return (
              <Tooltip
                title={
                  isExceeded
                    ? ctIntl.formatMessage({
                        id: 'The actual duration spent at the location exceeded the scheduled duration.',
                      })
                    : ''
                }
              >
                <Typography sx={{ ...(isExceeded && { color: 'error.main' }) }}>
                  {ctIntl.formatDuration(actualDurationInMinutes * 60)}
                </Typography>
              </Tooltip>
            )
          },
          filterable: false,
        }),
      ),
      createColumn(
        'completedTs',
        buildDateTimeColumnWithOnlyRangeFilterOperator({
          field: 'completedTs',
          headerName: ctIntl.formatMessage({ id: 'Completed Time' }),
          valueGetter: (_, row) => row.completedTs?.date ?? null,
          renderCell: ({ row }) => {
            if (!row?.completedTs) return '-'

            const { isLate } = formatTimeWindow(
              row.deliveryWindows,
              row[STOP_STATUS_ID_TO_TIME_KEY[row.stopStatusId]]?.raw,
            )

            return (
              <Typography>
                {DeliveryDateTime.fromJSDate(row.completedTs.date).toFormat('D')}
                <Tooltip
                  title={match({
                    stopStatusId: row.stopStatusId,
                    stopTypeId: row.stopTypeId,
                    isLate,
                  })
                    .with({ isLate: true, stopStatusId: STOP_STATUS_ID.ARRIVED }, () =>
                      ctIntl.formatMessage({
                        id: 'The driver arrived later than scheduled.',
                      }),
                    )
                    .with(
                      {
                        isLate: true,
                        stopStatusId: STOP_STATUS_ID.COMPLETED,
                        stopTypeId: STOP_TYPE_ID.PICKUP,
                      },
                      () =>
                        ctIntl.formatMessage({
                          id: 'Driver picked up the job later than scheduled.',
                        }),
                    )
                    .with(
                      {
                        isLate: true,
                        stopStatusId: STOP_STATUS_ID.COMPLETED,
                      },
                      () =>
                        ctIntl.formatMessage({
                          id: 'The driver completed the job later than scheduled, possibly due to a delayed arrival or extended duration',
                        }),
                    )
                    .otherwise(() => '')}
                >
                  <Box
                    component="span"
                    sx={{ ...(isLate && { color: 'error.main' }) }}
                  >
                    {' '}
                    {DeliveryDateTime.fromJSDate(row.completedTs.date).toFormat('T:ss')}
                  </Box>
                </Tooltip>
              </Typography>
            )
          },
        }),
      ),
      createColumn(
        'itemsTrackingNumber',
        columnHelper.string(
          (_, row) =>
            row.itemsTrackingNumber.length > 0
              ? row.itemsTrackingNumber.map((obj) => Object.values(obj)[0]).join(', ')
              : '-',
          {
            field: 'itemsTrackingNumber',
            headerName: ctIntl.formatMessage({ id: 'Items Tracking Number' }),
            sortable: false,
            filterable: false,
          },
        ),
      ),
      createColumn('labels', {
        ...columnHelper.string(
          (_, row) => (row.labels.length > 0 ? row.labels.join(', ') : '-'),
          {
            field: 'labels',
            headerName: ctIntl.formatMessage({ id: 'Labels' }),
            sortable: false,
            filterOperators: [
              {
                value: 'is',
                getApplyFilterFn: (filterItem) => filterItem.value,
                InputComponent: GridFilterInputMultipleSingleSelect,
                InputComponentProps: {
                  type: 'multiple',
                },
              },
            ],
          },
        ),
        type: 'singleSelect',
        valueOptions: sortedJobLabels,
      }),
      createColumn(
        'scheduledTravelDistance',
        columnHelper.number((_, row) => row.scheduledTravelDistanceInMeters ?? 0, {
          field: 'scheduledTravelDistance',
          headerName: ctIntl.formatMessage({ id: 'Scheduled Travel Distance (km)' }),
          renderCell: ({ row }) => {
            if (!row.scheduledTravelDistanceInMeters) return '-'

            return formatLengthInKmOrMiles({
              valueInKm: row.scheduledTravelDistanceInMeters / 1000,
              intlFormatNumberOptions: {
                maximumFractionDigits: 0,
              },
            })
          },
          sortable: false,
          filterable: false,
        }),
      ),
      ...(driverTrackLocations
        ? [
            createColumn(
              'actualDistance',
              columnHelper.number((_, row) => row.actualDistanceInMeters ?? 0, {
                field: 'actualDistance',
                headerName: ctIntl.formatMessage({ id: 'Actual Distance' }),
                renderCell: ({ row }) => {
                  if (!row.actualDistanceInMeters) return '-'

                  return formatLengthInKmOrMiles({
                    valueInKm: row.actualDistanceInMeters / 1000,
                    intlFormatNumberOptions: {
                      maximumFractionDigits: 0,
                    },
                  })
                },
                sortable: false,
                filterable: false,
              }),
            ),
            createColumn(
              'travelDistancePct',
              columnHelper.number((_, row) => row.travelDistancePct ?? 0, {
                field: 'travelDistancePct',
                headerName: ctIntl.formatMessage({ id: 'Travel Distance %' }),
                renderCell: ({ row }) => row.travelDistancePct || '-',
                filterable: false,
                sortable: false,
              }),
            ),
          ]
        : []),
      createColumn(
        'scheduledTravelTime',
        columnHelper.number((_, row) => row.scheduledTravelTime ?? 0, {
          field: 'scheduledTravelTime',
          headerName: ctIntl.formatMessage({ id: 'Scheduled Travel Time (min)' }),
          renderCell: ({ row }) => {
            if (!row.scheduledTravelTime) return '-'

            // Already in seconds
            return ctIntl.formatDuration(row.scheduledTravelTime)
          },
          filterable: false,
          sortable: false,
        }),
      ),
      ...(driverTrackLocations
        ? [
            createColumn(
              'actualTravelTime',
              columnHelper.number((_, row) => row.actualTravelTime ?? 0, {
                field: 'actualTravelTime',
                headerName: ctIntl.formatMessage({ id: 'Actual Travel Time (min)' }),
                renderCell: ({ row }) => {
                  if (!row.actualTravelTime) return '-'

                  // Already in seconds
                  return ctIntl.formatDuration(row.actualTravelTime)
                },
                filterable: false,
                sortable: false,
              }),
            ),
            createColumn(
              'travelTimePct',
              columnHelper.number((_, row) => row.travelTimePct ?? 0, {
                field: 'travelTimePct',
                headerName: ctIntl.formatMessage({ id: 'Travel Time %' }),
                renderCell: ({ row }) => row.travelTimePct || '-',
                filterable: false,
                sortable: false,
              }),
            ),
          ]
        : []),
      {
        field: 'actions',
        type: 'actions' as const,
        headerName: ctIntl.formatMessage({ id: '' }),
        align: 'center',
        width: 50,
        getActions: ({ row }) => [
          <JobActionsMenu
            key={row.stopId}
            jobDetails={{
              jobId: Number(row.jobId),
              jobStatusId: row.jobStatusId,
              driverId: row.deliveryDriverId,
              hasJobStarted: row.hasJobStarted,
            }}
            panel="Table"
            menuProps={{
              slotProps: {
                root: {
                  onContextMenu: (e) => {
                    e.preventDefault()
                    e.stopPropagation()
                  },
                },
              },
            }}
          />,
        ],
      },
    ]
  }, [
    columnHelper,
    sortedJobLabels,
    driverTrackLocations,
    driversList?.byId,
    formatLengthInKmOrMiles,
  ])
}
