import { includes, isEqual, keyBy, keys, orderBy, pickBy } from 'lodash'
import { Stack, Typography } from '@karoo-ui/core'
import WarningAmberIcon from '@mui/icons-material/WarningAmber'
import type { Location } from 'history'
import { Info } from 'luxon'
import { FormattedMessage, type useIntl } from 'react-intl'
import { match, P } from 'ts-pattern'

import { buildRouteQueryStringKeepingExistingSearchParams } from 'api/utils'
import { DeliveryDateTime } from 'src/modules/deliveryRevamp/utils/deliveryDateTime'
import { ctIntl } from 'src/util-components/ctIntl'

import { parseJobFormToApiPayload } from '../../api/jobs/jobFormApiParser'
import type { FetchDeliveryJobDetails } from '../../api/jobs/useDeliveryJobDetails'
import type { JobsList } from '../../api/jobs/useJobsList'
import type { PlanDetailReturn } from '../../api/plans/types'
import type { CreateDeliveryPlan } from '../../api/plans/useCreatePlanMutation'
import type { EditDeliveryPlan } from '../../api/plans/useEditPlanMutation'
import { calculateOrdering } from '../../api/routes/useRoutesList'
import {
  FORM_STATE,
  JOB_STATUS_ID,
  JOB_TYPE_ID,
  SCHEDULE_TYPE_ID,
  STOP_TYPE_ID,
} from '../../constants/job'
import { globalRecurringDialogModalSearchParamsSchema } from '../GlobalModals/schema'
import {
  getJobDetailsSchema,
  type JobDetailsFormType,
} from '../MapPanel/components/JobDetails/Form/schema'
import { getJobFormDefaultValues } from '../MapPanel/components/JobDetails/Form/utils'
import { parseStopsIntoFormSchema } from '../MapPanel/components/JobDetails/utils'
import {
  DAYS,
  INTERVAL,
  INTERVAL_LABEL,
  type RecurringDialogParams,
  type RecurringFormType,
} from './types'

export const MONTH_LIST = [
  'January',
  'February',
  'March',
  'April',
  'May',
  'June',
  'July',
  'August',
  'September',
  'October',
  'November',
  'December',
] as const

export const INTERVAL_OPTIONS: Array<{
  label: string
  value: INTERVAL
}> = [
  {
    label: INTERVAL_LABEL[INTERVAL.DAILY],
    value: INTERVAL.DAILY,
  },
  {
    label: INTERVAL_LABEL[INTERVAL.CUSTOM],
    value: INTERVAL.CUSTOM,
  },
  {
    label: INTERVAL_LABEL[INTERVAL.WEEKLY],
    value: INTERVAL.WEEKLY,
  },
  {
    label: INTERVAL_LABEL[INTERVAL.BIWEEKLY],
    value: INTERVAL.BIWEEKLY,
  },
  {
    label: INTERVAL_LABEL[INTERVAL.MONTHLY],
    value: INTERVAL.MONTHLY,
  },
  {
    label: INTERVAL_LABEL[INTERVAL.YEARLY],
    value: INTERVAL.YEARLY,
  },
] as const

export const INTERVAL_CONFIG_INITIAL_VALUES = {
  daysOfWeek: getDaysOfWeekOptions('short').map((day) => day.value),
  runEvery: null,
  yearlyDay: null,
  excludeDays: {
    Mon: false,
    Tue: false,
    Wed: false,
    Thu: false,
    Fri: false,
    Sat: false,
    Sun: false,
  },
}

export const recurringFormDefaultValues = {
  formState: FORM_STATE.STARTED,
  dates: {
    start: DeliveryDateTime.now().toJSDate(),
    end: null,
  },
  subuserId: null,
  routeName: '',
  driverId: null,
  isExcludeDays: false,
  interval: INTERVAL.DAILY,
  intervalConfig: INTERVAL_CONFIG_INITIAL_VALUES,
  jobs: [],
  stops: [],
}

export const jobFormDefaultValues = getJobFormDefaultValues(null)

export function parseJobApiToForm(
  job: FetchDeliveryJobDetails.Return[number]['formData'],
): JobDetailsFormType {
  return {
    formState: FORM_STATE.CREATE,
    jobId: job.jobId,
    scheduledDate: job.scheduledDate,
    driverId: job.driverId ?? null,
    subuserId: job.subuserId ?? null,
    scheduleTypeId: job.scheduleTypeId,
    sendToDriverAt: job.sendToDriverAt,
    allowedToStartAt: job.allowedToStartAt,
    referenceNumber: job.referenceNumber ?? null,
    labels: job.labels,
    specialEquipments: job.specialEquipments,
    optimizationPriority: job.optimizationPriority,
    hasCustomPriority: job.hasCustomPriority,
    stops: parseStopsIntoFormSchema(job.stops),
    items: job.items,
  }
}

export function getDaysOfWeekOptions(weekdayFormatOption: 'short' | 'long') {
  const date = DeliveryDateTime.now().startOf('week')

  return DAYS.map((day, index) => ({
    label: date.plus({ days: index }).toLocaleString({ weekday: weekdayFormatOption }),
    value: day,
  }))
}

function constructDefaultValues<T extends Record<string, any>>(
  data: Partial<T>,
  defaultValues: Partial<T>,
): T {
  return Object.fromEntries(
    Object.entries(defaultValues).map(([key, defaultValue]) => [
      key,
      data[key as keyof T] !== null &&
      data[key as keyof T] !== undefined &&
      data[key as keyof T] !== ''
        ? data[key as keyof T]
        : defaultValue,
    ]),
  ) as T
}

export function constructEditFormInitialValues(
  data: Partial<PlanDetailReturn> & { formState?: RecurringFormType['formState'] },
  jobs: FetchDeliveryJobDetails.Return = {},
): RecurringFormType {
  const values = constructDefaultValues(data, {
    recurrence: {},
    name: '',
    orderedStopIds: [],
    targetDriverId: null,
    scheduledTime: null,
    formState: FORM_STATE.VIEW,
    planId: undefined,
  })

  const scheduledTime = values.scheduledTime
    ? DeliveryDateTime.fromJSDate(
        new Date(values.scheduledTime.replace(/ (.*) /, ' $1+')),
      )
    : null

  const runEvery = match({
    freq: values.recurrence.freq,
    byMonth: values.recurrence.byMonth,
    byMonthDay: values.recurrence.byMonthDay,
    interval: values.recurrence.interval,
  })
    .with(
      {
        freq: INTERVAL.YEARLY,
        byMonth: P.not(P.nullish),
      },
      ({ byMonth }) => {
        const monthNames = Info.months('long')
        return monthNames[byMonth - 1]
      },
    )
    .with(
      { freq: INTERVAL.MONTHLY, byMonthDay: P.not(P.nullish) },
      ({ byMonthDay }) => byMonthDay,
    )
    .with(
      {
        freq: INTERVAL.DAILY,
        interval: P.when((interval) => interval && interval > 1),
      },
      ({ interval }) => interval,
    )
    .otherwise(() => null)

  const interval = match({
    freq: values.recurrence.freq,
    interval: values.recurrence.interval,
  })
    .with(
      {
        freq: INTERVAL.DAILY,
        interval: P.when((interval) => interval && interval > 1),
      },
      () => INTERVAL.CUSTOM,
    )
    .with({ freq: INTERVAL.WEEKLY, interval: 2 }, () => INTERVAL.BIWEEKLY)
    .otherwise(({ freq }) => freq)

  const excludeDays = match({
    freq: values.recurrence.freq,
    daysOfWeek: values.recurrence.daysOfWeek,
  })
    .with(
      {
        freq: P.when((value) =>
          includes([INTERVAL.DAILY, INTERVAL.MONTHLY, INTERVAL.YEARLY], value),
        ),
        daysOfWeek: P.array(P.string),
      },
      ({ daysOfWeek }) =>
        getDaysOfWeekOptions('short')
          .map((day) => day.value)
          .reduce(
            (acc, day) => {
              acc[day] = !daysOfWeek.includes(day)
              return acc
            },
            {} as Record<
              keyof RecurringFormType['intervalConfig']['excludeDays'],
              boolean
            >,
          ),
    )
    .otherwise(() => INTERVAL_CONFIG_INITIAL_VALUES.excludeDays)

  const isExcludeDays = match({
    freq: values.recurrence.freq,
    daysOfWeek: values.recurrence.daysOfWeek,
  })
    .with(
      {
        freq: P.when((value) =>
          [INTERVAL.DAILY, INTERVAL.MONTHLY, INTERVAL.YEARLY].includes(
            value as INTERVAL,
          ),
        ),
        daysOfWeek: P.array(P.string),
      },
      ({ daysOfWeek }) => daysOfWeek.length < 7,
    )
    .otherwise(() => false)

  const jobList = Object.values(jobs)

  const stops = match({ jobList, stops: values.orderedStopIds })
    .with({ jobList: P.when((value) => value.length), stops: [] }, ({ jobList }) =>
      getAllStopsFromJobsApi(jobList).map((item) => item.stopId),
    )
    .otherwise(() => values.orderedStopIds)

  return {
    formState: values.formState,
    planId: values.planId,
    dates: {
      start: scheduledTime ? scheduledTime.toJSDate() : null,
      end: values.recurrence.until
        ? DeliveryDateTime.fromISO(values.recurrence.until).toJSDate()
        : null,
    },
    routeName: values.name,
    driverId: values.targetDriverId,
    subuserId:
      jobList.length > 0 && jobList[0].formData.subuserId
        ? jobList[0].formData.subuserId
        : null,
    isExcludeDays: isExcludeDays,
    interval: interval ?? '',
    intervalConfig: {
      runEvery: runEvery ?? INTERVAL_CONFIG_INITIAL_VALUES.runEvery,
      excludeDays: excludeDays,
      daysOfWeek:
        values.recurrence.daysOfWeek ?? INTERVAL_CONFIG_INITIAL_VALUES.daysOfWeek,
      yearlyDay:
        values.recurrence.byMonthDay ?? INTERVAL_CONFIG_INITIAL_VALUES.yearlyDay,
    },
    jobs: jobList.map((job) => parseJobApiToForm(job.formData)),
    stops: stops,
  }
}

type ConstructFormPayloadOptions = {
  removeJobAndStopId?: boolean
  isStartDateChange?: boolean
  removeSubuserField?: boolean
  originalPlanReturn?: PlanDetailReturn
}

export function constructFormPayload(
  values: RecurringFormType,
  options: ConstructFormPayloadOptions,
): CreateDeliveryPlan.ApiInput | EditDeliveryPlan.ApiInput[number] {
  const {
    removeJobAndStopId,
    isStartDateChange,
    removeSubuserField,
    originalPlanReturn,
  } = options

  const frequency = match(values.interval)
    .with(INTERVAL.CUSTOM, () => INTERVAL.DAILY)
    .with(INTERVAL.BIWEEKLY, () => INTERVAL.WEEKLY)
    .otherwise((data) => data)

  const intervalFrequency = match({
    interval: values.interval,
    runEvery: values.intervalConfig.runEvery,
  })
    .with(
      {
        interval: INTERVAL.CUSTOM,
        runEvery: P.number,
      },
      ({ runEvery }) => runEvery,
    )
    .with({ interval: INTERVAL.BIWEEKLY }, () => 2)
    .otherwise(() => 1)

  const until = match(values.dates.end)
    .with(
      P.when((d): d is Date => d instanceof Date),
      (d) => d.toISOString(),
    )
    .otherwise(() => undefined)

  const daysOfWeek = match({
    interval: values.interval,
    excludeDays: values.intervalConfig.excludeDays,
    daysOfWeek: values.intervalConfig.daysOfWeek,
  })
    .with(
      {
        interval: P.when((value) =>
          includes([INTERVAL.DAILY, INTERVAL.MONTHLY, INTERVAL.YEARLY], value),
        ),
      },
      (data) => {
        const excludeDays = data.excludeDays
        const result = keys(pickBy(excludeDays, (value) => !value))
        return result.length < 7 ? result : undefined
      },
    )
    .with(
      {
        interval: P.not(INTERVAL.WEEKLY),
        daysOfWeek: P.when((value) => value.length === 7),
      },
      () => [],
    )
    .otherwise(({ daysOfWeek }) => daysOfWeek)

  const jobs = values.jobs.map((job) => {
    const { stops, jobId, ...restJobData } = parseJobFormToApiPayload(job)

    const parsedJobId = jobId && jobId > 1 && !removeJobAndStopId ? jobId : null

    const scheduleData = match({
      isStartDateChange,
      jobId: parsedJobId,
      startDate: values.dates.start,
    })
      .with(
        { isStartDateChange: true },
        { jobId: P.when((value) => !value) },
        ({ startDate }) => ({
          scheduleTypeId: startDate
            ? SCHEDULE_TYPE_ID.SCHEDULE
            : SCHEDULE_TYPE_ID.UNSCHEDULE,
          scheduledDeliveryTs: null,
        }),
      )
      .otherwise(() => ({
        scheduleTypeId: undefined,
        scheduledDeliveryTs: undefined,
      }))

    const subuserId = match(removeSubuserField)
      .with(false, () => values.subuserId)
      .otherwise(() => undefined)

    const transformedStops = stops.map(({ stopId, ...restStopData }) => ({
      ...restStopData,
      ...(stopId && stopId > 1 && !removeJobAndStopId && { stopId }),
      stopRef: String(stopId),
    }))

    return {
      ...restJobData,
      ...scheduleData,
      ...(parsedJobId && { jobId: parsedJobId }),
      subuserId,
      stops: transformedStops,
    }
  })

  const byMonthDay = match({
    interval: values.interval,
    runEvery: values.intervalConfig.runEvery,
    yearlyDay: values.intervalConfig.yearlyDay,
  })
    .with(
      { interval: INTERVAL.MONTHLY, runEvery: P.number },
      ({ runEvery }) => runEvery,
    )
    .with(
      { interval: INTERVAL.YEARLY, yearlyDay: P.number },
      ({ yearlyDay }) => yearlyDay,
    )
    .otherwise(() => undefined)

  const byMonth = match({
    interval: values.interval,
    runEvery: values.intervalConfig.runEvery,
  })
    .with({ interval: INTERVAL.YEARLY, runEvery: P.string }, ({ runEvery }) => {
      const monthNumber = DeliveryDateTime.fromFormat(runEvery, 'LLLL').month
      return monthNumber
    })
    .otherwise(() => undefined)

  const dtstart = values.dates.start
    ? DeliveryDateTime.fromJSDate(values.dates.start).startOf('day').toISO({
        suppressMilliseconds: true,
      })
    : match(values.formState)
        .with(FORM_STATE.EDIT, () => null)
        .otherwise(() => undefined)

  const updatedRecurrence = {
    freq: frequency,
    interval: intervalFrequency,
    daysOfWeek,
    dtstart,
    until,
    byMonth,
    byMonthDay,
  }

  if (originalPlanReturn !== undefined && originalPlanReturn !== null) {
    const typedPayload: EditDeliveryPlan.ApiInput[number] = {
      planId: originalPlanReturn.planId,
      ...(!isEqual(originalPlanReturn.name, values.routeName) && {
        name: values.routeName,
      }),
      ...(!isEqual(originalPlanReturn.targetDriverId, values.driverId) && {
        targetDriverId: values.driverId,
      }),
      ...(!isEqual(
        originalPlanReturn?.recurrence,
        removeUndefined(updatedRecurrence),
      ) && {
        recurrence: updatedRecurrence,
      }),
      contents: {
        jobs,
        orderedStopRefs: values.stops.map(String),
      },
    }
    return typedPayload
  } else {
    const typedPayload: CreateDeliveryPlan.ApiInput = {
      name: values.routeName,
      targetDriverId: values.driverId,
      recurrence: updatedRecurrence,
      contents: {
        jobs,
        orderedStopRefs: values.stops.map(String),
      },
    }
    return typedPayload
  }
}

export function constructRecurrenceSummary({
  interval,
  dates,
  intervalConfig,
  formatList,
}: Pick<RecurringFormType, 'interval' | 'intervalConfig' | 'dates'> & {
  formatList: ReturnType<typeof useIntl>['formatList']
}): React.ReactNode {
  const daysFrequency = intervalConfig.daysOfWeek
    ? formatList(
        intervalConfig.daysOfWeek
          .filter((v) => typeof v === 'string')
          .map((v) => ctIntl.formatMessage({ id: v })),
      )
    : ''

  const repeatingFrequency = match({
    interval,
    daysFrequency,
    runEvery: intervalConfig.runEvery,
    yearlyDay: intervalConfig.yearlyDay,
  })
    .with({ interval: INTERVAL.CUSTOM }, ({ runEvery }) =>
      ctIntl.formatMessage(
        { id: 'delivery.recurrence.frequency.custom' },
        { values: { daysFrequency: runEvery } },
      ),
    )
    .with({ interval: INTERVAL.DAILY }, () =>
      ctIntl.formatMessage({ id: 'delivery.recurrence.frequency.daily' }),
    )
    .with({ interval: INTERVAL.WEEKLY }, ({ daysFrequency }) =>
      ctIntl.formatMessage(
        { id: 'delivery.recurrence.frequency.weekly' },
        { values: { daysFrequency } },
      ),
    )
    .with({ interval: INTERVAL.BIWEEKLY }, () =>
      ctIntl.formatMessage({ id: 'delivery.recurrence.frequency.biweekly' }),
    )
    .with({ interval: INTERVAL.MONTHLY }, ({ runEvery }) =>
      ctIntl.formatMessage(
        { id: 'delivery.recurrence.frequency.monthly' },
        { values: { monthDay: runEvery } },
      ),
    )
    .with(
      { interval: INTERVAL.YEARLY, runEvery: P.string },
      ({ runEvery, yearlyDay }) =>
        ctIntl.formatMessage(
          { id: 'delivery.recurrence.frequency.yearly' },
          {
            values: {
              monthDay: yearlyDay,
              month: ctIntl.formatMessage({
                id: runEvery,
              }),
            },
          },
        ),
    )
    .otherwise(() => '')

  const startingOn = match(dates.start)
    .with(P.not(P.nullish), (startDate) =>
      ctIntl.formatMessage(
        { id: 'delivery.recurrence.startingOn' },
        {
          values: {
            startDate: DeliveryDateTime.fromJSDate(startDate as Date).toFormat('D'),
          },
        },
      ),
    )
    .otherwise(() => (
      <Stack
        gap={0.5}
        direction="row"
        alignItems="center"
      >
        <WarningAmberIcon sx={{ fontSize: 16, color: 'warning.main' }} />
        {ctIntl.formatMessage({
          id: 'Start date is unscheduled',
        })}
      </Stack>
    ))

  const unScheduledInfo = match(dates.start)
    .with(P.nullish, () => ctIntl.formatMessage({ id: 'once cadence begins' }))
    .otherwise(() => '')

  const repeating = match(repeatingFrequency)
    .with(P.not(P.nullish), () => (
      <FormattedMessage
        id="delivery.recurrence.repeatingOn"
        values={{ repeatingFrequency: <b>{repeatingFrequency}</b> }}
      />
    ))
    .otherwise(() => '')

  const endingOn = dates.end
    ? ctIntl.formatMessage(
        { id: 'delivery.recurrence.until' },
        {
          values: {
            endDate: DeliveryDateTime.fromJSDate(dates.end).toFormat('D'),
          },
        },
      )
    : ctIntl.formatMessage({ id: 'delivery.recurrence.withNoEndDate' })

  const excludeDays = formatList(
    Object.keys(intervalConfig.excludeDays)
      .filter(
        (key) =>
          intervalConfig.excludeDays[key as keyof typeof intervalConfig.excludeDays],
      )
      .map((key) => ctIntl.formatMessage({ id: key })),
  )

  const excluding = excludeDays
    ? ctIntl.formatMessage(
        { id: 'delivery.recurrence.excluding' },
        {
          values: { excludeDays },
        },
      )
    : ''

  return (
    <Typography variant="body2">
      <Stack direction="row">
        <Stack
          direction="row"
          alignItems="center"
          divider={<span>{',\u00A0'}</span>}
        >
          {[
            startingOn,
            repeating,
            endingOn,
            ...(excludeDays.length > 0 ? [excluding] : []),
            unScheduledInfo,
          ]}
        </Stack>
        {'.'}
      </Stack>
    </Typography>
  )
}

export function generateUniqueId() {
  return Number('0.' + Date.now() + Math.floor(Math.random() * 1000))
}

export const addStopsUniqueId = (data: JobDetailsFormType['stops']) => {
  if (data.jobTypeId === JOB_TYPE_ID.SINGLE) {
    return {
      ...data,
      single: { ...data.single, id: generateUniqueId() },
    }
  }

  return {
    ...data,
    pickup: { ...data.pickup, id: generateUniqueId() },
    dropoff: { ...data.dropoff, id: generateUniqueId() },
  }
}

export const addJobAndStopsUniqueId = (data: JobDetailsFormType) => {
  const jobId = generateUniqueId()

  const stopsWithIds = addStopsUniqueId(data.stops)

  return {
    ...data,
    jobId: jobId,
    stops: stopsWithIds,
  }
}

export function parseStopsFormToApi({
  jobId,
  stops,
  referenceNumber,
}: Pick<
  JobDetailsFormType,
  'jobId' | 'stops' | 'referenceNumber'
>): Array<JobsList.Stop> {
  const stopTypeMap: { [key: string]: number } = {
    single: STOP_TYPE_ID.SINGLE,
    pickup: STOP_TYPE_ID.PICKUP,
    dropoff: STOP_TYPE_ID.DROPOFF,
  }

  return orderBy(
    Object.entries(stops)
      .map(([key, stop]) => {
        if (key === 'jobTypeId') return null
        const latitude =
          stop.address.latitude !== '' ? Number.parseFloat(stop.address.latitude) : 0
        const longitude =
          stop.address.longitude !== '' ? Number.parseFloat(stop.address.longitude) : 0

        return {
          stopId: stop.id,
          stopTypeId: stopTypeMap[key],
          stopStatusId: 1,
          addressLine1: stop.address.addressLine1 ?? '',
          addressLine2: stop.address.addressLine2 ?? '',
          postalCode: stop.address.postalCode,
          latitude,
          longitude,
          latLng: `${latitude},${longitude}` as `${number},${number}`,
          customerName: stop.customer.name,
          customerId: stop.customerId,
          countryId: Number.parseInt(stop.address.countryId, 10),
          expectedDurationInMinutes: stop.duration,
          deliveryWindows: null,
          earliestTimeWindowInDateTime: null,
          latestTimeWindowInDateTime: null,
          formattedTimeWindow: null,
          isLate: false,
          jobId: jobId ?? 0,
          activityRejectedTs: null,
          jobStatusId: JOB_STATUS_ID.ASSIGN_LATER,
          referenceNumber: referenceNumber,
          orderId: '',
          rejectedReason: '',
          canReassign: false,
          ordering: 0,
          subUser: null,
          isGrouped: false,
        }
      })
      .filter((stop) => stop !== null),
    'stopTypeId',
    'asc',
  )
}

export function parseAllStopsFormToApi(
  jobs: Array<JobDetailsFormType>,
): Array<JobsList.Stop> {
  let stops: Array<JobsList.Stop> = []

  for (const job of jobs) {
    const jobStops = parseStopsFormToApi(job)

    stops = [...stops, ...jobStops]
  }

  return stops
}

export function parseJobsApiError(inputString: string): Record<number, Array<string>> {
  const jobData: Record<number, Array<string>> = {}
  const entries = inputString.split(', ')

  for (const entry of entries) {
    const parts = entry.match(/The \d+\.contents\.jobs\.(\d+)\.(\w+)/)
    if (parts) {
      const jobIndex = Number.parseInt(parts[1], 10)
      const attribute = parts[2]

      jobData[jobIndex] ||= []
      jobData[jobIndex].push(attribute)
    }
  }

  return jobData
}

export const getAllStopsFromJobsApi = (jobs: FetchDeliveryJobDetails.Return) =>
  Object.values(jobs).reduce<Array<JobsList.Stop>>(
    (acc, job) => [
      ...acc,
      ...parseStopsFormToApi({
        jobId: job.formData.jobId,
        referenceNumber: job.formData.referenceNumber || job.formData.orderId,
        stops: parseStopsIntoFormSchema(job.formData.stops),
      }),
    ],
    [],
  )

export const stopsWithGroupedOrdering = (
  getAllStops: Array<JobsList.Stop>,
  stopIds: Array<number>,
) => {
  const stopsLookup = keyBy(getAllStops, 'stopId')
  let currentOrdering = 0
  return stopIds.map((id, index) => {
    const stop = stopsLookup[id]
    const preStop = stopsLookup[stopIds[index - 1]]
    const nextStop = stopsLookup[stopIds[index + 1]]
    const { ordering, isGrouped } = calculateOrdering({
      currentOrdering,
      stop,
      preStop,
      nextStop,
    })
    currentOrdering = ordering

    return {
      ...stop,
      ordering,
      isGrouped,
    }
  })
}

export const getJobErrorIds = (jobList: Array<JobDetailsFormType>) => {
  const result = getJobDetailsSchema.array().safeParse(jobList)

  if (!result.success) {
    const jobIdsSet = new Set<number>()

    for (const error of result.error.issues) {
      const index = error.path[0] as number
      const jobId = jobList[index].jobId

      if (jobId) {
        jobIdsSet.add(jobId)
      }
    }

    return Array.from(jobIdsSet)
  }

  return []
}

export const getDeliveryRecurringDialogMainPath = (
  location: Location,
  params: RecurringDialogParams,
) =>
  `${location.pathname}?${buildRouteQueryStringKeepingExistingSearchParams({
    location,
    schema: globalRecurringDialogModalSearchParamsSchema,
    searchParams: {
      deliveryGlobalModal: { modal: 'recurring', params },
    },
    options: { shouldJsonStringify: true },
  })}`

function removeUndefined<T extends Record<string, any>>(obj: T): T {
  return Object.entries(obj).reduce((acc, [key, value]) => {
    if (value !== undefined) {
      acc[key as keyof T] = value
    }
    return acc
  }, {} as T)
}
