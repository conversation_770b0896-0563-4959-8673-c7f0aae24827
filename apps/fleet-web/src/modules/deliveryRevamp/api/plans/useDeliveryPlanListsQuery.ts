import { useQuery, type UseQueryOptions } from '@tanstack/react-query'
import type { DateTime, WeekdayNumbers } from 'luxon'
import { isNonNullish } from 'remeda'
import { match, P } from 'ts-pattern'
import type { Except } from 'type-fest'

import { apiCallerNoX } from 'src/api/api-caller'
import type { RoutesList } from 'src/modules/deliveryRevamp/api/routes/useRoutesList'
import { normalizeData } from 'src/modules/deliveryRevamp/helpers'

import { DAYS } from '../../components/RecurringDialog/types'
import { FREQUENCY, INTERVAL } from '../../constants/recurrence'
import { DeliveryDateTime } from '../../utils/deliveryDateTime'
import { DELIVERY_SYNC_DATA_INTERVAL } from '../constants'
import type { PlanDetailApiOutput } from './types'
import type { FetchDeliveryPlanDetails } from './useDeliveryPlanDetailsQuery'

export declare namespace FetchDeliveryPlanLists {
  type ApiOutput = {
    data: Array<PlanDetailApiOutput>
  }
  type ApiInput = {
    onlyRecurrent?: boolean
  }
  type Return = ReturnType<typeof parseDeliveryPlanList>
}

export const deliveryPlanListsQueryKey = (
  data: FetchDeliveryPlanLists.ApiInput = {},
) => {
  const baseKey = ['deliveryRevamp/planLists'] as const
  return [...baseKey, data] as const
}

function useDeliveryPlanListsQuery<TData = ReturnType<typeof parseDeliveryPlanList>>(
  data: FetchDeliveryPlanLists.ApiInput = {},
  options?: Except<
    UseQueryOptions<
      ReturnType<typeof parseDeliveryPlanList>,
      Error,
      TData,
      ReturnType<typeof deliveryPlanListsQueryKey>
    >,
    'queryKey'
  >,
) {
  return useQuery({
    queryKey: deliveryPlanListsQueryKey(data),
    queryFn: () => fetchDeliveryPlansList(data),
    refetchInterval: DELIVERY_SYNC_DATA_INTERVAL,
    ...options,
  })
}

async function fetchDeliveryPlansList(data: FetchDeliveryPlanLists.ApiInput) {
  return apiCallerNoX<FetchDeliveryPlanLists.ApiOutput>('delivery_plan_list', {
    data,
  }).then((res) => parseDeliveryPlanList(res.data))
}

const getNearestAvailableDate = (days: Array<string>): DateTime => {
  const today = DeliveryDateTime.now().startOf('day')
  const weekDays: Array<string> = [...DAYS]
  return days
    .map((day) => {
      const targetWeekday = weekDays.indexOf(day) + 1
      const targetDate = today.set({ weekday: targetWeekday as WeekdayNumbers })

      return targetDate < today ? targetDate.plus({ weeks: 1 }) : targetDate
    })
    .reduce((nearest, current) => (current < nearest ? current : nearest))
}

export const parseDeliveryPlanDetails = (
  planDetail: FetchDeliveryPlanDetails.ApiOutput['plan'],
) => {
  const planSnap = planDetail.planSnaps[planDetail.current_snap_id]
  const isRecurring = Boolean(planSnap.recurrence)

  const scheduledTime = match(planSnap)
    .with(
      {
        recurrence: { dtstart: P.not(P.nullish) },
      },
      ({ recurrence: { dtstart } }) => dtstart,
    )
    .with({ scheduled_time: P.not(P.nullish) }, ({ scheduled_time }) => scheduled_time)
    .otherwise(() => null)

  const firstOccurrence = match({
    scheduleTime: scheduledTime,
    recurrence: planSnap.recurrence,
  })
    .with(
      { scheduleTime: P.not(P.nullish), recurrence: P.not(P.nullish) },
      ({ scheduleTime, recurrence }) => {
        const startDate = DeliveryDateTime.fromJSDate(new Date(scheduleTime))
        return match(recurrence)
          .with(
            { freq: FREQUENCY.MONTHLY, byMonthDay: P.not(P.nullish) },
            ({ byMonthDay, daysOfWeek }) => {
              const day = byMonthDay

              const dateResult = DeliveryDateTime.fromObject({
                day,
                month: startDate.month,
                year: startDate.year,
              })

              if (!daysOfWeek || daysOfWeek.length === 0) return dateResult

              return dateResult < DeliveryDateTime.now()
                ? getNearestAvailableDate(daysOfWeek)
                : dateResult
            },
          )
          .with(
            {
              freq: FREQUENCY.YEARLY,
              byMonthDay: P.not(P.nullish),
              byMonth: P.not(P.nullish),
            },
            ({ byMonthDay, byMonth, daysOfWeek }) => {
              const day = byMonthDay
              const month = byMonth

              const dateResult = DeliveryDateTime.fromObject({
                day,
                month,
                year: startDate.year,
              })

              if (!daysOfWeek || daysOfWeek.length === 0) return dateResult

              return dateResult < DeliveryDateTime.now()
                ? getNearestAvailableDate(daysOfWeek)
                : dateResult
            },
          )
          .with(
            {
              freq: FREQUENCY.WEEKLY,
              daysOfWeek: P.not(P.nullish),
            },
            ({ daysOfWeek }) => {
              if (!daysOfWeek || daysOfWeek.length === 0) return startDate

              return startDate < DeliveryDateTime.now()
                ? getNearestAvailableDate(daysOfWeek)
                : startDate
            },
          )
          .with(
            {
              freq: FREQUENCY.DAILY,
            },
            ({ daysOfWeek }) => {
              if (!daysOfWeek || daysOfWeek.length === 0) return startDate

              return startDate < DeliveryDateTime.now()
                ? getNearestAvailableDate(daysOfWeek)
                : startDate
            },
          )
          .otherwise(() => startDate)
      },
    )
    .otherwise(() => null)

  const isRecurrenceExpired =
    firstOccurrence && firstOccurrence < DeliveryDateTime.now()

  const { recurrence } = planSnap

  const daysOfWeek: string[] | undefined = match({
    scheduledTime,
    freq: planSnap.recurrence?.freq,
    daysOfWeek: planSnap.recurrence?.daysOfWeek,
  })
    .with({ daysOfWeek: P.not(P.nullish) }, ({ daysOfWeek }) => daysOfWeek)
    .with(
      {
        daysOfWeek: P.nullish,
        freq: FREQUENCY.WEEKLY,
        scheduledTime: P.not(P.nullish),
      },
      ({ scheduledTime }) => [
        DeliveryDateTime.fromJSDate(new Date(scheduledTime)).toFormat('ccc'),
      ],
    )
    .otherwise(() => undefined)

  return {
    planId: planDetail.plan_id,
    routeId: `plan_${planDetail.plan_id}` as RoutesList.Route['routeId'],
    name: planDetail.name,
    targetDriverId: planSnap.target_driver_id,
    targetDriverName: '',
    scheduledTime: scheduledTime,
    orderedJobIds: planSnap.orderedJobIds,
    orderedStopIds: planSnap.orderedStopIds,
    uniqueCustomers: planSnap.uniqueCustomers,
    nextOccurrence: planSnap.nextOccurrence,
    createTs: planDetail.createTs,
    updateTs: planDetail.updateTs,
    recurrence: {
      ...(isNonNullish(daysOfWeek) && {
        daysOfWeek,
      }),
      ...(isNonNullish(planSnap.recurrence?.freq) && {
        freq: planSnap.recurrence.freq,
      }),
      ...(isNonNullish(planSnap.recurrence?.interval) && {
        interval: planSnap.recurrence.interval,
      }),
      ...(isNonNullish(planSnap.recurrence?.until) && {
        until: DeliveryDateTime.fromJSDate(new Date(planSnap.recurrence.until)).toISO({
          suppressMilliseconds: true,
        }),
      }),
      ...(isNonNullish(planSnap.recurrence?.dtstart) && {
        dtstart: DeliveryDateTime.fromJSDate(
          new Date(planSnap.recurrence.dtstart),
        ).toISO({ suppressMilliseconds: true }),
      }),
      ...(isNonNullish(planSnap.recurrence?.byMonthDay) && {
        byMonthDay: Number(planSnap.recurrence.byMonthDay),
      }),
      ...(isNonNullish(planSnap.recurrence?.byMonth) && {
        byMonth: Number(planSnap.recurrence.byMonth),
      }),
    },
    isRecurring,
    isRecurrenceExpired,
    isPlanOnDate: (targetDate: DateTime | null) => {
      if (recurrence) {
        const start = scheduledTime
          ? DeliveryDateTime.fromJSDate(new Date(scheduledTime)).startOf('day')
          : null

        if (!start) return targetDate === null
        if (!targetDate) return start === null

        // If target date is before the start date, return false
        if (targetDate < start) return false

        // If "until" is defined, ensure the target date is before or equal to it
        if (recurrence.until) {
          const untilDate = DeliveryDateTime.fromJSDate(
            new Date(recurrence.until),
          ).startOf('day')
          if (targetDate > untilDate) return false
        }

        // Calculate the difference in days between the target date and the start date
        const daysDifference = targetDate.diff(start, 'days').days
        const weekday = targetDate.toFormat('EEE')

        return match(recurrence)
          .with(
            { freq: FREQUENCY.DAILY, interval: INTERVAL.DEFAULT },
            ({ daysOfWeek }) => !daysOfWeek || daysOfWeek.includes(weekday),
          )
          .with(
            {
              freq: FREQUENCY.DAILY,
              interval: P.when((interval) => interval && interval > 1),
            },
            ({ interval }) => interval && daysDifference % interval === 0,
          )
          .with(
            { freq: FREQUENCY.WEEKLY, interval: INTERVAL.DEFAULT },
            ({ daysOfWeek }) =>
              daysOfWeek ? daysOfWeek.includes(weekday) : daysDifference % 7 === 0,
          )
          .with(
            { freq: FREQUENCY.WEEKLY, interval: INTERVAL.BI_WEEKLY },
            () => daysDifference % 14 === 0,
          )
          .with({ freq: FREQUENCY.MONTHLY }, ({ daysOfWeek, byMonthDay }) =>
            daysOfWeek
              ? daysOfWeek.includes(weekday) && targetDate.day === byMonthDay
              : targetDate.day === byMonthDay,
          )
          .with({ freq: FREQUENCY.YEARLY }, ({ daysOfWeek, byMonthDay, byMonth }) =>
            daysOfWeek
              ? daysOfWeek.includes(weekday) &&
                targetDate.month === byMonth &&
                targetDate.day === byMonthDay
              : targetDate.month === byMonth && targetDate.day === byMonthDay,
          )
          .otherwise(() => {
            throw new Error(`Unsupported frequency: ${recurrence.freq}`)
          })
      } else {
        const planDateTime = scheduledTime
          ? DeliveryDateTime.fromJSDate(new Date(scheduledTime)).startOf('day')
          : null
        if (planDateTime && targetDate) {
          return targetDate.hasSame(planDateTime, 'day')
        } else {
          return targetDate === planDateTime
        }
      }
    },
  }
}

export const parseDeliveryPlanList = (
  planDetails: FetchDeliveryPlanLists.ApiOutput['data'],
) =>
  normalizeData(
    planDetails.map((planDetail: FetchDeliveryPlanDetails.ApiOutput['plan']) =>
      parseDeliveryPlanDetails(planDetail),
    ),
    'planId',
  )

export default Object.assign(useDeliveryPlanListsQuery, {
  createKey: deliveryPlanListsQueryKey,
})
