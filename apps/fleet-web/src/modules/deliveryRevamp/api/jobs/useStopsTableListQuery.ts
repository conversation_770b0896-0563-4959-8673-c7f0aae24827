import { useQuery, type UseQueryOptions } from '@tanstack/react-query'
import type { Except } from 'type-fest'

import { apiCallerNoX } from 'api/api-caller'
import { makeQueryErrorHandlerWithToast } from 'api/helpers'
import type { ASSIGN_STATUS } from 'src/modules/deliveryRevamp/components/Filters/filtersSchemas'
import { createQuery } from 'src/util-functions/react-query-utils'

import type {
  ITEM_STATUS_ID,
  JOB_STATUS_ID,
  STOP_STATUS_ID,
  STOP_TYPE_ID,
} from '../../constants/job'
import {
  generateMockDeliveryData,
  parseStopsTableList,
} from '../../Pages/Table/DataGrid/mock-data'
import type { SortableFields } from '../../Pages/Table/DataGrid/types'
import type { JobId } from './types'
import type { ApiJobDetails } from './useDeliveryJobDetails'

export declare namespace FetchStopsTableListQuery {
  type FilterStopStatus =
    | 'not_started'
    | 'started'
    | 'arrived'
    | 'picked_up'
    | 'completed'
    | 'rejected'
    | 'cancelled'

  type ApiInput = {
    filters: {
      scheduledDeliveryTs: {
        from: string
        to: string
      } | null // null for unscheduled
      groupStops: boolean
      assignment: ASSIGN_STATUS
      referenceNumber: string | undefined
      customerName: string | undefined
      stopStatus: Array<FilterStopStatus> | undefined
      assignedToDriverIds: Array<string> | undefined
      driverName: string | undefined
      planName: string | undefined
      creationTs: { from: string; to: string } | undefined
      actualArrivalTs: { from: string; to: string } | undefined
      completedTs: { from: string; to: string } | undefined
      scheduledArrivalTs: { from: string; to: string } | undefined
      searchInput: string | undefined
      labels: Array<string> | undefined
    }
    sort: Partial<Record<SortableFields, 'asc' | 'desc'>>
    pagination: {
      page: number
      perPage: number
    }
  }

  type ApiOutput = {
    stops: Array<{
      stop_id: ApiJobDetails['stop'][number]['stop_id']
      job_id: JobId
      order_id: ApiJobDetails['order_id']
      scheduled_delivery_ts: ApiJobDetails['scheduled_delivery_ts']
      delivery_date: string | null
      allowed_to_start_at: string | null
      create_ts: ApiJobDetails['create_ts']
      delivery_driver_id: ApiJobDetails['driver']['delivery_driver_id'] | null
      driver_name: ApiJobDetails['driver']['first_name']
      plan_id: ApiJobDetails['plan_id']
      plan_name: string | null
      route_name: string | null
      reference_number: ApiJobDetails['reference_number']
      customer_id: ApiJobDetails['stop'][number]['customer_id']
      customer_name: ApiJobDetails['stop'][number]['customer_name']
      country_id: ApiJobDetails['stop'][number]['country_id'] | null
      stop_type_id: ApiJobDetails['stop'][number]['stop_type_id']
      stop_status_id: ApiJobDetails['stop'][number]['stop_status_id']
      job_status_id: ApiJobDetails['job_status_id']
      user_id: ApiJobDetails['userId']
      address_line_1: ApiJobDetails['stop'][number]['address_line_1']
      address_line_2: ApiJobDetails['stop'][number]['address_line_2']
      postal_code: ApiJobDetails['stop'][number]['postal_code']
      contact_number: ApiJobDetails['stop'][number]['contact_number']
      contact_code: ApiJobDetails['stop'][number]['contact_code']
      email: ApiJobDetails['stop'][number]['email']
      note: ApiJobDetails['stop'][number]['note']
      latitude: ApiJobDetails['stop'][number]['latitude']
      longitude: ApiJobDetails['stop'][number]['longitude']
      ordering: ApiJobDetails['stop'][number]['ordering']
      activity_started_ts: ApiJobDetails['stop'][number]['activity_started_ts']
      activity_arrived_ts: ApiJobDetails['stop'][number]['activity_arrived_ts']
      activity_completed_ts: ApiJobDetails['stop'][number]['activity_completed_ts']
      activity_rejected_ts: ApiJobDetails['stop'][number]['activity_rejected_ts']
      activity_duration: ApiJobDetails['stop'][number]['activity_duration']
      status_remarks: ApiJobDetails['stop'][number]['status_remarks']
      priority: ApiJobDetails['stop'][number]['priority']
      has_custom_priority: ApiJobDetails['stop'][number]['has_custom_priority']
      duration: ApiJobDetails['stop'][number]['duration']
      stopRef: ApiJobDetails['stop'][number]['stopRef']
      sum_actual_distance: ApiJobDetails['driverLegsInfo']['sumActualDistance']
      sum_actual_travel_time: ApiJobDetails['driverLegsInfo']['sumActualTravelTime']
      sum_projected_distance: ApiJobDetails['driverLegsInfo']['sumProjectedDistance']
      sum_projected_travel_time: ApiJobDetails['driverLegsInfo']['sumProjectedTravelTime']
      delivery_windows: ApiJobDetails['stop'][number]['delivery_windows']
      labels: Array<string>
      items_tracking_number: Array<Record<string, string>>
      items_and_todos: Array<
        Pick<
          ApiJobDetails['job_item'][number],
          | 'job_item_id'
          | 'job_item_status_id_dropoff'
          | 'job_item_status_id_pickup'
          | 'job_item_status_id_single'
          | 'description'
          | 'item_type_id'
        > & {
          job_item_todo:
            | Array<
                Pick<
                  ApiJobDetails['job_item'][number]['job_item_todo'][number],
                  | 'job_item_todo_id'
                  | 'todo_type_id'
                  | 'todo_status_id'
                  | 'complete_ts'
                  | 'description'
                  | 'note'
                  | 'status_remarks'
                  | 'ordering'
                  | 'tag'
                  | 'latitude'
                  | 'longitude'
                > & {
                  job_item_todo_image:
                    | Array<{
                        image_id: number
                        customer_name: string | null
                        image_url: string
                      }>
                    | undefined
                }
              >
            | undefined
        }
      >
      todos: Array<
        Pick<
          ApiJobDetails['stop'][number]['stop_todo'][number],
          | 'stop_todo_id'
          | 'todo_type_id'
          | 'todo_status_id'
          | 'update_ts'
          | 'description'
          | 'note'
          | 'status_remarks'
          | 'ordering'
          | 'tag'
          | 'latitude'
          | 'longitude'
        > & {
          stop_todo_image:
            | Array<{
                image_id: number
                customer_name: string | null
                image_url: string
                stop_todo_id: number
              }>
            | undefined
        }
      >
    }>
    meta: {
      currentPage: number
      lastPage: number
      perPage: number
      totalCompleted: number
      totalRejected: number
      totalRows: number
      totalUnassigned: number
    }
  }

  type TodosReturn = Array<{
    id: ApiOutput['stops'][number]['todos'][number]['stop_todo_id']
    todoTypeId: ApiOutput['stops'][number]['todos'][number]['todo_type_id']
    todoStatusId: ApiOutput['stops'][number]['todos'][number]['todo_status_id']
    date: {
      raw: ApiOutput['stops'][number]['todos'][number]['update_ts']
      date: Date
    } | null
    description: ApiOutput['stops'][number]['todos'][number]['description']
    note: ApiOutput['stops'][number]['todos'][number]['note']
    coords: {
      latitude: NonNullable<ApiOutput['stops'][number]['todos'][number]['latitude']>
      longitude: NonNullable<ApiOutput['stops'][number]['todos'][number]['longitude']>
    } | null
    statusRemarks: ApiOutput['stops'][number]['todos'][number]['status_remarks']
    images: Array<{
      id: NonNullable<
        ApiOutput['stops'][number]['todos'][number]['stop_todo_image']
      >[number]['image_id']
      url: NonNullable<
        ApiOutput['stops'][number]['todos'][number]['stop_todo_image']
      >[number]['image_url']
      customerName: NonNullable<
        ApiOutput['stops'][number]['todos'][number]['stop_todo_image']
      >[number]['customer_name']
    }>
  }>

  type Return = {
    stops: Array<{
      jobId: number
      orderId: string
      jobStatusId: JOB_STATUS_ID
      stopId: number
      stopStatusId: STOP_STATUS_ID
      stopTypeId: STOP_TYPE_ID
      scheduledDeliveryDateTime: { raw: string; date: Date } | null
      deliveryDate: { raw: string; date: Date } | null
      allowedToStartAt: { raw: string; date: Date } | null
      planId: number | null
      planName: string | null
      routeName: string | null
      deliveryDriverId: string | null
      driverName: string | null
      hasJobStarted: boolean
      referenceNumber: string | null
      customerName: string | null
      labels: Array<string>
      todos: TodosReturn
      items: Array<
        Except<
          ApiOutput['stops'][number]['items_and_todos'][number],
          'job_item_todo'
        > & { jobItemTodo: TodosReturn; jobItemStatus: ITEM_STATUS_ID }
      >
      addressLine1: string
      addressLine2: string
      createTs: { raw: string; date: Date }
      actualArrivalTs: { raw: string; date: Date } | null
      completedTs: { raw: string; date: Date } | null
      itemsTrackingNumber: Array<Record<string, string>>
      scheduledTravelDistanceInMeters: number | null
      actualDistanceInMeters: number | null
      travelDistancePct: number | null
      scheduledTravelTime: number | null
      actualTravelTime: number | null
      travelTimePct: number | null
      activityStartedTs: { raw: string; date: Date } | null
      activityArrivedTs: { raw: string; date: Date } | null
      activityCompletedTs: { raw: string; date: Date } | null
      activityRejectedTs: { raw: string; date: Date } | null
      statusRemarks: string | null
      expectedDurationInMinutes: number | null
      actualDurationInMinutes: number | null
      scheduledArrivalTs: {
        from: string
        to: string
      } | null
      deliveryWindows: Array<{
        stopId: number
        timeFrom: string
        timeTo: string
      }> | null
    }>
    meta: ApiOutput['meta']
  }
}

const createKey = (apiInput?: FetchStopsTableListQuery.ApiInput) =>
  ['deliveryRevamp/stopsTableList', ...(apiInput ? [apiInput] : [])] as const

const useStopsTableListQuery = <TData = FetchStopsTableListQuery.Return>(
  apiInput: FetchStopsTableListQuery.ApiInput,
  options?: Except<
    UseQueryOptions<
      FetchStopsTableListQuery.Return,
      Error,
      TData,
      ReturnType<typeof createKey>
    >,
    'queryKey'
  >,
) =>
  useQuery({
    ...stopsTableListQuery(apiInput),
    ...options,
  })

export const stopsTableListQuery = (apiInput: FetchStopsTableListQuery.ApiInput) =>
  createQuery({
    queryKey: createKey(apiInput),
    queryFn: () => fetchStopsTableList(apiInput),
    ...makeQueryErrorHandlerWithToast(),
  })

const fetchStopsTableList = async (apiInput: FetchStopsTableListQuery.ApiInput) => {
  // Edit this value to switch between mock and real data. Useful when endpoint is not returning data.
  const useMockData = false

  if (useMockData) {
    return new Promise<FetchStopsTableListQuery.Return>((resolve) => {
      window.setTimeout(() => {
        const mockData = generateMockDeliveryData(100)
        resolve(parseStopsTableList(mockData))
      }, 2000)
    })
  }

  return apiCallerNoX<FetchStopsTableListQuery.ApiOutput>('delivery_get_stops_list', {
    data: apiInput,
  }).then((res) => parseStopsTableList(res))
}

export default Object.assign(useStopsTableListQuery, {
  createKey,
})
