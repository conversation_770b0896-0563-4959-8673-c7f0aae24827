import { useMemo } from 'react'
import { match } from 'ts-pattern'
import type { Except } from 'type-fest'

import type { LocationId, VehicleId } from 'api/types'
import { GA4, type EventArgs } from 'src/shared/google-analytics4'
import {
  createDebugLogger,
  type DebugLoggerAllowList,
} from 'src/util-functions/logging'

import type {
  Ct_vision_get_terminal_list,
  Facility,
  FacilityVideoSourceGlobalId,
  FacilityVideoSourceId,
  VehicleVideoSourceGlobalId,
  VehicleVideoSourceId,
} from '../api/types'
import type { VehiclesPendingStandbyTransition } from './useVehicleStatusUpdateHandler'

export const getVehicleVideoSourceGlobalId = (
  vehicleId: VehicleId,
  videoSourceId: VehicleVideoSourceId,
) =>
  `VEHICLE_ID:${vehicleId}_VIDEOSOURCE_ID:${videoSourceId}` as VehicleVideoSourceGlobalId

export const getFacilityVideoSourceGlobalId = (
  facilityId: LocationId,
  videoSourceId: FacilityVideoSourceId,
) =>
  `FACILITY_ID:${facilityId}_VIDEOSOURCE_ID:${videoSourceId}` as FacilityVideoSourceGlobalId

export const getVideoSourceGlobalId = (
  args:
    | {
        terminalType: 'vehicle'
        vehicleId: VehicleId
        videoSourceId: VehicleVideoSourceId
      }
    | {
        terminalType: 'facility'
        facilityId: LocationId
        videoSourceId: FacilityVideoSourceId
      },
) =>
  args.terminalType === 'vehicle'
    ? getVehicleVideoSourceGlobalId(args.vehicleId, args.videoSourceId)
    : getFacilityVideoSourceGlobalId(args.facilityId, args.videoSourceId)

export const liveStreamGAEvent = (event: Except<EventArgs, 'category'>) =>
  GA4.event({ category: 'Vision - Live Stream', ...event })

export const doesFacilityContainAnyCamerasOnline = (facility: Facility) =>
  facility.activeDevices.length > 0

export const doesVehicleContainAnyCamerasOnline = (
  vehicle: Ct_vision_get_terminal_list.Vehicle,
) => vehicle.videoUnit.activeSources.length > 0

const isVehicleIgnitionOff = (vehicle: Ct_vision_get_terminal_list.Vehicle): boolean =>
  vehicle.statusClassName === 'ignition-off'

export const isVehicleConsideredOnline = ({
  vehicle,
  vehiclesPendingStandby,
}: {
  vehicle: Ct_vision_get_terminal_list.Vehicle
  vehiclesPendingStandby: VehiclesPendingStandbyTransition
}): boolean => {
  const isStandardOnline =
    doesVehicleContainAnyCamerasOnline(vehicle) && !isVehicleIgnitionOff(vehicle)

  const isPendingStandby =
    vehiclesPendingStandby.has(vehicle.id) &&
    doesVehicleContainAnyCamerasOnline(vehicle)

  return isStandardOnline || isPendingStandby
}

export const isVehicleConsideredStandby = ({
  vehicle,
  vehiclesPendingStandby,
}: {
  vehicle: Ct_vision_get_terminal_list.Vehicle
  vehiclesPendingStandby: VehiclesPendingStandbyTransition
}): boolean =>
  doesVehicleContainAnyCamerasOnline(vehicle) &&
  isVehicleIgnitionOff(vehicle) &&
  !vehiclesPendingStandby.has(vehicle.id)

export const isVehicleVisionOffline = (
  vehicle: Ct_vision_get_terminal_list.Vehicle,
): boolean => !doesVehicleContainAnyCamerasOnline(vehicle)

export const getVehicleStatusCategory = ({
  vehicle,
  vehiclesPendingStandby,
}: {
  vehicle: Ct_vision_get_terminal_list.Vehicle
  vehiclesPendingStandby: VehiclesPendingStandbyTransition
}): 'online' | 'standby' | 'offline' => {
  if (isVehicleConsideredOnline({ vehicle, vehiclesPendingStandby })) return 'online'
  if (isVehicleConsideredStandby({ vehicle, vehiclesPendingStandby })) return 'standby'
  return 'offline'
}

// Intialize the debug flag to false. If QAs, devs or the BE want to enable it to test end-to-end, they can do it by running this in the console:
// window.liveStreamPlayerDebug = true
window.liveStreamPlayerDebug = ENV.NODE_ENV === 'development' ? 'warn_error' : undefined

export const createVisionLiveStreamDebugLogger = (
  prefix: string,
  allowList?: DebugLoggerAllowList,
) => {
  const defaultAllowListOnInvalidOrUndefined: DebugLoggerAllowList =
    ENV.NODE_ENV === 'production' ? ['error'] : []
  return createDebugLogger(
    prefix,
    allowList ??
      match(window.liveStreamPlayerDebug)
        .returnType<DebugLoggerAllowList>()
        .with('warn_error', () => ['warn', 'error'])
        .with(true, () => 'all' /* use default */)
        .with(false, () => [])
        .with(undefined, () => defaultAllowListOnInvalidOrUndefined)
        .exhaustive((unexpectedValue) => {
          // We use exhaustive function form because anyone can set the window value to anything
          console.error(
            'Unexpected value for window.liveStreamPlayerDebug',
            unexpectedValue,
          )
          return defaultAllowListOnInvalidOrUndefined
        }),
  )
}

export function useVisionDebugLogger(prefix: string) {
  return useMemo(() => createVisionLiveStreamDebugLogger(prefix), [prefix])
}
