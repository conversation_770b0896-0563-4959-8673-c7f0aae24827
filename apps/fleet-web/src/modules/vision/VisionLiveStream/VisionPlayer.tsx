import { useEffect, useMemo, useState, type MouseEventHandler } from 'react'
import { Box, Chip, IconButton, Stack, styled, Typography } from '@karoo-ui/core'
import CheckCircleIcon from '@mui/icons-material/CheckCircle'
import CloseIcon from '@mui/icons-material/Close'
import { rgba } from 'polished'
import { FormattedMessage } from 'react-intl'
import { match, P } from 'ts-pattern'
import type { Except, SetOptional } from 'type-fest'

import type { VehicleId } from 'api/types'
import {
  getVisionAudioSetting,
  getVisionLiveStreamingStrategy,
} from 'duxs/user-sensitive-selectors'
import { useTypedSelector } from 'src/redux-hooks'
import { ctIntl } from 'src/util-components/ctIntl'

// import {
//   canPlayHEVCUsingMediaSourceResult,
//   hardwareAccelerationSupportInfo,
// } from 'src/util-functions/video-utils'

import {
  useFacilityDeviceVideoStreamMetaDataQuery,
  useVehicleVideoStreamMetaDataQuery,
} from '../api/queries'
import type { FacilityVideoSourceId, VehicleVideoSourceId } from '../api/types'
import type {
  FacilityNormalizedVideoSourceWithMetaData,
  NormalizedVideoSourceWithMetaData,
  VehicleNormalizedVideoSourceWithMetaData,
} from './types'
import { liveStreamGAEvent } from './utils'
import VideoLiveHLS, { type VideoLiveHLSProps } from './VideoLiveHLS'
import VideoWs, { type VideoWsExposedApi, type VideoWsProps } from './VideoWs'

// import VideoWs, { type VideoWsExposedApi } from './VideoWs'

export type VideoStreamHeaderProps = {
  title: string
  cameraLabel: string
  mb?: string | number
  onCloseIconButtonClick: MouseEventHandler<HTMLButtonElement>
  isHovered: boolean
  extraButtons?: React.ReactNode
}

function VideoStreamHeader({
  title,
  cameraLabel,
  mb,
  onCloseIconButtonClick,
  isHovered,
  extraButtons,
}: VideoStreamHeaderProps) {
  const chipLabel = (() => {
    if (!title) {
      return `${ctIntl.formatMessage({
        id: 'Camera',
      })} ${cameraLabel}`
    }
    return `${title} / ${ctIntl.formatMessage({
      id: 'Camera',
    })} ${cameraLabel}`
  })()

  return (
    <Stack
      direction="row"
      justifyContent="space-between"
      sx={{
        px: 1.5,
        py: 1,
        mb,
        width: '100%',
        position: 'absolute',
        background: isHovered
          ? 'linear-gradient(to top, rgba(0, 0, 0, 0), rgba(0, 0, 0, 0.7))'
          : 'none',
      }}
    >
      <Stack
        direction="row"
        alignItems="center"
        sx={{ gap: 1 }}
      >
        <Chip
          variant="filled"
          color="success"
          size="small"
          label={
            <Typography
              sx={{
                display: 'flex',
                alignItems: 'center',
                gap: 0.5,
              }}
            >
              {chipLabel}
              <CheckCircleIcon
                color="success"
                sx={{ width: '13px' }}
              />
            </Typography>
          }
          sx={{
            backgroundColor: 'rgba(0,0,0,0.5)',
            color: 'white',
          }}
        />
      </Stack>

      {isHovered && (
        <Stack
          direction="row"
          gap={1}
        >
          {extraButtons}
          <IconButton
            size="small"
            edge="end"
            sx={{ color: 'white', py: 0 }}
            onClick={onCloseIconButtonClick}
          >
            <CloseIcon fontSize="inherit" />
          </IconButton>
        </Stack>
      )}
    </Stack>
  )
}

export type VehicleVisionPlayerWithHeaderProps = {
  className?: string
  VideoWsProps: VehicleDeviceVisionPlayerProps['VideoWsProps']
  VideoHLSProps: VehicleDeviceVisionPlayerProps['VideoHLSProps']
  headerProps: Pick<
    VideoStreamHeaderProps,
    'title' | 'onCloseIconButtonClick' | 'cameraLabel' | 'extraButtons'
  >
  videoSourceMetaData: VehicleNormalizedVideoSourceWithMetaData
  videoSourceStatus: NormalizedVideoSourceWithMetaData['status']
}
export function VehicleVisionPlayerWithHeader({
  headerProps,
  videoSourceMetaData,
  className,
  ...props
}: VehicleVisionPlayerWithHeaderProps) {
  const [isHovered, setIsHovered] = useState(false)
  return (
    <Stack
      sx={{ gap: 1, position: 'relative' }}
      className={className}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      <VehicleDeviceVisionPlayer
        videoSourceMetaData={videoSourceMetaData}
        {...props}
      />
      {/* put the header code at the bottom to ensure it always overlays the video without complex z-index management. */}
      <VideoStreamHeader
        cameraLabel={headerProps.cameraLabel}
        title={headerProps.title}
        onCloseIconButtonClick={headerProps.onCloseIconButtonClick}
        isHovered={isHovered}
        extraButtons={headerProps.extraButtons}
      />
    </Stack>
  )
}

export type FacilityVisionPlayerWithHeaderProps = {
  className?: string
  VideoWsProps: FacilityDeviceVisionPlayerProps['VideoWsProps']
  headerProps: Pick<
    VideoStreamHeaderProps,
    'title' | 'onCloseIconButtonClick' | 'cameraLabel' | 'extraButtons'
  >
  videoSourceMetaData: FacilityNormalizedVideoSourceWithMetaData
  videoSourceStatus: NormalizedVideoSourceWithMetaData['status']
}
export function FacilityVisionPlayerWithHeader({
  headerProps,
  videoSourceMetaData,
  className,
  ...props
}: FacilityVisionPlayerWithHeaderProps) {
  const [isHovered, setIsHovered] = useState(false)
  return (
    <Stack
      sx={{ gap: 1, position: 'relative' }}
      className={className}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      <FacilityDeviceVisionPlayer
        videoSourceMetaData={videoSourceMetaData}
        {...props}
      />
      {/* put the header code at the bottom to ensure it always overlays the video without complex z-index management. */}
      <VideoStreamHeader
        cameraLabel={headerProps.cameraLabel}
        title={headerProps.title}
        onCloseIconButtonClick={headerProps.onCloseIconButtonClick}
        isHovered={isHovered}
        extraButtons={headerProps.extraButtons}
      />
    </Stack>
  )
}

type FacilityDeviceVisionPlayerProps = {
  className?: string
  VideoWsProps: SetOptional<
    Except<VideoWsProps, 'noSound' | 'url' | 'onWebSocketCloseFromServer'>,
    'VideoProps'
  >
  videoSourceMetaData: {
    id: FacilityVideoSourceId
  }
}

export function FacilityDeviceVisionPlayer({
  videoSourceMetaData,
  ...props
}: FacilityDeviceVisionPlayerProps) {
  const videoStreamQuery = useFacilityDeviceVideoStreamMetaDataQuery({
    deviceId: videoSourceMetaData.id,
  })

  return (
    <VisionPlayerWS
      {...props}
      videoStreamQueryResult={videoStreamQuery}
    />
  )
}

type VehicleDeviceVisionPlayerProps = {
  className?: string
  VideoHLSProps: SetOptional<
    Except<
      VideoLiveHLSProps,
      'noSound' | 'url' | 'key' | 'onFatalError' | 'onMediaEnded'
    >,
    'VideoProps'
  >
  VideoWsProps: SetOptional<
    Except<VideoWsProps, 'noSound' | 'url' | 'onWebSocketCloseFromServer'>,
    'VideoProps'
  >
  videoSourceMetaData: VehicleNormalizedVideoSourceWithMetaData
}

export const useCreateVehicleVideoStreamMetaDataQueryParams = () => {
  const visionLiveStreamingStrategy = useTypedSelector(getVisionLiveStreamingStrategy)
  const createParams = ({
    metaData,
  }: {
    metaData: {
      vehicleId: VehicleId
      id: VehicleVideoSourceId
    }
  }) =>
    match(visionLiveStreamingStrategy)
      .returnType<Parameters<typeof useVehicleVideoStreamMetaDataQuery>[0]>()
      .with('hls', () => ({
        type: 'hls',
        // ALWAYS request H.265 for bandwidth savings - transcoding handles unsupported browsers
        // CAN BE CHANGED IN THE FUTURE FOR THE CODE BELOW
        preferredCodec: 'h265',

        // Check if hardware acceleration is supported and if the browser can play HEVC
        // Sometimes, the hardware can play hevc, but the user disables hardware acceleration.
        // We combine both to ensure we get the most accurate result.
        // preferredCodec:
        //   hardwareAccelerationSupportInfo.status === 'likely_supported' &&
        //   canPlayHEVCUsingMediaSourceResult === 'supported'
        //     ? 'h265'
        //     : 'h264',
        vehicleId: metaData.vehicleId,
        videoSourceId: metaData.id,
      }))
      .with('web_socket', () => ({
        type: 'web_socket',
        vehicleId: metaData.vehicleId,
        videoSourceId: metaData.id,
      }))
      .exhaustive()

  return { createParams, visionLiveStreamingStrategy }
}

export function VehicleDeviceVisionPlayer({
  videoSourceMetaData,
  ...props
}: VehicleDeviceVisionPlayerProps) {
  const { createParams, visionLiveStreamingStrategy } =
    useCreateVehicleVideoStreamMetaDataQueryParams()

  const videoStreamQuery = useVehicleVideoStreamMetaDataQuery(
    createParams({ metaData: videoSourceMetaData }),
  )

  return match(visionLiveStreamingStrategy)
    .with('hls', () => (
      <VisionPlayerHLS
        {...props}
        videoStreamQueryResult={videoStreamQuery}
      />
    ))
    .with('web_socket', () => (
      <VisionPlayerWS
        {...props}
        videoStreamQueryResult={videoStreamQuery}
      />
    ))
    .exhaustive()
}

type VisionPlayerHLSProps = {
  className?: string
  VideoHLSProps: SetOptional<
    Except<
      VideoLiveHLSProps,
      'noSound' | 'url' | 'key' | 'onFatalError' | 'onMediaEnded'
    >,
    'VideoProps'
  >
  videoStreamQueryResult:
    | ReturnType<typeof useFacilityDeviceVideoStreamMetaDataQuery>
    | ReturnType<typeof useVehicleVideoStreamMetaDataQuery>
}

function VisionPlayerHLS({
  videoStreamQueryResult,
  className,
  VideoHLSProps,
}: VisionPlayerHLSProps) {
  const visionAudio = useTypedSelector(getVisionAudioSetting)
  const [reconnectState, setReconnectState] = useState<
    'closed_and_refetch_video_stream_url' | 'normal_streaming'
  >('normal_streaming')

  const videoStreamQuery = videoStreamQueryResult

  const finalUrlMeta = useMemo(():
    | { status: 'valid_url'; url: string }
    | { status: 'camera_offline_or_unavailable' | 'loading' } => {
    if (!videoStreamQuery.data) {
      return { status: 'loading' }
    }
    const { videoStreamUrlMeta } = videoStreamQuery.data

    if (videoStreamUrlMeta.status === 'camera_offline_or_unavailable') {
      console.warn('Invalid video stream url:', videoStreamUrlMeta)
      return { status: 'camera_offline_or_unavailable' }
    }

    const url = new URL(videoStreamUrlMeta.url)
    if (!visionAudio) {
      url.searchParams.set('nosound', '1')
    }
    return {
      status: 'valid_url',
      url: url.toString(),
    }
  }, [videoStreamQuery.data, visionAudio])

  useEffect(() => {
    if (
      reconnectState === 'closed_and_refetch_video_stream_url' &&
      finalUrlMeta.status === 'valid_url'
    ) {
      setReconnectState('normal_streaming')
    }
  }, [finalUrlMeta, reconnectState])

  if (videoStreamQuery.isLoading) {
    return null
  }

  const onVideoMounted = (videoEl: HTMLVideoElement) => {
    videoEl.addEventListener('fullscreenchange', () => {
      liveStreamGAEvent({
        action: 'vision_stream_fullscreen',
        metaData: document.fullscreenElement ? 1 : 0,
      })
    })
    VideoHLSProps?.onMounted?.(videoEl)
  }

  return (
    <Box
      className={className}
      sx={{
        position: 'relative',
        width: 'auto',
        backgroundColor: 'rgba(0,0,0,0.05)',
      }}
    >
      {match([reconnectState, finalUrlMeta])
        .with([P.any, { status: 'camera_offline_or_unavailable' }], () => (
          <StyledWrapper>
            <FormattedMessage
              id="vision.liveStream.retryNotification.cameraOff"
              values={{
                n: (chunks) => (
                  <Typography
                    variant="caption"
                    sx={({ palette }) => ({
                      color: palette.getContrastText(palette.common.black),
                    })}
                  >
                    {chunks}
                  </Typography>
                ),
                c: (chunks) => (
                  <Typography
                    variant="caption"
                    sx={({ palette }) => ({
                      color: rgba(palette.getContrastText(palette.text.secondary), 0.5),
                    })}
                  >
                    {chunks}
                  </Typography>
                ),
              }}
            />
          </StyledWrapper>
        ))
        .with([P.any, { status: 'loading' }], () => <StyledWrapper />)
        .with([P.any, { status: 'valid_url' }], ([, { url }]) => (
          <VideoLiveHLS
            // Remount and reset the player when url changes
            key={url}
            {...VideoHLSProps}
            VideoProps={{
              ...VideoHLSProps?.VideoProps,
              sx: {
                width: '100%',
                height: '100%',
                maxHeight: 600,
                ...VideoHLSProps?.VideoProps?.sx,
              },
            }}
            url={url}
            onMounted={onVideoMounted}
            noSound={!visionAudio}
            onMediaEnded={() => {
              videoStreamQuery.refetch()
              setReconnectState('closed_and_refetch_video_stream_url')
            }}
            onFatalError={() => {
              // TODO: Review this logic. Currently the same as VIdeoWs
              if (videoStreamQuery.isError) {
                // Do not refetch if for some reason the endpoint threw an error.
                // This is to avoid infinite loop of refetching and throwing an error in case the BE returns 500
                return
              }
              videoStreamQuery.refetch()
              setReconnectState('closed_and_refetch_video_stream_url')
            }}
            // TODO: Even when using HLS, when the camera goes offline, we should expect some sort of error or event that causes HLS to stop streaming.
            // Need to identify this and handle it similarly to web sockets so that we can get the new camera offline/online status too.

            // onWebSocketCloseFromServer={() => {
            //   // dispatch the event video_stream_closed_by_server_and_query_refetch, with video source meta
            //   // and call the query's refetch directly since it's from the event
            //   videoStreamQuery.refetch()
            //   setReconnectState('closed_and_refetch_video_stream_url')
            // }}
          />
        ))
        .exhaustive()}
    </Box>
  )
}

type VisionPlayerWSProps = {
  className?: string
  VideoWsProps: SetOptional<
    Except<VideoWsProps, 'noSound' | 'url' | 'onWebSocketCloseFromServer'>,
    'VideoProps'
  >
  videoStreamQueryResult:
    | ReturnType<typeof useFacilityDeviceVideoStreamMetaDataQuery>
    | ReturnType<typeof useVehicleVideoStreamMetaDataQuery>
}

function VisionPlayerWS({
  videoStreamQueryResult,
  className,
  VideoWsProps,
}: VisionPlayerWSProps) {
  const visionAudio = useTypedSelector(getVisionAudioSetting)
  const [reconnectState, setReconnectState] = useState<
    'closed_and_refetch_video_stream_url' | 'normal_streaming'
  >('normal_streaming')

  const videoStreamQuery = videoStreamQueryResult

  const finalUrlMeta = useMemo(():
    | { status: 'valid_url'; url: string }
    | { status: 'camera_offline_or_unavailable' | 'loading' } => {
    if (!videoStreamQuery.data) {
      return { status: 'loading' }
    }
    const { videoStreamUrlMeta } = videoStreamQuery.data

    if (videoStreamUrlMeta.status === 'camera_offline_or_unavailable') {
      console.warn('Invalid video stream url:', videoStreamUrlMeta)
      return { status: 'camera_offline_or_unavailable' }
    }

    const [, urlSuffix] = videoStreamUrlMeta.url.replace('?', '').split('://')
    return {
      status: 'valid_url',
      url: `wss://${urlSuffix}?ws=1${visionAudio ? '' : '&nosound'}`,
    }
  }, [videoStreamQuery.data, visionAudio])

  useEffect(() => {
    if (
      reconnectState === 'closed_and_refetch_video_stream_url' &&
      finalUrlMeta.status === 'valid_url'
    ) {
      setReconnectState('normal_streaming')
    }
  }, [finalUrlMeta, reconnectState])

  if (videoStreamQuery.status === 'pending') {
    return null
  }

  const onVideoMounted = (
    videoEl: HTMLVideoElement,
    publicApiRef: React.MutableRefObject<VideoWsExposedApi>,
  ) => {
    videoEl.addEventListener('fullscreenchange', () => {
      liveStreamGAEvent({
        action: 'vision_stream_fullscreen',
        metaData: document.fullscreenElement ? 1 : 0,
      })
    })
    VideoWsProps?.onMounted?.(videoEl, publicApiRef)
  }

  return (
    <Box
      className={className}
      sx={{
        position: 'relative',
        width: 'auto',
        backgroundColor: 'rgba(0,0,0,0.05)',
      }}
    >
      {match([reconnectState, finalUrlMeta])
        .with([P.any, { status: 'camera_offline_or_unavailable' }], () => (
          <StyledWrapper>
            <FormattedMessage
              id="vision.liveStream.retryNotification.cameraOff"
              values={{
                n: (chunks) => (
                  <Typography
                    variant="caption"
                    sx={({ palette }) => ({
                      color: palette.getContrastText(palette.common.black),
                    })}
                  >
                    {chunks}
                  </Typography>
                ),
                c: (chunks) => (
                  <Typography
                    variant="caption"
                    sx={({ palette }) => ({
                      color: rgba(palette.getContrastText(palette.text.secondary), 0.5),
                    })}
                  >
                    {chunks}
                  </Typography>
                ),
              }}
            />
          </StyledWrapper>
        ))
        .with([P.any, { status: 'loading' }], () => <StyledWrapper />)
        .with([P.any, { status: 'valid_url' }], ([, { url }]) => (
          <VideoWs
            // Remount and reset the player when url changes
            key={url}
            {...VideoWsProps}
            VideoProps={{
              ...VideoWsProps?.VideoProps,
              sx: {
                width: '100%',
                height: '100%',
                maxHeight: 600,
                ...VideoWsProps?.VideoProps?.sx,
              },
            }}
            url={url}
            onMounted={onVideoMounted}
            noSound={!visionAudio}
            onWebSocketCloseFromServer={() => {
              // dispatch the event video_stream_closed_by_server_and_query_refetch, with video source meta
              // and call the query's refetch directly since it's from the event
              videoStreamQuery.refetch()
              setReconnectState('closed_and_refetch_video_stream_url')
            }}
          />
        ))
        .exhaustive()}
    </Box>
  )
}

const StyledWrapper = styled(Box)(({ theme }) =>
  theme.unstable_sx({
    display: 'flex',
    flexDirection: 'column',
    alignItems: 'center',
    justifyContent: 'center',
    p: 3,
    height: '100%',
    backgroundColor: 'common.black',
    textAlign: 'center',
    aspectRatio: 16 / 9,
  }),
)
