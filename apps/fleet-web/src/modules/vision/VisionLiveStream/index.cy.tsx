/// <reference types="@testing-library/cypress" />
import { times } from 'lodash'
import { match } from 'ts-pattern'

import { duxsMocks } from 'src/cypress-ct/mocks/duxs'
import { endpointsMocks } from 'src/cypress-ct/mocks/endpoints'
import {
  cyExpect,
  exhaustiveEndpointCallCheck,
  mountWithProviders,
} from 'src/cypress-ct/utils'
import type { FacilityDeviceId } from 'src/modules/lists/Facilities/api/types'

import type { UserAvailableCameraTerminalTypes } from '../api/types'
import VisionLiveStream from './index'
import { FacilityListItemSize } from './types'

const findFacilityListItemByListIndex = (index: number) =>
  cy.findByTestId(`facility-list-item_byIndex=${index}`, { exact: false })

const findFacilityListItemById = (id: string) =>
  cy.findByTestId(`facility-list-item_byId=${id}`, { exact: false })

describe('VisionLiveStream', () => {
  // Verify that the initial screen is rendered . Also prevents flakiness in tests that follow
  const shouldHaveRenderedInitialScreen = () =>
    cy.findByText(/to start a stream/i).should('exist')

  const shouldHaveSettingsPanelFunctionalityWorking = () => {
    cy.findByTestId('SettingsIcon').click()
    cy.findByRole('tooltip').contains(/List Settings/i)

    cy.findByRole('tooltip').findByTestId('CloseIcon').click()
    cy.findByRole('tooltip').should('not.exist')
  }

  it('shows default message to start a stream when entering the page', () => {
    cy.intercept('POST', '/jsonrpc/public/index.php', (req) => {
      match(req.body)
        .with({ method: 'ct_vision_get_terminal_list' }, () => {
          req.reply(endpointsMocks.ct_vision_get_terminal_list().successReply)
        })
        .with({ method: 'ct_fetch_user_available_camera_terminal_types' }, () => {
          req.reply({
            delay: 50,
            body: {
              id: 10,
              result:
                'FACILITIES_AND_VEHICLES' satisfies UserAvailableCameraTerminalTypes,
              error: null,
            },
          })
        })
        .with({ method: 'ct_vision_check_user_credit' }, () => {
          req.reply(endpointsMocks.ct_vision_check_user_credit().have_data)
        })
        .otherwise(exhaustiveEndpointCallCheck)
    })

    mountWithProviders(<VisionLiveStream />, {
      reduxOptions: {
        preloadedState: {
          user: duxsMocks.user().mockState,
        },
      },
    })

    shouldHaveRenderedInitialScreen()
  })

  it('expands facility list item when clicking the expand icon button and collapses item when clicking collapse icon', () => {
    cy.intercept('POST', '/jsonrpc/public/index.php', (req) => {
      match(req.body)
        .with({ method: 'ct_vision_get_terminal_list' }, () => {
          req.reply(endpointsMocks.ct_vision_get_terminal_list().successReply)
        })
        .with({ method: 'ct_fetch_user_available_camera_terminal_types' }, () => {
          req.reply({
            delay: 50,
            body: {
              id: 10,
              result:
                'FACILITIES_AND_VEHICLES' satisfies UserAvailableCameraTerminalTypes,
              error: null,
            },
          })
        })
        .with({ method: 'ct_vision_check_user_credit' }, () => {
          req.reply(endpointsMocks.ct_vision_check_user_credit().have_data)
        })
        .otherwise(exhaustiveEndpointCallCheck)
    })

    mountWithProviders(<VisionLiveStream />, {
      reduxOptions: {
        preloadedState: {
          user: duxsMocks.user().mockState,
        },
      },
    })
    shouldHaveRenderedInitialScreen()

    const collapsedFacilityListItemHeight = FacilityListItemSize.COLLAPSED

    const checkIfFacilityItemIsCollapsed = () => {
      findFacilityListItemByListIndex(0).should(($el) => {
        const height = $el.outerHeight()
        cyExpect(height).toBeEqual(collapsedFacilityListItemHeight)
      })
    }

    checkIfFacilityItemIsCollapsed()

    findFacilityListItemByListIndex(0).findByTestId('ExpandMoreIcon').click()
    findFacilityListItemByListIndex(0).should(($el) => {
      const height = $el.outerHeight()
      expect(height).to.be.above(collapsedFacilityListItemHeight)
    })
    findFacilityListItemByListIndex(0).findByTestId('ExpandLessIcon').click()

    checkIfFacilityItemIsCollapsed()

    findFacilityListItemByListIndex(0)
      .findByLabelText(/Open Blueprint/i)
      .trigger('mouseover')
    cy.findByRole('tooltip').contains(/Open Blueprint/i)
  })

  it('opens facility blue print drawer and base functionality works', () => {
    cy.intercept('POST', '/jsonrpc/public/index.php', (req) => {
      match(req.body)
        .with({ method: 'ct_vision_get_terminal_list' }, () => {
          req.reply(
            endpointsMocks.ct_vision_get_terminal_list({
              result: {
                ct_vision_terminals: [
                  {
                    terminal_type: 'facility',
                    facility_id: '1',
                    facility_name: 'Facility 1',
                    facility_description: 'Facility 1 description',
                    devices: times(4, (i) => {
                      const coords: { latitude: number; longitude: number } = match(i)
                        .with(0, () => ({ latitude: 0, longitude: 0 }))
                        .with(1, () => ({ latitude: 0.44, longitude: 1.2 }))
                        .with(2, () => ({ latitude: 2.1, longitude: 2.4 })) // purposefully going wayyy out of "normal" max bounds (which is a valid use case)
                        .with(3, () => ({ latitude: -0.4, longitude: -1.21 }))
                        .otherwise(() => ({ latitude: 0, longitude: 0 }))

                      return {
                        device_id: i.toString() as FacilityDeviceId,
                        device_name: `Device ${i}`,
                        online: true,
                        ...coords,
                      }
                    }),
                  },
                ],
                ct_vision_vehicle_groups: {},
              },
            }).successReply,
          )
        })
        .with({ method: 'ct_fleet_get_facility_details' }, () => {
          req.reply(
            endpointsMocks.ct_fleet_get_facility_details().successReply({
              facilityId: '1',
              devices: [], // Not relevant for this test
            }),
          )
        })
        .with({ method: 'ct_fetch_user_available_camera_terminal_types' }, () => {
          req.reply({
            delay: 50,
            body: {
              id: 10,
              result:
                'FACILITIES_AND_VEHICLES' satisfies UserAvailableCameraTerminalTypes,
              error: null,
            },
          })
        })
        .with({ method: 'ct_vision_check_user_credit' }, () => {
          req.reply(endpointsMocks.ct_vision_check_user_credit().have_data)
        })
        .otherwise(exhaustiveEndpointCallCheck)
    })

    mountWithProviders(<VisionLiveStream />, {
      reduxOptions: {
        preloadedState: { user: duxsMocks.user().mockState },
      },
    })
    shouldHaveRenderedInitialScreen()

    findFacilityListItemById('1')
      .findByLabelText(/Open Blueprint/i)
      .click()

    cy.findByTestId('facility-blueprint-drawer')
      .findByText('Select Cameras')
      .should('exist')

    // Wait for the bottom left google image to load (means the map is loaded)
    cy.findByAltText(/Google/i, { timeout: 14000 }).should('exist')

    cy.findByTestId('map-container').then(($el) => {
      const mapContainer = $el.get(0)
      const mapContainerRect = mapContainer.getBoundingClientRect()

      // Check that all devices are visible on the map (even the ones that are out of bounds)
      // The map should expand the bounds to accommodate all devices. At least, if we zoom all out, we should see everything.
      // On our tests we're not zooming out, but we're checking that all our test devices are visible and that's good enough to identify faulty logic
      cy.get('[data-map-marker-type="device"]')
        .should('have.length', 4)
        .each(($el) => {
          const device = $el.get(0)
          const deviceRect = device.getBoundingClientRect()
          const deviceHalfWidth = deviceRect.width / 2
          const deviceHalfHeight = deviceRect.height / 2

          // Check that the center of the device marker is always within boundaries.
          // Sometimes the whole device might not be visible but that's ok as long as most of it is, and you can click it
          expect(deviceRect.left + deviceHalfWidth).to.be.above(mapContainerRect.left)
          expect(deviceRect.right - deviceHalfWidth).to.be.below(mapContainerRect.right)
          expect(deviceRect.top + deviceHalfHeight).to.be.above(mapContainerRect.top)
          expect(deviceRect.bottom - deviceHalfHeight).to.be.below(
            mapContainerRect.bottom,
          )
        })
    })

    cy.findByTestId('facility-blueprint-drawer').findByTestId('CloseIcon').click()
    cy.findByTestId('facility-blueprint-drawer').should('not.exist')
  })

  describe('when to show or hide main tabs', () => {
    it('shows both "Facilities" and "Vehicles" tab when user has cameras of both types and have "Facilities" selected by default. Should have settings panel working when vehicles tab is selected', () => {
      cy.intercept('POST', '/jsonrpc/public/index.php', (req) => {
        match(req.body)
          .with({ method: 'ct_vision_get_terminal_list' }, () => {
            req.reply(endpointsMocks.ct_vision_get_terminal_list().successReply)
          })
          .with({ method: 'ct_fetch_user_available_camera_terminal_types' }, () => {
            req.reply({
              delay: 50,
              body: {
                id: 10,
                result:
                  'FACILITIES_AND_VEHICLES' satisfies UserAvailableCameraTerminalTypes,
                error: null,
              },
            })
          })
          .with({ method: 'ct_vision_check_user_credit' }, () => {
            req.reply(endpointsMocks.ct_vision_check_user_credit().have_data)
          })
          .otherwise(exhaustiveEndpointCallCheck)
      })

      mountWithProviders(<VisionLiveStream />, {
        reduxOptions: {
          preloadedState: { user: duxsMocks.user().mockState },
        },
      })
      shouldHaveRenderedInitialScreen()

      cy.findByRole('tab', { name: /Facilities/i })
        .should('exist')
        .should('have.attr', 'aria-selected', 'true')

      cy.findByTestId('SettingsIcon').should('not.exist')

      cy.findByRole('tab', { name: /Vehicles/i })
        .should('exist')
        .click()

      shouldHaveSettingsPanelFunctionalityWorking()

      // should be able to toggle back to facilities tab. There was a stupid bug where we couldn't go back to facilities tab so I prefer to add a test case for it :D
      cy.findByRole('tab', { name: /Facilities/i }).click()

      cy.findByRole('tab', { name: /Facilities/i }).should(
        'have.attr',
        'aria-selected',
        'true',
      )
    })

    it('does not show any main tab when user has only cameras of type "Facilities"', () => {
      cy.intercept('POST', '/jsonrpc/public/index.php', (req) => {
        match(req.body)
          .with({ method: 'ct_vision_get_terminal_list' }, () => {
            req.reply(endpointsMocks.ct_vision_get_terminal_list().successReply)
          })
          .with({ method: 'ct_fetch_user_available_camera_terminal_types' }, () => {
            req.reply({
              delay: 50,
              body: {
                id: 10,
                result: 'FACILITIES' satisfies UserAvailableCameraTerminalTypes,
                error: null,
              },
            })
          })
          .with({ method: 'ct_vision_check_user_credit' }, () => {
            req.reply(endpointsMocks.ct_vision_check_user_credit().have_data)
          })
          .otherwise(exhaustiveEndpointCallCheck)
      })

      mountWithProviders(<VisionLiveStream />, {
        reduxOptions: {
          preloadedState: { user: duxsMocks.user().mockState },
        },
      })
      shouldHaveRenderedInitialScreen()

      cy.findByRole('tab', { name: /Facilities/i }).should('not.exist')

      cy.findByTestId('SettingsIcon').should('not.exist')

      cy.findByRole('tab', { name: /Vehicles/i }).should('not.exist')
    })

    it('should not show any main tab when user has only cameras of type "Vehicles" and have settings panel working', () => {
      cy.intercept('POST', '/jsonrpc/public/index.php', (req) => {
        match(req.body)
          .with({ method: 'ct_vision_get_terminal_list' }, () => {
            req.reply(endpointsMocks.ct_vision_get_terminal_list().successReply)
          })
          .with({ method: 'ct_fetch_user_available_camera_terminal_types' }, () => {
            req.reply({
              delay: 50,
              body: {
                id: 10,
                result: 'VEHICLES' satisfies UserAvailableCameraTerminalTypes,
                error: null,
              },
            })
          })
          .with({ method: 'ct_vision_check_user_credit' }, () => {
            req.reply(endpointsMocks.ct_vision_check_user_credit().have_data)
          })
          .otherwise(exhaustiveEndpointCallCheck)
      })

      mountWithProviders(<VisionLiveStream />, {
        reduxOptions: {
          preloadedState: { user: duxsMocks.user().mockState },
        },
      })
      shouldHaveRenderedInitialScreen()

      cy.findByRole('tab', { name: /Facilities/i }).should('not.exist')
      cy.findByRole('tab', { name: /Vehicles/i }).should('not.exist')

      shouldHaveSettingsPanelFunctionalityWorking()
    })
  })

  describe('show data usage alert overlay', () => {
    // Re-add once data usage is complete
    it.skip('should show no data and have overlay alert when user have data limitations', () => {
      cy.intercept('POST', '/jsonrpc/public/index.php', (req) => {
        match(req.body)
          .with({ method: 'ct_vision_get_terminal_list' }, () => {
            req.reply(endpointsMocks.ct_vision_get_terminal_list().successReply)
          })
          .with({ method: 'ct_fetch_user_available_camera_terminal_types' }, () => {
            req.reply({
              delay: 50,
              body: {
                id: 10,
                result: 'VEHICLES' satisfies UserAvailableCameraTerminalTypes,
                error: null,
              },
            })
          })
          .with({ method: 'ct_vision_check_user_credit' }, () => {
            req.reply(endpointsMocks.ct_vision_check_user_credit().no_data_have_credit)
          })
          .with({ method: 'ct_vision_get_vehicle_video_stream_v2' }, () => {
            req.reply(
              endpointsMocks.ct_vision_get_vehicle_video_stream_v2().successReply,
            )
          })
          .otherwise(exhaustiveEndpointCallCheck)
      })

      mountWithProviders(<VisionLiveStream />, {
        reduxOptions: {
          preloadedState: { user: duxsMocks.user().mockState },
        },
      })
      cy.findByTestId(`vehicle-list-item_byIndex=${'0'}`)
        .findByTestId(`vehicle-video-source-button_byIndex=${'0'}`)
        .click()
      cy.findByTestId(
        `CameraUsageOverlayBlockerContainer_byVehicleId=${'237220892'}`,
      ).should('exist')
    })
  })
})
