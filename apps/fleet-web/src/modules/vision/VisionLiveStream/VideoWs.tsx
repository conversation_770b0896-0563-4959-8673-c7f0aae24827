import React, {
  useCallback,
  useEffect,
  useImperative<PERSON>andle,
  useLayoutEffect,
  useMemo,
  useRef,
  useState,
  useSyncExternalStore,
} from 'react'
import {
  Box,
  CircularProgressDelayed,
  Fade,
  IconButton,
  Slider,
  Stack,
  styled,
} from '@karoo-ui/core'
import FullscreenIcon from '@mui/icons-material/Fullscreen'
import FullscreenExitIcon from '@mui/icons-material/FullscreenExit'
import VolumeMuteIcon from '@mui/icons-material/VolumeMute'
import VolumeUpIcon from '@mui/icons-material/VolumeUp'
import * as R from 'remeda'
import screenfull from 'screenfull'
import { match, P } from 'ts-pattern'

import { useEffectEvent, useEventHandler } from 'src/hooks/useEventHandler'
import { useFullscreen } from 'src/hooks/useFullscreen'
import { safeAppendBuffer } from 'src/util-functions/safe-utils'
import { isNonEmptyString } from 'src/util-functions/string-utils'

import { useVisionDebugLogger } from './utils'

type SocketReadyState = 'CONNECTING' | 'OPEN' | 'CLOSING' | 'CLOSED'

export type VideoWsExposedApi = {
  closeSocketConnection: (reason: string) => void
  recreateSocketConnectionIfNeeded: (reason: string) => void
  getVideoWebSocketState: () => SocketReadyState | 'NOT_INITIALIZED'
}

export type VideoWsApiRef = React.Ref<VideoWsExposedApi>

export const useVideoWsApiRef = () =>
  React.useRef<VideoWsExposedApi>({} as VideoWsExposedApi)

export type VideoWsProps = {
  logId: string
  onMounted?: (
    videoEl: HTMLVideoElement,
    publicApiRef: React.MutableRefObject<VideoWsExposedApi>,
  ) => void
  onUnmount?: () => void
  children?: React.ReactNode
  // Since we need to imperatively control the web socket connection state from outside the component, this is a decent way to do it.
  // [ Inspired by mui DataGrid apiRef prop approach - https://github.com/mui/mui-x/blob/next/packages/grid/x-data-grid/src/hooks/core/useGridApiInitialization.ts#L81 ]
  apiRef?: VideoWsApiRef
  url: string
  VideoProps: VideoProps
  className?: string
  noSound?: boolean
  // It happens when the websocket connection is closed by the server, and we need to reconnect to the server.
  onWebSocketCloseFromServer: () => void
}

// This is now used to correctly auto-detect appropriate codec
const defaultCodecString = 'video/mp4; codecs="avc1.4d4014, mp4a.40.2"'

type VideoMeta = {
  currentStream: {
    webSocket: WebSocket
    queue: Array<ArrayBuffer>
    mediaSource: MediaSource
    buffer: SourceBuffer | null
  } | null
  wasVideoPlayingBeforeVisibilityChangedToHidden: true | null
  onMessageTimes: number
  lastMessageUnixMs: number | null
  lastTimeRecordedByStallDetection: number | null
  isBufferLeadCurrentlyLessThanMinusXSeconds: boolean
  lastTimeWaitingUnixMs: number | null
}

const initialVideoMeta = {
  currentStream: null,
  wasVideoPlayingBeforeVisibilityChangedToHidden: null,
  onMessageTimes: 0,
  lastMessageUnixMs: null,
  lastTimeRecordedByStallDetection: null,
  isBufferLeadCurrentlyLessThanMinusXSeconds: false,
  lastTimeWaitingUnixMs: null,
} as const satisfies VideoMeta

export default function VideoWs({
  logId,
  url,
  onMounted: onMountedProp,
  onUnmount: onUnmountProp,
  apiRef: apiRefProp,
  children,
  className,
  VideoProps,
  noSound = false,
  onWebSocketCloseFromServer: onWebSocketCloseFromServerProp,
}: VideoWsProps) {
  const logPrefix = useMemo(
    // Show the last part of the url as the log prefix (the one that usually matters)
    () => `[Cartrack][VideoWs][${logId}] -`,
    [logId],
  )
  const logger = useVisionDebugLogger(logPrefix)
  const mountedRef = useRef(false)
  const webSocketsCloseReasonsFromClientMapRef = useRef<Map<WebSocket, string>>(
    new Map(),
  )
  const videoRef = useRef<HTMLVideoElement | null>(null)
  const videoContainerRef = useRef<HTMLDivElement | null>(null)
  const containerMouseMoveInFullscreenTimeoutRef = useRef<number | null>(null)
  const videoMetaRef = useRef<VideoMeta>(initialVideoMeta)
  const publicApiRef = useRef() as React.MutableRefObject<VideoWsExposedApi>
  const [isVideoHovered, setIsVideoHovered] = useState(false)
  const isFullscreen = useFullscreen()

  const showLoadingState = useSyncExternalStore(
    useCallback(
      (cb) => {
        videoRef.current?.addEventListener('readystatechange', cb)
        videoRef.current?.addEventListener('waiting', cb)
        videoRef.current?.addEventListener('canplay', cb)
        videoRef.current?.addEventListener('playing', cb)
        videoRef.current?.addEventListener('pause', cb)
        videoRef.current?.addEventListener('ended', cb)
        return () => {
          videoRef.current?.removeEventListener('readystatechange', cb)
          videoRef.current?.removeEventListener('waiting', cb)
          videoRef.current?.removeEventListener('canplay', cb)
          videoRef.current?.removeEventListener('playing', cb)
          videoRef.current?.removeEventListener('pause', cb)
          videoRef.current?.removeEventListener('ended', cb)
        }
      },
      [videoRef],
    ),
    useCallback(() => {
      if (!videoRef.current) {
        return true
      }
      if (videoRef.current.paused) {
        return false
      }
      if (videoRef.current.played.length === 0) {
        return true
      }

      const readyState = getVideoReadyState(videoRef.current.readyState)

      return readyState === 'HAVE_NOTHING' || readyState === 'HAVE_METADATA'
    }, [videoRef]),
  )

  useEffect(() => {
    if (isFullscreen) {
      return // do nothing
    }

    // If we are leaving fullscreen, we want to clear the timeout that was only meant to work while in fullscreen
    if (containerMouseMoveInFullscreenTimeoutRef.current) {
      window.clearTimeout(containerMouseMoveInFullscreenTimeoutRef.current)
    }
  }, [isFullscreen])

  useEffect(() => {
    logger.debug(['url', url])
  }, [url, logger])

  useImperativeHandle(
    apiRefProp,
    (): VideoWsExposedApi => ({
      closeSocketConnection: (reason: string) => {
        publicApiRef.current.closeSocketConnection(reason)
      },
      recreateSocketConnectionIfNeeded: (reason: string) => {
        publicApiRef.current.recreateSocketConnectionIfNeeded(reason)
      },
      getVideoWebSocketState: () => publicApiRef.current.getVideoWebSocketState(),
    }),
    [publicApiRef],
  )

  const onMount = useEffectEvent(
    (
      videoEl: HTMLVideoElement,
      publicApiRef: React.MutableRefObject<VideoWsExposedApi>,
    ) => {
      onMountedProp?.(videoEl, publicApiRef)
      setupNewFreshSocketConnection('onMount')
    },
  )
  useEffect(() => {
    // Using mountedRef "hack" to prevent issues with upcoming react concurrent rendering
    // onMounted should really on be called once so it's better to make sure it is.
    if (mountedRef.current) {
      return
    }
    mountedRef.current = true

    if (videoRef.current) {
      // publicRef is guaranteed to be set by the time this is called because it's set in useLayoutEffect
      onMount(videoRef.current, publicApiRef)
    }
  }, [])

  useEffect(() => {
    const interval = window.setInterval(function ensureBufferLead() {
      // This is to ensure that the buffer is always ahead of the current time
      try {
        const videoEl = videoRef.current
        if (!videoEl) {
          return
        }

        const bufferEnd =
          videoEl.buffered.length > 0 ? videoEl.buffered.end(0) : videoEl.currentTime
        const lead = bufferEnd - videoEl.currentTime

        // Note that this negative lead doesn't represent an actual 230 seconds.
        // In these situations, the lead grows slowly negative and this means the real time that passed is greater than the lead.
        const allowedNegativeLeadInSeconds = -230
        if (lead < allowedNegativeLeadInSeconds) {
          logger.debug([
            `buffer lead is less than ${allowedNegativeLeadInSeconds} seconds. Something is very wrong. Reconnecting...`,
          ])
          publicApiRef.current.recreateSocketConnectionIfNeeded('buffer_lead_too_low')
          return
        }

        videoMetaRef.current.isBufferLeadCurrentlyLessThanMinusXSeconds = lead < -10
        const rate = (() => {
          if (videoMetaRef.current.isBufferLeadCurrentlyLessThanMinusXSeconds) {
            logger.debug([
              'buffer lead is less than -10 seconds, taking special measures',
            ])

            // When the connection is very bad, e.g Cambodia, we get negative leads sometimes.
            // There is not point in adjusting the playback at this point
            return 1
          }
          if (lead < -1) {
            return 1
          }
          if (lead < 2) {
            return 0.95
          }
          if (lead >= 2 && lead <= 8) {
            return 1
          }
          if (lead >= 8 && lead <= 16) {
            return 1.05
          }

          // Lead is to high. Need to speed up the playback even further
          return 1.1
        })()

        if (videoEl.playbackRate !== rate) {
          logger.debug(['playback rate changed', { lead, rate }])
        }

        videoEl.playbackRate = rate
      } catch (e) {
        logger.error(['Failed to ensure buffer lead', e])
      }
    }, 1000)

    return () => {
      window.clearInterval(interval)
    }
  }, [logger])

  useEffect(() => {
    const interval = window.setInterval(() => {
      const videoEl = videoRef.current
      if (!videoEl) {
        return
      }
      const lastMessageUnixMs = videoMetaRef.current.lastMessageUnixMs
      const thresholdInMs = 25_000
      if (
        // Make sure the video has played at least 1 frame. Otherwise, we don't give enough time to reconnect and we risk a loop of reconnecting.
        videoEl.played.length > 0 &&
        lastMessageUnixMs &&
        Date.now() - lastMessageUnixMs > thresholdInMs
      ) {
        const msg = `No packets in at least ${
          thresholdInMs / 1000
        }s, forcing reconnect...`
        logger.warn([msg])
        publicApiRef.current.recreateSocketConnectionIfNeeded(msg)
      }
    }, 5_000)

    return () => window.clearInterval(interval)
  }, [logger])

  useEffect(() => {
    const interval = window.setInterval(() => {
      const videoEl = videoRef.current
      const { currentStream: stream } = videoMetaRef.current
      if (!videoEl || !stream) {
        return
      }

      const socketState = getWebSocketState(stream.webSocket)
      if (socketState === 'CLOSING' || socketState === 'CONNECTING') {
        logger.warn(['[stall detection] WebSocket is closing/connecting, do nothing'])
        return
      }
      const { lastTimeRecordedByStallDetection } = videoMetaRef.current

      // Verify if the video is stalled and has more data to play
      logger.debug([
        '[stall detection]',
        {
          lastTimeRecordedByStallDetection,
          videoCurrentTime: videoEl.currentTime,
          videoEl,
        },
      ])
      if (
        !videoEl.paused &&
        !videoEl.ended &&
        // Make sure the video has played at least 1 frame. Otherwise, we don't give enough time to reconnect and we risk a loop of reconnecting.
        videoEl.played.length > 0 &&
        videoEl.readyState > 2
      ) {
        // Make sure we compare seconds as integers. There can be some precision issues.
        // This way it is more reliable.
        if (
          videoEl.currentTime.toFixed(0) ===
          lastTimeRecordedByStallDetection?.toFixed(0)
        ) {
          logger.warn(['Video seems stalled — reconnecting...'])
          publicApiRef.current.recreateSocketConnectionIfNeeded('video_stalled')
        }
        videoMetaRef.current.lastTimeRecordedByStallDetection = videoEl.currentTime
      }
    }, 10_000)

    return () => window.clearInterval(interval)
  }, [logger])

  useEffect(() => {
    const onVisibilityChange = () => {
      const videoStatus = (() => {
        if (!videoRef.current) {
          return 'video_not_mounted' as const
        }
        if (videoRef.current.paused) {
          return 'video_paused' as const
        }
        return 'video_playing' as const
      })()

      logger.debug([
        'Visibility change start',
        {
          visibilityState: document.visibilityState,
          videoStatus,
          wasVideoPlayingBeforeVisibilityChangedToHidden:
            videoMetaRef.current.wasVideoPlayingBeforeVisibilityChangedToHidden,
        },
      ])
      switch (document.visibilityState) {
        case 'hidden': {
          if (videoStatus === 'video_playing') {
            videoMetaRef.current.wasVideoPlayingBeforeVisibilityChangedToHidden = true
          } else {
            videoMetaRef.current.wasVideoPlayingBeforeVisibilityChangedToHidden = null
          }
          break
        }
        case 'visible': {
          if (videoMetaRef.current.wasVideoPlayingBeforeVisibilityChangedToHidden) {
            logger.debug(['Playing video on visibility change to visible'])
            videoRef.current?.play().catch((e) => {
              logger.error(['Failed to play on visibility change', e])
              // If play fails on visibility change, try reconnecting
              publicApiRef.current.recreateSocketConnectionIfNeeded(
                'visibility_recovery',
              )
            })
          }
          break
        }
        default:
          break
      }
    }

    document.addEventListener('visibilitychange', onVisibilityChange)
    return () => {
      document.removeEventListener('visibilitychange', onVisibilityChange)
    }
  }, [logger])

  // Auto-detect codec based on data received
  const detectCodec = useCallback(
    (dataBuffer: ArrayBuffer) => {
      try {
        const data = new Uint8Array(dataBuffer)
        // Check for H.264 Baseline profile
        if (data[2] === 0x04 && data[3] === 0xdf) {
          return 'video/mp4; codecs="avc1.42c01e, mp4a.40.2"'
        }
        // Check for HEVC
        if (data[2] === 0x0e && data[3] === 0x5a) {
          return 'video/mp4; codecs="hvc1.1.6.H150.90, mp4a.40.2"'
        }
        // Default to standard H.264 High profile
        return defaultCodecString
      } catch (e) {
        logger.error(['Codec detection failed', e])
        return defaultCodecString
      }
    },
    [logger],
  )

  const maybeTrimVideoBuffer = useCallback(() => {
    if (!videoRef.current || !videoMetaRef.current.currentStream?.buffer) {
      return
    }

    const videoEl = videoRef.current
    const buffer = videoMetaRef.current.currentStream.buffer

    if (buffer.updating) {
      // Can not trim the buffer while it is updating
      return
    }

    if (videoEl.buffered.length > 0) {
      const bufferStart = videoEl.buffered.start(0)
      const bufferEnd = videoEl.buffered.end(0)
      const maxBufferSizeInSeconds = 15
      if (bufferEnd - bufferStart > maxBufferSizeInSeconds) {
        const trimEnd = bufferEnd - maxBufferSizeInSeconds

        logger.debug([
          'trimming video buffer',
          {
            trimStart: 0,
            trimEnd,
            maxBufferSizeInSeconds,
          },
        ])
        buffer.remove(0, trimEnd)
      }
    }
  }, [logger])

  // Queue processing function to handle incoming data chunks
  const processQueue = useCallback(() => {
    const videoMeta = videoMetaRef.current
    const videoEl = videoRef.current

    if (
      !videoEl ||
      !videoMeta.currentStream ||
      !videoMeta.currentStream.buffer ||
      videoMeta.currentStream.queue.length === 0 ||
      videoMeta.currentStream.buffer.updating
    ) {
      return
    }

    const { queue, buffer } = videoMeta.currentStream

    try {
      const chunk = queue.shift() as (typeof queue)[number]

      const result = safeAppendBuffer(buffer, chunk)

      maybeTrimVideoBuffer()

      if (result.isErr()) {
        logger.warn(['Source buffer appendBuffer error', result.error])

        if (result.error.type === 'QuotaExceededError') {
          // Re-add the chunk we failed to append
          queue.unshift(chunk)

          // Logic above is apparently not enough to fix the issue at the moment.
          // To make our player reliable, we just reconnect to the server to clear the buffer and recreate everything.

          publicApiRef.current.recreateSocketConnectionIfNeeded('source_buffer_full')
        } else if (result.error.type === 'InvalidStateError') {
          // If the video element is in an error state, we need to reset it
          if (videoEl.error) {
            // When videoEl.error is set, the video element is in an error state which is pretty much unrecoverable.
            // We reconnect the socket to reset everything.
            publicApiRef.current.recreateSocketConnectionIfNeeded('invalid_state_error')
          }
        } else {
          logger.error(['Source buffer appendBuffer unknown error. Do nothing.'])
        }
      }

      // Continue processing queue recursively if we have more data
      if (videoMeta.currentStream.queue.length > 0) {
        processQueue()
      }
    } catch (e) {
      logger.error(['Process queue error', e])
    }
  }, [logger, maybeTrimVideoBuffer])

  // Listen for buffer update completion
  const onSourceBufferUpdateEnd = useEventHandler(() => {
    processQueue()
  })

  const onWebSocketCloseFromServer = useEffectEvent(
    onWebSocketCloseFromServerProp ?? R.doNothing,
  )

  const onSocketClose = useEventHandler(
    ({
      closeEvent,
      socketBeingClosed,
      mediaSourceUsed,
    }: {
      closeEvent: CloseEvent
      socketBeingClosed: WebSocket
      mediaSourceUsed: MediaSource
    }) => {
      const closeReasonFromClient =
        webSocketsCloseReasonsFromClientMapRef.current.get(socketBeingClosed)
      logger.debug([
        'Web Socket onclose event',
        {
          socketBeingClosed,
          mediaSourceUsed,
          webSocketCloseEventReason: closeEvent.reason,
          code: closeEvent.code,
          closeReasonFromClient,
        },
      ])

      // remove the listeners in case
      cleanupSocketListeners(socketBeingClosed)

      // if the media source is still open, clean it up
      if (mediaSourceUsed.readyState === 'open') {
        for (const buffer of mediaSourceUsed.sourceBuffers) {
          if (buffer) {
            try {
              buffer.removeEventListener('updateend', onSourceBufferUpdateEnd)
              mediaSourceUsed.removeSourceBuffer(buffer)
            } catch (e) {
              logger.error(['Failed to removeSourceBuffer', e])
            }
          }
        }

        try {
          mediaSourceUsed.endOfStream()
        } catch (e) {
          logger.error(['Failed to endOfStream MediaSource', e])
        }
      }

      if (closeReasonFromClient !== undefined) {
        webSocketsCloseReasonsFromClientMapRef.current.delete(socketBeingClosed)
        return
      }

      // No reason from client, so this socket was closed by server
      logger.debug(['onWebSocketCloseFromServer'])
      onWebSocketCloseFromServer()
    },
  )

  const onSocketError = useEventHandler((e: Event) => {
    logger.error(['WebSocket error event', e])
  })

  const onSocketMessage = useEventHandler(({ data: data_ }: MessageEvent) => {
    const videoMeta = videoMetaRef.current
    const data = data_ as ArrayBuffer
    videoMetaRef.current.lastMessageUnixMs = Date.now()
    if (!videoMetaRef.current.currentStream) {
      return
    }
    // Collect first few chunks to detect codec
    if (videoMeta.onMessageTimes < 2) {
      videoMetaRef.current.onMessageTimes++
      videoMetaRef.current.currentStream.queue.push(data)

      const mediaSource = videoMeta.currentStream?.mediaSource
      if (!mediaSource) {
        throw new Error('MediaSource not found')
      }

      // After receiving second chunk, set up the SourceBuffer with appropriate codec
      if (videoMeta.onMessageTimes === 2 && mediaSource.readyState === 'open') {
        const codecString = detectCodec(data)

        try {
          logger.debug(['Adding SourceBuffer with codec', codecString])
          videoMetaRef.current.currentStream.buffer =
            mediaSource.addSourceBuffer(codecString)
          videoMetaRef.current.currentStream.buffer.mode = 'sequence'
          videoMetaRef.current.currentStream.buffer.addEventListener(
            'updateend',
            onSourceBufferUpdateEnd,
          )
          processQueue()
        } catch (e) {
          logger.error(['Failed to add SourceBuffer', e])
        }
      }
      return
    }

    // Normal processing of chunks
    if (videoMetaRef.current.currentStream.buffer === null) {
      // Still waiting for buffer to be created, just queue the data
      videoMetaRef.current.currentStream.queue.push(data)
      return
    }

    videoMetaRef.current.currentStream.queue.push(data)
    if (!videoMetaRef.current.currentStream.buffer.updating) {
      processQueue()
    }
  })

  const cleanupSocketListeners = useCallback(
    (socket: WebSocket) => {
      socket.removeEventListener('message', onSocketMessage)
      socket.removeEventListener('error', onSocketError)
    },
    [onSocketMessage, onSocketError],
  )

  const closeSocketConnection = useCallback(
    (nonEmptyReason: string) => {
      const { currentStream: stream } = videoMetaRef.current
      if (!stream) {
        return
      }
      const { webSocket, mediaSource } = stream

      webSocketsCloseReasonsFromClientMapRef.current.set(webSocket, nonEmptyReason)
      logger.debug(['closeSocketConnection', { nonEmptyReason }])

      webSocket.close()

      if (stream.buffer) {
        try {
          mediaSource.removeSourceBuffer(stream.buffer)
          mediaSource.endOfStream()
        } catch {
          // No buffer to cleanup
        }
      }

      videoMetaRef.current = initialVideoMeta
    },
    [logger],
  )

  const setupNewFreshSocketConnection = useEventHandler((reason: string) => {
    const videoEl = videoRef.current
    if (!videoEl) {
      return
    }

    logger.debug(['setupNewFreshSocketConnection', { reason }])

    const webSocket = new WebSocket(url)
    webSocket.binaryType = 'arraybuffer'

    const mediaSource = new MediaSource()

    videoMetaRef.current = {
      ...initialVideoMeta,
      currentStream: { webSocket, mediaSource, queue: [], buffer: null },
    }

    mediaSource.addEventListener('sourceopen', function () {
      logger.debug(['MediaSource sourceopen event'])
      // Very important. Previously we were setting duration=1 and at some point, the video stopped and we clicking play button would not work.
      mediaSource.duration = Infinity

      videoEl.play().catch((e) => {
        logger.error(['Failed to play on sourceopen', e])
      })

      webSocket.addEventListener(
        'close',
        function (closeEvent) {
          onSocketClose({
            closeEvent,
            socketBeingClosed: webSocket,
            mediaSourceUsed: mediaSource,
          })
        },
        // Once is important here so that we don't have to remove the listener when closing the socket
        // which would not allow us to send the payload above to onSocketClose
        { once: true },
      )
      webSocket.addEventListener('message', onSocketMessage)
      webSocket.addEventListener('error', onSocketError)
    })

    const currentSrc = videoEl.src
    // eslint-disable-next-line no-param-reassign
    videoEl.src = window.URL.createObjectURL(mediaSource)
    videoEl.playbackRate = 0.95

    if (isNonEmptyString(currentSrc)) {
      logger.debug(['Revoking old object URL', { oldUrl: currentSrc }])
      // Prevent memory leak by revoking the old object URL, if any
      window.URL.revokeObjectURL(currentSrc)
    }
  })

  const recreateSocketConnectionIfNeeded = useCallback(
    (reason: string) => {
      const videoEl = videoRef.current
      if (!videoEl) {
        return
      }

      const fnName = 'maybeRecreateSocketConnection'

      if (videoMetaRef.current.currentStream) {
        const socketBeingClosed = videoMetaRef.current.currentStream.webSocket
        const webSocketState = getWebSocketState(socketBeingClosed)
        if (webSocketState === 'CONNECTING' || webSocketState === 'CLOSING') {
          logger.debug([
            `${fnName} [cancelled]`,
            {
              reason: `No need to setup a new connection since one is already ${webSocketState}`,
            },
          ])
          return
        }

        closeSocketConnection(`${fnName}`)
        setupNewFreshSocketConnection(`${reason}. Previous connection was closed`)
      } else {
        setupNewFreshSocketConnection(`${reason}. No previous connection to cleanup.`)
      }
    },
    [logger, setupNewFreshSocketConnection, closeSocketConnection],
  )

  const onUnmount = useEffectEvent((videoEl: HTMLVideoElement | null) => {
    onUnmountProp?.()

    if (videoEl?.src) {
      // Always revoke the object URL when the component unmounts to prevent memory leaks
      window.URL.revokeObjectURL(videoEl.src)
    }

    closeSocketConnection('onUnmount')
  })
  useEffect(() => {
    // Capture the video element value for cleanup
    const videoEl = videoRef.current
    return () => {
      onUnmount(videoEl)
    }
  }, [])

  useLayoutEffect(() => {
    publicApiRef.current = {
      closeSocketConnection: closeSocketConnection,
      recreateSocketConnectionIfNeeded: recreateSocketConnectionIfNeeded,
      getVideoWebSocketState: () => {
        const socket = videoMetaRef.current.currentStream?.webSocket
        if (!socket) {
          return 'NOT_INITIALIZED'
        }

        return getWebSocketState(socket)
      },
    }
  }, [closeSocketConnection, recreateSocketConnectionIfNeeded])

  const videoPropsSx = VideoProps.sx ?? []

  return (
    <Box
      ref={videoContainerRef}
      sx={{ position: 'relative' }}
      className={className}
      onMouseEnter={() => {
        if (isFullscreen) {
          return
        }
        setIsVideoHovered(true)
      }}
      onMouseLeave={() => {
        if (isFullscreen) {
          return
        }
        setIsVideoHovered(false)
      }}
      onMouseMove={() => {
        if (isFullscreen) {
          setIsVideoHovered(true)

          // Make sure to clear the timeout if it exists
          if (containerMouseMoveInFullscreenTimeoutRef.current) {
            window.clearTimeout(containerMouseMoveInFullscreenTimeoutRef.current)
          }
          containerMouseMoveInFullscreenTimeoutRef.current = window.setTimeout(() => {
            setIsVideoHovered(false)
            containerMouseMoveInFullscreenTimeoutRef.current = null
          }, 3000)
        }
      }}
    >
      {children}
      <Video
        ref={videoRef}
        autoPlay={false}
        muted
        controls={false}
        // Re-add this seeking preventing in case we want to re-add the timelinebar
        // onSeeking={(event) => {
        //   const videoEl = event.currentTarget
        //   if (videoEl.buffered.length > 0) {
        //     const bufferEnd = videoEl.buffered.end(0)
        //     if (
        //       videoEl.currentTime < bufferEnd - 15 ||
        //       videoEl.currentTime > bufferEnd
        //     ) {
        //       // While we have a timeline bar, we need to prevent user from seeking to front or past of the video to prevent issues with buffering and stalling
        //       event.preventDefault()

        //       log(
        //         'debug',
        //         'on Seeking too far behind or ahead of buffer, keeping current time between buffer ranges',
        //         {
        //           currentTime: videoEl.currentTime,
        //           bufferEnd,
        //         },
        //       )
        //       // Keep the current time between buffer ranges, even for negative leads
        //       videoEl.currentTime = Math.max(bufferEnd - 0.5, 0)
        //     }
        //   }
        // }}
        onEnded={(event) => {
          logger.debug(['Video ended', event])
        }}
        onWaiting={() => {
          logger.warn(['Video waiting'])
          videoMetaRef.current.lastTimeWaitingUnixMs = Date.now()
        }}
        onError={(event) => {
          logger.error(['Video error', event])
        }}
        {...VideoProps}
        sx={[
          ...(R.isArray(videoPropsSx) ? videoPropsSx : [videoPropsSx]),

          // The parent component may want to set the height to something else but if we are fullscreen
          // we want to override it to 100%
          isFullscreen && {
            maxHeight: '100% !important',
          },
        ]}
      />

      <VideoWidgets>
        {match(showLoadingState)
          .with(true, () => (
            <CircularProgressDelayed
              sx={{
                display: 'flex',
                position: 'absolute',
                alignSelf: 'center',
                justifySelf: 'center',
              }}
            />
          ))
          .with(false, () => null)
          .exhaustive()}

        {isVideoHovered && (
          <Fade in>
            <BottomBar>
              {noSound ? null : (
                <VolumeControl
                  videoRef={videoRef}
                  disabled={showLoadingState}
                />
              )}
              <FullscreenButton
                videoContainerRef={videoContainerRef}
                isFullscreen={isFullscreen}
              />
            </BottomBar>
          </Fade>
        )}
      </VideoWidgets>
    </Box>
  )
}

type VideoProps = React.ComponentProps<typeof Video>
const Video = styled('video')({})

const getWebSocketState = (webSocket: WebSocket) =>
  match(webSocket.readyState)
    .with(webSocket.CONNECTING, () => 'CONNECTING' as const)
    .with(webSocket.OPEN, () => 'OPEN' as const)
    .with(webSocket.CLOSING, () => 'CLOSING' as const)
    .with(webSocket.CLOSED, () => 'CLOSED' as const)
    .otherwise(() => 'CLOSED' as const) // "otherwise" should never run but browsers might introduce a new state in the future

const getVideoReadyState = (readyState: number) =>
  match(readyState)
    .with(HTMLMediaElement.HAVE_ENOUGH_DATA, () => 'HAVE_ENOUGH_DATA' as const)
    .with(HTMLMediaElement.HAVE_METADATA, () => 'HAVE_METADATA' as const)
    .with(HTMLMediaElement.HAVE_CURRENT_DATA, () => 'HAVE_CURRENT_DATA' as const)
    .with(HTMLMediaElement.HAVE_FUTURE_DATA, () => 'HAVE_FUTURE_DATA' as const)
    .with(HTMLMediaElement.HAVE_NOTHING, () => 'HAVE_NOTHING' as const)
    .otherwise(() => 'HAVE_NOTHING' as const)

const VideoWidgets = styled('div')(({ theme }) =>
  theme.unstable_sx({
    display: 'grid',
    width: '100%',
    height: '100%',
    top: 0,
    position: 'absolute',
    pointerEvents: 'none',
  }),
)

const BottomBar = styled('div')(({ theme }) =>
  theme.unstable_sx({
    display: 'flex',
    alignSelf: 'flex-end',
    justifyContent: 'flex-end',
    background: `linear-gradient(to top,rgba(0, 0, 0, 0.9) 0%,rgba(0, 0, 0, 0.5) 60%,rgba(0, 0, 0, 0) 100%)`,
    width: '100%',
    px: 0.5,
    pb: 0.5,
    pointerEvents: 'all',
  }),
)

function VolumeControl({
  videoRef,
  disabled,
}: {
  videoRef: React.RefObject<HTMLVideoElement>
  disabled: boolean
}) {
  const subscribeVolumeChange = useCallback(
    (cb: () => void) => {
      videoRef.current?.addEventListener('volumechange', cb)
      return () => videoRef.current?.removeEventListener('volumechange', cb)
    },
    [videoRef],
  )

  const videoVolume = useSyncExternalStore(
    subscribeVolumeChange,
    useCallback((): number => videoRef.current?.volume ?? 0, [videoRef]),
  )
  const isMuted = useSyncExternalStore(
    subscribeVolumeChange,
    useCallback((): boolean => videoRef.current?.muted ?? true, [videoRef]),
  )

  return (
    <Stack
      spacing={0.5}
      direction="row"
      sx={{ alignItems: 'center', width: 90 }}
    >
      <Slider
        size="small"
        max={1}
        min={0}
        step={0.01}
        sx={{ color: disabled ? undefined : 'white' }}
        disabled={disabled}
        // This mimics the behavior of the browser player.
        value={isMuted ? 0 : videoVolume}
        onChange={(_, value) => {
          const videoEl = videoRef.current
          if (!videoEl) {
            return
          }
          // React compiler thinks we are mutating a normal variable but it's a ref. It's fine.
          // eslint-disable-next-line react-hooks/react-compiler, no-param-reassign
          videoEl.volume = value
          if (videoEl.volume > 0) {
            // When user has muted, but now they change the volume, unmute the video
            videoEl.muted = false
          }
        }}
      />
      <IconButton
        size="medium"
        onClick={() => {
          if (!videoRef.current) {
            return
          }
          // eslint-disable-next-line no-param-reassign
          videoRef.current.muted = !videoRef.current.muted
        }}
        disabled={disabled}
      >
        {match([videoVolume, isMuted])
          .with([0, P.any], [P.any, true], () => (
            <VolumeMuteIcon
              fontSize="inherit"
              sx={{ color: 'white' }}
            />
          ))
          .otherwise(() => (
            <VolumeUpIcon
              fontSize="inherit"
              sx={{ color: 'white' }}
            />
          ))}
      </IconButton>
    </Stack>
  )
}

function FullscreenButton({
  videoContainerRef,
  isFullscreen,
}: {
  videoContainerRef: React.RefObject<HTMLDivElement>
  isFullscreen: boolean
}) {
  return (
    <IconButton
      size="medium"
      sx={{ color: 'white' }}
      onClick={() => {
        if (!videoContainerRef.current || !screenfull) {
          return
        }
        screenfull.toggle(videoContainerRef.current)
      }}
    >
      {isFullscreen ? <FullscreenExitIcon /> : <FullscreenIcon />}
    </IconButton>
  )
}
