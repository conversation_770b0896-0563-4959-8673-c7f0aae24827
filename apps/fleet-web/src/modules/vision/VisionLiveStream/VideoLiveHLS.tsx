import React, {
  useCallback,
  useEffect,
  useMemo,
  useRef,
  useState,
  useSyncExternalStore,
} from 'react'
import {
  Box,
  CircularProgressDelayed,
  Fade,
  IconButton,
  Slider,
  Stack,
  styled,
  Typography,
} from '@karoo-ui/core'
import FullscreenIcon from '@mui/icons-material/Fullscreen'
import FullscreenExitIcon from '@mui/icons-material/FullscreenExit'
import VolumeMuteIcon from '@mui/icons-material/VolumeMute'
import VolumeUpIcon from '@mui/icons-material/VolumeUp'
import Hls, { type ErrorData } from 'hls.js/dist/hls.mjs'
import * as R from 'remeda'
import screenfull from 'screenfull'
import { match, P } from 'ts-pattern'

import { useEffectEvent, useEventHandler } from 'src/hooks/useEventHandler'
import { useFullscreen } from 'src/hooks/useFullscreen'
import { isNonEmptyString } from 'src/util-functions/string-utils'

import { useVisionDebugLogger } from './utils'

export type VideoLiveHLSProps = {
  /**
   * Make key mandatory so that people reset the component when the `url` changes.
   */
  key: string
  logId: string
  onMounted?: (videoEl: HTMLVideoElement) => void
  onUnmount?: () => void
  children?: React.ReactNode

  url: string
  VideoProps: VideoProps
  className?: string
  noSound?: boolean
  onFatalError: (error: ErrorData) => void
  onMediaEnded: () => void
}

export default function VideoLiveHLS({
  logId,
  url,
  onMounted: onMountedProp,
  onUnmount: onUnmountProp,
  children,
  className,
  VideoProps,
  noSound = false,
  onFatalError,
  onMediaEnded,
}: VideoLiveHLSProps) {
  const logPrefix = useMemo(
    // Show the last part of the url as the log prefix (the one that usually matters)
    () => `[Cartrack][VideoLiveHLS][${logId}] -`,
    [logId],
  )
  const logger = useVisionDebugLogger(logPrefix)

  const [DO_NOT_USE_DIRECTLY_hlsInstance] = useState(
    () =>
      // The docs were taken from https://github.com/video-dev/hls.js/blob/master/docs/API.md
      new Hls({
        /**
         * (default: undefined)
         * Alternative parameter to liveSyncDurationCount, expressed in seconds vs number of segments. If defined in the configuration object, liveSyncDuration will take precedence over the default liveSyncDurationCount.
         * You can't define this parameter and either liveSyncDurationCount or liveMaxLatencyDurationCount in your configuration object at the same time. A value too low (inferior to ~3 segment durations) is likely to cause playback stalls.
         */
        liveSyncDuration: 3,
        /**
         * (default: undefined)
         * Alternative parameter to liveMaxLatencyDurationCount, expressed in seconds vs number of segments. If defined in the configuration object, liveMaxLatencyDuration will take precedence over the default liveMaxLatencyDurationCount.
         * If set, this value must be strictly superior to liveSyncDuration which must be defined as well. You can't define this parameter and either liveSyncDurationCount or liveMaxLatencyDurationCount in your configuration object at the same time. A value too close from liveSyncDuration is likely to cause playback stalls.
         */
        liveMaxLatencyDuration: 15,

        /**
         * (default: 1 min: 1 max: 2)
         * When set to a value greater than 1, the latency-controller will adjust video.playbackRate up to maxLiveSyncPlaybackRate to catch up to target latency in a live stream. hls.targetLatency is based on liveSyncDuration|Count or manifest PART-|HOLD-BACK.
         * The default value is 1, which disables playback rate adjustment. Set maxLiveSyncPlaybackRate to a value greater than 1 to enable playback rate adjustment at the live edge.
         */
        maxLiveSyncPlaybackRate: 1.3,
        enableWorker: true,
        lowLatencyMode: false,

        /**
         * (default: Infinity)
         * The maximum duration of buffered media to keep once it has been played, in seconds. Any video buffered past this duration will be evicted.
         * Infinity means no restriction on back buffer length; 0 keeps the minimum amount. The minimum amount is equal to the target duration of a segment to ensure that current playback is not interrupted.
         * Keep in mind, the browser can and does evict media from the buffer on its own, so with the Infinity setting, HLS.js will let the browser do what it needs to do. (Ref: the MSE spec under coded frame eviction).
         */
        backBufferLength: 20,

        /**
         * (default: 30 seconds)
         * Maximum buffer length in seconds. If buffer length is/become less than this value, a new fragment will be loaded. This is the guaranteed buffer length HLS.js will try to reach, regardless of maxBufferSize.
         */
        maxBufferLength: 20,

        /**
         * (default 600s)
         * Maximum buffer length in seconds. HLS.js will never exceed this value, even if maxBufferSize is not reached yet.
         * HLS.js tries to buffer up to a maximum number of bytes (60 MB by default) rather than to buffer up to a maximum nb of seconds. this is to mimic the browser behaviour (the buffer eviction algorithm is starting after the browser detects that video buffer size reaches a limit in bytes)
         */
        maxMaxBufferLength: 60,

        // Allow more time for on-the-fly transcoding to deliver segments
        fragLoadingTimeOut: 30000,
        levelLoadingTimeOut: 30000,
        manifestLoadingTimeOut: 30000,

        /**
         * (default: 0.1 seconds)
         * Maximum inter-fragment buffer hole tolerance that HLS.js can cope with when searching for the next fragment to load. When switching between quality level, fragments might not be perfectly aligned.
         * This could result in small overlapping or hole in media buffer. This tolerance factor helps cope with this.
         */
        // maxBufferHole: 0.6,
        debug: false,
      }),
  )
  const hlsRef = useRef<Hls>(DO_NOT_USE_DIRECTLY_hlsInstance)

  // When something like a fatal error happens, we can not proceed with the video playback.
  const [hlsLastFatalError] = useState<'mime_type_not_supported' | 'unknown' | null>(
    null,
  )

  const [debugInfo] = useState<ReadonlySet<string>>(new Set())

  const mountedRef = useRef(false)

  const videoRef = useRef<HTMLVideoElement | null>(null)
  const videoContainerRef = useRef<HTMLDivElement | null>(null)
  const containerMouseMoveInFullscreenTimeoutRef = useRef<number | null>(null)
  const [isVideoHovered, setIsVideoHovered] = useState(false)
  const isFullscreen = useFullscreen()

  const showLoadingState = useSyncExternalStore(
    useCallback(
      (cb) => {
        videoRef.current?.addEventListener('readystatechange', cb)
        videoRef.current?.addEventListener('waiting', cb)
        videoRef.current?.addEventListener('canplay', cb)
        videoRef.current?.addEventListener('playing', cb)
        videoRef.current?.addEventListener('pause', cb)
        videoRef.current?.addEventListener('ended', cb)
        return () => {
          videoRef.current?.removeEventListener('readystatechange', cb)
          videoRef.current?.removeEventListener('waiting', cb)
          videoRef.current?.removeEventListener('canplay', cb)
          videoRef.current?.removeEventListener('playing', cb)
          videoRef.current?.removeEventListener('pause', cb)
          videoRef.current?.removeEventListener('ended', cb)
        }
      },
      [videoRef],
    ),
    useCallback(() => {
      if (!videoRef.current) {
        return true
      }
      if (videoRef.current.paused) {
        return false
      }
      if (videoRef.current.played.length === 0) {
        return true
      }

      const readyState = getVideoReadyState(videoRef.current.readyState)

      return readyState === 'HAVE_NOTHING' || readyState === 'HAVE_METADATA'
    }, [videoRef]),
  )

  useEffect(() => {
    if (isFullscreen) {
      return // do nothing
    }

    // If we are leaving fullscreen, we want to clear the timeout that was only meant to work while in fullscreen
    if (containerMouseMoveInFullscreenTimeoutRef.current) {
      window.clearTimeout(containerMouseMoveInFullscreenTimeoutRef.current)
    }
  }, [isFullscreen])

  useEffect(() => {
    logger.debug(['url', url])
  }, [url, logger])

  const onMount = useEffectEvent((videoEl: HTMLVideoElement) => {
    onMountedProp?.(videoEl)
    setupHlsMeta('onMount')
  })
  useEffect(() => {
    // Using mountedRef "hack" to prevent issues with upcoming react concurrent rendering
    // onMounted should really on be called once so it's better to make sure it is.
    if (mountedRef.current) {
      return
    }
    mountedRef.current = true

    if (videoRef.current) {
      onMount(videoRef.current)
    }
  }, [])

  const setupHlsMeta = useEventHandler((reason: string) => {
    const videoEl = videoRef.current
    if (!videoEl) {
      return
    }

    logger.debug(['setupHlsMeta', { reason }])

    const hls = hlsRef.current

    const currentSrc = videoEl.src

    hls.loadSource(url)
    hls.attachMedia(videoEl)
    hls.on(Hls.Events.MANIFEST_PARSED, () => {
      videoEl.play().catch(() => {})
    })

    // hls.on(Hls.Events.LEVEL_LOADED, (_, data) => {
    //   log('debug', 'LEVEL_LOADED', data)
    //   // Try levels in manifest
    //   if (hls.levels && hls.levels.length > 0) {
    //     let level = hls.levels.find((lvl) => lvl.videoCodec || lvl.audioCodec)
    //     if (!level) level = hls.levels[0]
    //     const cachedVideoCodec = level?.videoCodec || 'unknown'
    //     const cachedAudioCodec = level?.audioCodec || 'unknown'

    //     log('debug', 'Video codec:', cachedVideoCodec)
    //     log('debug', 'Audio codec:', cachedAudioCodec)
    //   }
    // })
    hls.on(Hls.Events.FRAG_BUFFERED, (_, data) => {
      logger.debug(['FRAG_BUFFERED', data])
    })
    hls.on(Hls.Events.FRAG_LOADED, (_, data) => {
      logger.debug(['FRAG_LOADED', data])
    })
    hls.on(Hls.Events.MEDIA_ENDED, (_, data) => {
      logger.warn(['MEDIA_ENDED', data])
      onMediaEnded()
    })
    hls.on(Hls.Events.ERROR, (_, data) => {
      logger.error(['ERROR', data])
      if (data.fatal) {
        onFatalError(data)
      }
    })

    // Clean up old object URL if it exists
    if (isNonEmptyString(currentSrc)) {
      logger.debug(['Revoking old object URL', { oldUrl: currentSrc }])
      // Prevent memory leak by revoking the old object URL, if any
      window.URL.revokeObjectURL(currentSrc)
    }
  })

  const onUnmount = useEffectEvent((videoEl: HTMLVideoElement | null) => {
    onUnmountProp?.()

    // Store the src before destroying HLS since it might clear it
    const videoSrc = videoEl?.src

    hlsRef.current.destroy()

    // Even though hls should revoke the object URL, we do it here as a safety measure, like we did for VideoWs
    if (videoSrc) {
      // Always revoke the object URL when the component unmounts to prevent memory leaks
      logger.debug(['Revoking object URL on unmount', { url: videoSrc }])
      window.URL.revokeObjectURL(videoSrc)
    }
  })
  useEffect(() => {
    // Capture the video element value for cleanup
    const videoEl = videoRef.current
    return () => {
      onUnmount(videoEl)
    }
  }, [])

  const videoPropsSx = VideoProps.sx ?? []

  return (
    <Box
      ref={videoContainerRef}
      sx={{ position: 'relative' }}
      className={className}
      onMouseEnter={() => {
        if (isFullscreen) {
          return
        }
        setIsVideoHovered(true)
      }}
      onMouseLeave={() => {
        if (isFullscreen) {
          return
        }
        setIsVideoHovered(false)
      }}
      onMouseMove={() => {
        if (isFullscreen) {
          setIsVideoHovered(true)

          // Make sure to clear the timeout if it exists
          if (containerMouseMoveInFullscreenTimeoutRef.current) {
            window.clearTimeout(containerMouseMoveInFullscreenTimeoutRef.current)
          }
          containerMouseMoveInFullscreenTimeoutRef.current = window.setTimeout(() => {
            setIsVideoHovered(false)
            containerMouseMoveInFullscreenTimeoutRef.current = null
          }, 3000)
        }
      }}
    >
      {children}
      <Video
        ref={videoRef}
        autoPlay={false}
        muted
        controls={false}
        onEnded={(event) => {
          logger.debug(['Video ended', event])
        }}
        onWaiting={() => {
          logger.warn(['Video waiting'])
        }}
        onError={(event) => {
          logger.error(['Video error', event])
        }}
        {...VideoProps}
        sx={[
          ...(R.isArray(videoPropsSx) ? videoPropsSx : [videoPropsSx]),

          // The parent component may want to set the height to something else but if we are fullscreen
          // we want to override it to 100%
          isFullscreen && {
            maxHeight: '100% !important',
          },
        ]}
      />

      <VideoWidgets>
        {debugInfo.size > 0 && (
          <Box
            sx={{
              alignSelf: 'flex-start',
              justifySelf: 'flex-start',
              background: 'rgba(0, 0, 0, 0.9)',
              color: 'white',
              fontSize: '12px',
              fontFamily: 'monospace',
              opacity: 0.4,
              p: 1,
              mt: 4,
              borderRadius: 1,
              maxWidth: 520,
              overflow: 'auto',
              wordBreak: 'break-word',
            }}
          >
            <Typography>Debug Info</Typography>
            <Box
              component="pre"
              sx={{
                whiteSpace: 'pre-wrap',
                wordBreak: 'break-all',
                overflowX: 'auto',
                margin: 0,
                padding: 0,
              }}
            >
              {Array.from(debugInfo).join('\n')}
            </Box>
          </Box>
        )}
        {match(hlsLastFatalError)
          .with(null, () =>
            // Only consider displaying the loading state if we don't have a fatal error
            match(showLoadingState)
              .with(true, () => (
                <CircularProgressDelayed
                  sx={{
                    display: 'flex',
                    position: 'absolute',
                    alignSelf: 'center',
                    justifySelf: 'center',
                  }}
                />
              ))
              .with(false, () => null)
              .exhaustive(),
          )
          .with('mime_type_not_supported', () => (
            <Box
              sx={{
                display: 'flex',
                position: 'absolute',
                alignSelf: 'center',
                justifySelf: 'center',
              }}
            >
              <Typography>
                The video codec is not supported by the browser OR you have hardware
                acceleration disabled in your browser settings.
              </Typography>
            </Box>
          ))
          .with('unknown', () => (
            <Box
              sx={{
                display: 'flex',
                position: 'absolute',
                alignSelf: 'center',
                justifySelf: 'center',
              }}
            >
              <Typography>An unknown fatal error occurred.</Typography>
            </Box>
          ))
          .exhaustive()}

        {isVideoHovered && (
          <Fade in>
            <BottomBar>
              {noSound ? null : (
                <VolumeControl
                  videoRef={videoRef}
                  disabled={showLoadingState}
                />
              )}
              <FullscreenButton
                videoContainerRef={videoContainerRef}
                isFullscreen={isFullscreen}
              />
            </BottomBar>
          </Fade>
        )}
      </VideoWidgets>
    </Box>
  )
}

type VideoProps = React.ComponentProps<typeof Video>
const Video = styled('video')({})

const getVideoReadyState = (readyState: number) =>
  match(readyState)
    .with(HTMLMediaElement.HAVE_ENOUGH_DATA, () => 'HAVE_ENOUGH_DATA' as const)
    .with(HTMLMediaElement.HAVE_METADATA, () => 'HAVE_METADATA' as const)
    .with(HTMLMediaElement.HAVE_CURRENT_DATA, () => 'HAVE_CURRENT_DATA' as const)
    .with(HTMLMediaElement.HAVE_FUTURE_DATA, () => 'HAVE_FUTURE_DATA' as const)
    .with(HTMLMediaElement.HAVE_NOTHING, () => 'HAVE_NOTHING' as const)
    .otherwise(() => 'HAVE_NOTHING' as const)

const VideoWidgets = styled('div')(({ theme }) =>
  theme.unstable_sx({
    display: 'grid',
    width: '100%',
    height: '100%',
    top: 0,
    position: 'absolute',
    pointerEvents: 'none',
  }),
)

const BottomBar = styled('div')(({ theme }) =>
  theme.unstable_sx({
    display: 'flex',
    alignSelf: 'flex-end',
    justifyContent: 'flex-end',
    background: `linear-gradient(to top,rgba(0, 0, 0, 0.9) 0%,rgba(0, 0, 0, 0.5) 60%,rgba(0, 0, 0, 0) 100%)`,
    width: '100%',
    px: 0.5,
    pb: 0.5,
    pointerEvents: 'all',
  }),
)

function VolumeControl({
  videoRef,
  disabled,
}: {
  videoRef: React.RefObject<HTMLVideoElement>
  disabled: boolean
}) {
  const subscribeVolumeChange = useCallback(
    (cb: () => void) => {
      videoRef.current?.addEventListener('volumechange', cb)
      return () => videoRef.current?.removeEventListener('volumechange', cb)
    },
    [videoRef],
  )

  const videoVolume = useSyncExternalStore(
    subscribeVolumeChange,
    useCallback((): number => videoRef.current?.volume ?? 0, [videoRef]),
  )
  const isMuted = useSyncExternalStore(
    subscribeVolumeChange,
    useCallback((): boolean => videoRef.current?.muted ?? true, [videoRef]),
  )

  return (
    <Stack
      spacing={0.5}
      direction="row"
      sx={{ alignItems: 'center', width: 90 }}
    >
      <Slider
        size="small"
        max={1}
        min={0}
        step={0.01}
        sx={{ color: disabled ? undefined : 'white' }}
        disabled={disabled}
        // This mimics the behavior of the browser player.
        value={isMuted ? 0 : videoVolume}
        onChange={(_, value) => {
          const videoEl = videoRef.current
          if (!videoEl) {
            return
          }
          // React compiler thinks we are mutating a normal variable but it's a ref. It's fine.
          // eslint-disable-next-line react-hooks/react-compiler, no-param-reassign
          videoEl.volume = value
          if (videoEl.volume > 0) {
            // When user has muted, but now they change the volume, unmute the video
            videoEl.muted = false
          }
        }}
      />
      <IconButton
        size="medium"
        onClick={() => {
          if (!videoRef.current) {
            return
          }
          // eslint-disable-next-line no-param-reassign
          videoRef.current.muted = !videoRef.current.muted
        }}
        disabled={disabled}
      >
        {match([videoVolume, isMuted])
          .with([0, P.any], [P.any, true], () => (
            <VolumeMuteIcon
              fontSize="inherit"
              sx={{ color: 'white' }}
            />
          ))
          .otherwise(() => (
            <VolumeUpIcon
              fontSize="inherit"
              sx={{ color: 'white' }}
            />
          ))}
      </IconButton>
    </Stack>
  )
}

function FullscreenButton({
  videoContainerRef,
  isFullscreen,
}: {
  videoContainerRef: React.RefObject<HTMLDivElement>
  isFullscreen: boolean
}) {
  return (
    <IconButton
      size="medium"
      sx={{ color: 'white' }}
      onClick={() => {
        if (!videoContainerRef.current || !screenfull) {
          return
        }
        screenfull.toggle(videoContainerRef.current)
      }}
    >
      {isFullscreen ? <FullscreenExitIcon /> : <FullscreenIcon />}
    </IconButton>
  )
}
