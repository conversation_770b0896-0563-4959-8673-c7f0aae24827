/**
 * Manifest Processor - H.265 → H.264 Codec String Replacement
 *
 * CRITICAL: This module ensures all manifest files have consistent H.264 codec declarations.
 * Any failure in codec replacement will abort the entire stream.
 *
 * Responsibilities:
 * 1. Replace all H.265 codec strings with H.264 equivalents
 * 2. Maintain bandwidth and resolution information
 * 3. Preserve all other manifest metadata
 * 4. Validate codec replacement was successful
 */

import { createVisionLiveStreamDebugLogger } from '../utils'

type CodecMapping = {
  h265Pattern: RegExp
  h264Replacement: string
  description: string
}

const logger = createVisionLiveStreamDebugLogger('[ManifestProcessor]', 'all')

class ManifestProcessor {
  // Comprehensive H.265 → H.264 codec mappings
  private codecMappings: Array<CodecMapping> = [
    // HEVC Main Profile Level 3.1 → AVC High Profile Level 3.1
    {
      h265Pattern: /CODECS="hev1\.1\.6\.L93\.B0"/g,
      h264Replacement: 'CODECS="avc1.4d401f"',
      description: 'HEVC Main L3.1 → AVC High L3.1',
    },
    {
      h265Pattern: /CODECS="hvc1\.1\.6\.L93\.B0"/g,
      h264Replacement: 'CODECS="avc1.4d401f"',
      description: 'HEVC Main L3.1 → AVC High L3.1',
    },

    // HEVC Main Profile Level 4.0 → AVC High Profile Level 4.0
    {
      h265Pattern: /CODECS="hev1\.1\.6\.L120\.B0"/g,
      h264Replacement: 'CODECS="avc1.4d4028"',
      description: 'HEVC Main L4.0 → AVC High L4.0',
    },
    {
      h265Pattern: /CODECS="hvc1\.1\.6\.L120\.B0"/g,
      h264Replacement: 'CODECS="avc1.4d4028"',
      description: 'HEVC Main L4.0 → AVC High L4.0',
    },

    // HEVC Main Profile Level 5.0 → AVC High Profile Level 5.0
    {
      h265Pattern: /CODECS="hev1\.1\.6\.L150\.B0"/g,
      h264Replacement: 'CODECS="avc1.4d4032"',
      description: 'HEVC Main L5.0 → AVC High L5.0',
    },
    {
      h265Pattern: /CODECS="hvc1\.1\.6\.L150\.B0"/g,
      h264Replacement: 'CODECS="avc1.4d4032"',
      description: 'HEVC Main L5.0 → AVC High L5.0',
    },

    // Generic HEVC patterns (fallback)
    {
      h265Pattern: /CODECS="hev1\.[^"]+"/g,
      h264Replacement: 'CODECS="avc1.4d401f"',
      description: 'Generic HEVC → AVC High L3.1',
    },
    {
      h265Pattern: /CODECS="hvc1\.[^"]+"/g,
      h264Replacement: 'CODECS="avc1.4d401f"',
      description: 'Generic HEVC → AVC High L3.1',
    },

    // Codec strings without quotes
    {
      h265Pattern: /hev1\.[^,\s]+/g,
      h264Replacement: 'avc1.4d401f',
      description: 'Unquoted HEVC → AVC',
    },
    {
      h265Pattern: /hvc1\.[^,\s]+/g,
      h264Replacement: 'avc1.4d401f',
      description: 'Unquoted HEVC → AVC',
    },

    // Other HEVC references
    {
      h265Pattern: /HEVC/g,
      h264Replacement: 'AVC',
      description: 'HEVC keyword → AVC',
    },
    {
      h265Pattern: /hevc/g,
      h264Replacement: 'avc',
      description: 'hevc keyword → avc',
    },
  ]

  /**
   * Process manifest and replace all H.265 codec references with H.264
   */
  processManifest(originalManifest: string): string {
    logger.debug([`📄 Original manifest length: ${originalManifest?.length} bytes`])

    return originalManifest
    // if (!originalManifest || originalManifest.length === 0) {
    //   throw new Error('Empty manifest received')
    // }

    // // Log first 500 chars of original manifest
    // logger.debug([`📄 Original manifest preview:`, originalManifest.substring(0, 500)])

    // // Detect if this is actually an H.265 manifest
    // const hasH265Codecs = this.detectH265Codecs(originalManifest)
    // logger.debug([`🔍 H.265 codecs detected: ${hasH265Codecs}`])

    // if (!hasH265Codecs) {
    //   logger.debug([
    //     '⚠️ No H.265 codecs in manifest, but assuming H.265 segments per requirements',
    //   ])
    //   logger.debug(['🔄 Adding H.264 codec declarations to manifest'])

    //   // Since we assume all content is H.265, we should add H.264 codec info to manifest
    //   // This ensures HLS.js knows what to expect after transcoding
    //   return this.addH264CodecInfo(originalManifest)
    // }

    // logger.debug(['🔄 H.265 codecs detected - performing codec replacement'])

    // // Perform codec replacement
    // let processedManifest = originalManifest
    // let totalReplacements = 0

    // logger.debug([`🔍 Testing ${this.codecMappings.length} codec mappings`])

    // for (const mapping of this.codecMappings) {
    //   const matches = processedManifest.match(mapping.h265Pattern)
    //   if (matches) {
    //     logger.debug([
    //       `🔄 Replacing ${matches.length} instances of ${mapping.description}`,
    //     ])
    //     logger.debug([`🔍 Pattern: ${mapping.h265Pattern}`])
    //     logger.debug([`🔍 Replacement: ${mapping.h264Replacement}`])
    //     logger.debug([`🔍 Matches found:`, matches])

    //     processedManifest = processedManifest.replace(
    //       mapping.h265Pattern,
    //       mapping.h264Replacement,
    //     )
    //     totalReplacements += matches.length

    //     logger.debug([`✅ Replacement completed`])
    //   } else {
    //     logger.debug([`⏭️ No matches for pattern: ${mapping.h265Pattern}`])
    //   }
    // }

    // logger.debug([`📊 Total replacements made: ${totalReplacements}`])

    // // Validate that codec replacement was successful
    // if (totalReplacements === 0) {
    //   throw new Error(
    //     'H.265 codecs detected but no replacements made - codec mapping failed',
    //   )
    // }

    // // Verify no H.265 codecs remain
    // const remainingH265 = this.detectH265Codecs(processedManifest)
    // if (remainingH265) {
    //   throw new Error(
    //     'H.265 codecs still present after replacement - incomplete transcoding',
    //   )
    // }

    // // Verify that H.264 codecs are now present
    // const hasH264Codecs = this.detectH264Codecs(processedManifest)
    // if (!hasH264Codecs) {
    //   throw new Error('No H.264 codecs found after replacement - transcoding failed')
    // }

    // logger.debug([
    //   `✅ Codec replacement successful - ${totalReplacements} replacements made`,
    // ])
    // logger.debug([`📊 Original manifest: ${originalManifest.length} bytes`])
    // logger.debug([`📊 Processed manifest: ${processedManifest.length} bytes`])
    // logger.debug([
    //   `📄 Processed manifest preview:`,
    //   processedManifest.substring(0, 500),
    // ])

    // return processedManifest
  }

  /**
   * Detect if manifest contains H.265 codec declarations
   */
  // private detectH265Codecs(manifest: string): boolean {
  //   const h265Patterns = [/hev1\./i, /hvc1\./i, /HEVC/i, /hevc/i]

  //   return h265Patterns.some((pattern) => pattern.test(manifest))
  // }

  /**
   * Detect if manifest contains H.264 codec declarations
   */
  // private detectH264Codecs(manifest: string): boolean {
  //   const h264Patterns = [/avc1\./i, /AVC/i, /avc/i]

  //   return h264Patterns.some((pattern) => pattern.test(manifest))
  // }

  /**
   * Add H.264 codec info to manifest that doesn't have codec declarations
   * This is needed when segments are H.265 but manifest doesn't declare it
   */
  // private addH264CodecInfo(manifest: string): string {
  //   logger.debug(['🔧 Adding H.264 codec info to manifest'])

  //   // For fMP4 streams, we can add codec info to the EXT-X-MAP line
  //   if (manifest.includes('#EXT-X-MAP:')) {
  //     logger.debug(['🔍 Found fMP4 stream, adding codec to EXT-X-MAP'])

  //     // Add codec info to the EXT-X-MAP line
  //     const modifiedManifest = manifest.replace(
  //       /#EXT-X-MAP:URI="([^"]+)"/g,
  //       '#EXT-X-MAP:URI="$1",CODECS="avc1.4d401f,mp4a.40.2"',
  //     )

  //     logger.debug([
  //       '📄 Modified manifest preview:',
  //       modifiedManifest.substring(0, 500),
  //     ])
  //     return modifiedManifest
  //   }

  //   // For other streams, add a comment indicating H.264 expectation
  //   const modifiedManifest = manifest.replace(
  //     '#EXTM3U',
  //     '#EXTM3U\n# Transcoded to H.264: avc1.4d401f,mp4a.40.2',
  //   )

  //   logger.debug(['📄 Added H.264 codec comment to manifest'])
  //   return modifiedManifest
  // }

  /**
   * Test codec replacement without processing full manifest
   */
  testCodecReplacement(text: string): {
    original: string
    processed: string
    replacements: number
  } {
    let processed = text
    let totalReplacements = 0

    for (const mapping of this.codecMappings) {
      const matches = processed.match(mapping.h265Pattern)
      if (matches) {
        processed = processed.replace(mapping.h265Pattern, mapping.h264Replacement)
        totalReplacements += matches.length
      }
    }

    return {
      original: text,
      processed,
      replacements: totalReplacements,
    }
  }
}

// Create singleton instance
export const ManifestProcessorSingleton = new ManifestProcessor()
