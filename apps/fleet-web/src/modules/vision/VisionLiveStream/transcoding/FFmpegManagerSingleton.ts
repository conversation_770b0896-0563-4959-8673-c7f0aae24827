import { FFmpeg } from '@ffmpeg/ffmpeg'
import { toBlobURL } from '@ffmpeg/util'

import { createDebugLogger } from 'src/util-functions/logging'

const logger = createDebugLogger('[FFmpegManager]')

class FFmpegManager {
  public ffmpeg: FFmpeg = new FFmpeg()

  async initialize(): Promise<void> {
    if (this.ffmpeg.loaded) {
      // Already initialized
      return
    }

    try {
      logger.debug(['🚀 Initializing FFmpeg.wasm...'])

      // Verbose ffmpeg logs for debugging
      try {
        this.ffmpeg.on('log', (_e) => {
          logger.debug([`[FFmpeg][${_e?.type ?? 'log'}]`, _e?.message ?? _e])
        })
        // this.ffmpeg.on('progress', (_e) => {
        //   logger.debug(['[FFmpeg][progress]', _e])
        // })
      } catch (_bindErr) {
        logger.warn(['⚠️ Failed to bind FFmpeg event listeners', _bindErr])
      }

      logger.debug(['📦 Loading FFmpeg.wasm core from CDN...'])
      // toBlobURL is used to bypass CORS issue, urls with the same
      // domain can be used directly.
      await this.ffmpeg.load({
        coreURL: await toBlobURL(
          'https://fleetweb.ams3.cdn.digitaloceanspaces.com/libraries/ffmpeg@0.12.6/ffmpeg-core.js',
          'text/javascript',
        ),
        wasmURL: await toBlobURL(
          'https://fleetweb.ams3.cdn.digitaloceanspaces.com/libraries/ffmpeg@0.12.6/ffmpeg-core.wasm',
          'application/wasm',
        ),
      })
      logger.debug(['✅ FFmpeg.wasm initialized successfully'])

      // FS sanity check
      try {
        await this.ffmpeg.writeFile('fs_check.txt', new Uint8Array([1, 2, 3]))
        const check = (await this.ffmpeg.readFile('fs_check.txt')) as Uint8Array
        if (check.length !== 3) throw new Error('Unexpected FS check length')
        await this.ffmpeg.deleteFile('fs_check.txt')
        logger.debug(['🗂️ FFmpeg FS check passed'])
      } catch (_fsErr) {
        logger.error(['💥 FFmpeg FS check failed:', _fsErr])
        throw _fsErr
      }
    } catch (_error: any) {
      logger.error(['💥 FFmpeg.wasm initialization failed:', _error])
      logger.error(['💥 Error details:', _error])
      throw new Error(`Failed to initialize FFmpeg.wasm: ${_error.message}`)
    }
  }

  cleanup(): void {
    if (!this.ffmpeg.loaded) {
      logger.debug(['FFmpeg.wasm not loaded, skipping cleanup'])
      return
    }

    try {
      this.ffmpeg.terminate()
      logger.debug(['Cleanup completed'])
    } catch (error) {
      logger.error(['Cleanup error:', error])
    }
  }
}

export const FFmpegManagerSingleton = new FFmpegManager()
