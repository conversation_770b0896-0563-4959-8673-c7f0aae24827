/**
 * Media Segment Processor - H.265 → H.264 Video Transcoding
 *
 * CRITICAL: This module handles the actual video transcoding of H.265 segments to H.264.
 * Uses FFmpeg.wasm for software transcoding with optimized settings for streaming.
 * Any transcoding failure will abort the entire stream.
 *
 * Responsibilities:
 * 1. Initialize FFmpeg.wasm with software-only codecs
 * 2. Transcode H.265 video segments to H.264
 * 3. Preserve timing information (PTS/DTS)
 * 4. Optimize for streaming performance
 */

import type { FFmpeg, FileData, OK } from '@ffmpeg/ffmpeg'

import { createVisionLiveStreamDebugLogger } from '../utils'
import { FFmpegManagerSingleton } from './FFmpegManagerSingleton'

type TranscodingOptions = {
  outputQuality: 'ultrafast' | 'superfast' | 'veryfast' | 'faster' | 'fast'
  maxResolution: '480p' | '720p' | '1080p'
  bitrate?: number
  preserveTiming: boolean
  // Optional encoder tuning
  encoderThreads?: number
  x264Params?: string
  // Timeline control
  targetFps?: number // default 25
}

type TranscodingStats = {
  inputSize: number
  outputSize: number
  duration: number
  fps?: number
}

const logger = createVisionLiveStreamDebugLogger('[MediaSegmentProcessor]')

const createSafeWriteReadFile = <Data extends FileData>(ffmpeg: FFmpeg) => ({
  writeFile: async (path: string, data: Data): Promise<'OK' | 'FAILED'> => {
    const writeFileSuccess: OK = await ffmpeg.writeFile(path, data)
    return writeFileSuccess ? 'OK' : 'FAILED'
  },
  readFile: async (
    path: string,
  ): Promise<{ success: true; data: Data } | { success: false; error: Error }> => {
    try {
      const data = await ffmpeg.readFile(path)
      return { success: true, data: data as Data }
    } catch (error) {
      return { success: false, error: error as Error }
    }
  },
})

class MediaSegmentProcessor {
  private defaultOptions: TranscodingOptions = {
    outputQuality: 'ultrafast', // Fastest encoding for real-time
    maxResolution: '480p', // Lower resolution for stability/perf
    bitrate: 1200, // ~1.2 Mbps for 480p
    preserveTiming: true, // Critical for HLS playback
  }

  private safeWriteReadFile = createSafeWriteReadFile<Uint8Array<ArrayBuffer>>(
    FFmpegManagerSingleton.ffmpeg,
  )

  /**
   * Transcode H.265 segment to H.264
   */
  async transcodeSegment(
    h265Data: ArrayBuffer,
    url: string,
    options: Partial<TranscodingOptions> = {},
  ): Promise<ArrayBuffer> {
    // Ensure FFmpeg is initialized
    await FFmpegManagerSingleton.initialize()

    const finalOptions = { ...this.defaultOptions, ...options }
    logger.debug(['Transcoding segment from', url, 'with options:', finalOptions])

    const startTime = Date.now()
    const job = async (): Promise<
      ArrayBuffer | 'FAILED_ON_WRITE_FILE' | 'FAILED_ON_EXEC' | 'FAILED_ON_READ_FILE'
    > => {
      const uid = Math.random().toString(36).slice(2)
      const inputFile = `input_${uid}.mp4`
      const outputExt = 'mp4'
      const outputFile = `output_${uid}.${outputExt}`

      // Write input
      const writeFileSuccess = await this.safeWriteReadFile.writeFile(
        inputFile,
        new Uint8Array(h265Data),
      )
      if (!writeFileSuccess) {
        logger.error(['💥 [MEDIA-TRANSCODER] Transcoding failed on writeFile'])
        return 'FAILED_ON_WRITE_FILE'
      }

      // Build args
      const ffmpegArgs = this.buildFFmpegArgs(inputFile, outputFile, finalOptions)
      logger.debug(['Running FFmpeg with args:', ffmpegArgs.join(' ')])

      try {
        // Exec using a single long-lived FFmpeg instance
        const execSuccess: number = await FFmpegManagerSingleton.ffmpeg.exec(ffmpegArgs)
        if (execSuccess !== 0) {
          logger.error(['💥 [MEDIA-TRANSCODER] Transcoding failed on exec'])
          return 'FAILED_ON_EXEC'
        }

        // Read output
        const h264DataResult = await this.safeWriteReadFile.readFile(outputFile)
        if (!h264DataResult.success) {
          logger.error([
            '💥 [MEDIA-TRANSCODER] Transcoding failed on readFile',
            h264DataResult.error,
          ])
          return 'FAILED_ON_READ_FILE'
        }
        const h264Data = h264DataResult.data

        const duration = Date.now() - startTime
        const stats: TranscodingStats = {
          inputSize: h265Data.byteLength,
          outputSize: h264Data.length,
          duration,
        }
        logger.debug([`Transcoding completed in ${duration.toFixed(2)}ms`, stats])

        if (h264Data.length === 0) {
          throw new Error('Transcoding produced empty output')
        }

        return h264Data.buffer
      } finally {
        // Always attempt to cleanup temp files to prevent FS growth

        // Do not block clean up
        Promise.all([
          FFmpegManagerSingleton.ffmpeg.deleteFile(inputFile),
          FFmpegManagerSingleton.ffmpeg.deleteFile(outputFile),
        ])
          .then(() => {
            logger.debug(['🚀 cleanup done'])
          })
          .catch((error) => {
            logger.error(['Non-fatal: cleanup failed', error])
          })
      }
    }

    try {
      // Run without serialization for experimentation
      // If needed, re-enable by chaining to this.transcodeQueue as before
      const result = await job()
      if (typeof result === 'string') {
        throw new TypeError(`Transcoding failed: ${result}`)
      }
      return result
    } catch (error) {
      const duration = Date.now() - startTime
      logger.error([
        `💥 [MEDIA-TRANSCODER] Transcoding failed after ${duration.toFixed(2)}ms:`,
        error,
      ])
      throw new Error(`Segment transcoding failed: ${error.message}`)
    }
  }

  /**
   * Build FFmpeg command arguments for H.265 → H.264 transcoding
   */
  private buildFFmpegArgs(
    inputFile: string,
    outputFile: string,
    options: TranscodingOptions,
  ): Array<string> {
    const targetFps = options.targetFps
    const gop = Math.max(1, Math.round((targetFps ?? 30) * 1)) // ~1-second GOP by default

    const args = [
      // Input
      '-i',
      inputFile,

      // Video codec and encoding settings
      '-c:v',
      'libx264', // H.264 encoder
      '-enc_time_base',
      '-1',
      '-preset',
      options.outputQuality, // Encoding speed preset
      '-tune',
      'zerolatency,fastdecode', // Optimize for streaming and lower decode complexity

      // Encoder threading and params (optional)
      ...(options.encoderThreads ? ['-threads', String(options.encoderThreads)] : []),
      ...(options.x264Params ? ['-x264-params', options.x264Params] : []),

      // Preserve input PTS and enforce CFR to avoid buffer holes
      '-copyts',
      '-vsync',
      'vfr',
      ...(targetFps ? ['-r', String(targetFps)] : []),

      // GOP/keyframe alignment to segment cadence (assumes ~1s segments)
      '-g',
      String(gop),
      '-keyint_min',
      String(gop),
      '-sc_threshold',
      '0',
      // Ensure periodic IDR (~1s) so each 1s fragment is independently decodable
      '-force_key_frames',
      'expr:gte(t,n_forced*1)',

      // Quality settings
      '-crf',
      this.getCRFValue(options.outputQuality), // Quality factor

      // Resolution scaling
      '-vf',
      this.getScaleFilter(options.maxResolution),
      // Thread the filter graph so scaler can use multiple workers
      '-filter_threads',
      '2',

      // Bitrate control (optional)
      ...(options.bitrate
        ? ['-maxrate', `${options.bitrate}k`, '-bufsize', `${options.bitrate * 2}k`]
        : []),

      // Audio: copy source AAC to avoid per-segment encoder priming (audible resets)
      '-c:a',
      'copy',

      // MP4 (fMP4) container for HLS segments
      '-f',
      'mp4',
      '-movflags',
      '+frag_keyframe+empty_moov+default_base_moof+faststart',
      // Stable timescale and low mux latency
      '-video_track_timescale',
      '90000',
      // Preserve input timebase in output timestamps
      '-copytb',
      '1',
      '-muxpreload',
      '0',
      '-muxdelay',
      '0',
      // Keep negative timestamps as-is to maintain continuity across segments
      // (avoid rebasing that forces timestampOffset updates in hls.js)
      // '-avoid_negative_ts',
      // 'make_zero',
      // -------

      // Output
      outputFile,
    ]

    return args
  }

  /**
   * Get CRF (Constant Rate Factor) value based on quality preset
   */
  private getCRFValue(quality: TranscodingOptions['outputQuality']): string {
    switch (quality) {
      case 'ultrafast':
        return '28' // Lower quality, faster encoding
      case 'superfast':
        return '26'
      case 'veryfast':
        return '24'
      case 'faster':
        return '22'
      case 'fast':
        return '20' // Higher quality, slower encoding
      default:
        return '26'
    }
  }

  /**
   * Get video scale filter for resolution limiting
   */
  private getScaleFilter(maxResolution: TranscodingOptions['maxResolution']): string {
    switch (maxResolution) {
      case '480p':
        return 'scale=-2:480:flags=fast_bilinear'
      case '720p':
        return 'scale=-2:720:flags=fast_bilinear'
      case '1080p':
        return 'scale=-2:1080:flags=fast_bilinear'
      default:
        return 'scale=-2:720:flags=fast_bilinear'
    }
  }

  // (Optional) A heuristic checker for HEVC segments can be reintroduced if needed.
}

// Create singleton instance
export const MediaSegmentProcessorSingleton = new MediaSegmentProcessor()
