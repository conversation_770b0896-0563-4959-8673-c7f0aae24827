/**
 * Init Segment Processor - MP4 Box Reconstruction for H.265 → H.264
 *
 * CRITICAL: This module converts H.265 initialization segments to H.264.
 * It reconstructs MP4 box structures, specifically converting hvcC → avcC boxes.
 * Any failure in box conversion will abort the entire stream.
 *
 * Responsibilities:
 * 1. Parse MP4 box structure from H.265 init segments
 * 2. Convert hvcC (HEVC config) → avcC (AVC config) boxes
 * 3. Update sample descriptions (hvc1/hev1 → avc1)
 * 4. Reconstruct valid H.264 initialization segment
 */

type MP4Box = {
  type: string
  size: number
  data: Uint8Array
  offset: number
}

class InitSegmentProcessor {
  private debugPrefix = '[InitSegmentProcessor]'

  /**
   * Process H.265 init segment and convert to H.264
   */
  processInitSegment(originalData: ArrayBuffer, url: string): ArrayBuffer {
    this.log(`Processing init segment from ${url}`)

    if (!originalData || originalData.byteLength === 0) {
      throw new Error('Empty init segment received')
    }

    const data = new Uint8Array(originalData)

    // Parse MP4 boxes
    const boxes = this.parseMP4Boxes(data)
    this.log(`Parsed ${boxes.length} MP4 boxes`)

    // Find and validate required boxes
    const ftypBox = boxes.find((box) => box.type === 'ftyp')
    const moovBox = boxes.find((box) => box.type === 'moov')

    if (!ftypBox) {
      throw new Error('Invalid init segment - missing ftyp box')
    }

    if (!moovBox) {
      throw new Error('Invalid init segment - missing moov box')
    }

    // Check if this is actually an H.265 init segment
    const hasHEVCConfig = this.detectHEVCConfig(moovBox.data)

    if (!hasHEVCConfig) {
      throw new Error('Init segment appears not to contain HEVC configuration')
    }

    this.log('HEVC configuration detected - performing box conversion')

    // Process moov box to convert H.265 → H.264
    const processedMoovData = this.processMoovBox(moovBox.data)

    // Reconstruct the init segment
    const processedData = this.reconstructInitSegment(boxes, processedMoovData)

    // Validate the processed segment
    this.validateProcessedSegment(processedData)

    this.log(
      `Init segment processed - ${originalData.byteLength} → ${processedData.byteLength} bytes`,
    )

    return processedData.buffer
  }

  /**
   * Parse MP4 boxes from data
   */
  private parseMP4Boxes(data: Uint8Array<ArrayBuffer>): Array<MP4Box> {
    const boxes: Array<MP4Box> = []
    let offset = 0

    while (offset < data.length) {
      if (offset + 8 > data.length) {
        break // Not enough data for box header
      }

      // Read box size (4 bytes, big-endian)
      const size = this.readUint32BE(data, offset)

      // Read box type (4 bytes)
      const type = String.fromCodePoint(
        data[offset + 4],
        data[offset + 5],
        data[offset + 6],
        data[offset + 7],
      )

      if (size < 8 || offset + size > data.length) {
        throw new Error(
          `Invalid MP4 box: ${type} with size ${size} at offset ${offset}`,
        )
      }

      // Extract box data
      const boxData = data.slice(offset, offset + size)

      boxes.push({
        type,
        size,
        data: boxData,
        offset,
      })

      offset += size
    }

    return boxes
  }

  /**
   * Detect if moov box contains HEVC configuration
   */
  private detectHEVCConfig(moovData: Uint8Array): boolean {
    // Look for hvcC or hev1/hvc1 boxes
    const moovString = Array.from(moovData, (byte) => String.fromCodePoint(byte)).join(
      '',
    )

    return (
      moovString.includes('hvcC') ||
      moovString.includes('hev1') ||
      moovString.includes('hvc1')
    )
  }

  /**
   * Process moov box to convert H.265 → H.264 configuration
   */
  private processMoovBox(moovData: Uint8Array): Uint8Array<ArrayBuffer> {
    // For MVP, we'll implement a simplified approach:
    // 1. Find hvcC boxes and replace with avcC
    // 2. Find hev1/hvc1 sample entries and replace with avc1
    // 3. Update codec-specific configuration

    let processedData = new Uint8Array(moovData)

    // Replace hvcC box type with avcC
    processedData = this.replaceBoxType(processedData, 'hvcC', 'avcC')

    // Replace sample entry types
    processedData = this.replaceBoxType(processedData, 'hev1', 'avc1')
    processedData = this.replaceBoxType(processedData, 'hvc1', 'avc1')

    // Update codec configuration data
    processedData = this.updateCodecConfiguration(processedData)

    return processedData
  }

  /**
   * Replace box type in MP4 data
   */
  private replaceBoxType(
    data: Uint8Array,
    oldType: string,
    newType: string,
  ): Uint8Array<ArrayBuffer> {
    const oldBytes = new TextEncoder().encode(oldType)
    const newBytes = new TextEncoder().encode(newType)

    if (oldBytes.length !== newBytes.length) {
      throw new Error(`Box type replacement length mismatch: ${oldType} → ${newType}`)
    }

    const result = new Uint8Array(data)

    // Find and replace all occurrences
    for (let i = 0; i <= data.length - oldBytes.length; i++) {
      let matches = true
      for (let j = 0; j < oldBytes.length; j++) {
        if (data[i + j] !== oldBytes[j]) {
          matches = false
          break
        }
      }

      if (matches) {
        for (let j = 0; j < newBytes.length; j++) {
          result[i + j] = newBytes[j]
        }
        this.log(`Replaced ${oldType} → ${newType} at offset ${i}`)
      }
    }

    return result
  }

  /**
   * Update codec configuration from HEVC to AVC
   */
  private updateCodecConfiguration(data: Uint8Array): Uint8Array<ArrayBuffer> {
    // For MVP, we'll generate a standard AVC configuration
    // This is a simplified approach - in production, we'd parse the actual HEVC config
    // and generate an equivalent AVC config

    const result = new Uint8Array(data)

    // Find avcC boxes (previously hvcC) and update their content
    const avcCPositions = this.findBoxPositions(result, 'avcC')

    for (const position of avcCPositions) {
      const avcConfig = this.generateStandardAVCConfig()

      // Replace the box content (keeping the 8-byte header)
      const boxHeaderSize = 8
      const configStart = position + boxHeaderSize
      const configEnd = configStart + avcConfig.length

      if (configEnd <= result.length) {
        result.set(avcConfig, configStart)
        this.log(`Updated AVC config at position ${position}`)
      }
    }

    return result
  }

  /**
   * Generate standard AVC configuration
   */
  private generateStandardAVCConfig(): Uint8Array {
    // Standard AVC configuration for H.264 High Profile Level 4.0
    return new Uint8Array([
      0x01, // configurationVersion
      0x4d, // AVCProfileIndication (High Profile)
      0x40, // profile_compatibility
      0x28, // AVCLevelIndication (Level 4.0)
      0xff, // lengthSizeMinusOne (3 bytes)
      0xe1, // numOfSequenceParameterSets (1)
      // SPS length and data would follow, but for MVP we'll use minimal config
      0x00,
      0x0a, // SPS length
      0x67,
      0x4d,
      0x40,
      0x28,
      0x96,
      0x52,
      0xc0,
      0x00,
      0x00,
      0x03, // Minimal SPS
      0x01, // numOfPictureParameterSets (1)
      0x00,
      0x04, // PPS length
      0x68,
      0xee,
      0x3c,
      0xb0, // Minimal PPS
    ])
  }

  /**
   * Find positions of specific box types
   */
  private findBoxPositions(data: Uint8Array, boxType: string): Array<number> {
    const positions: Array<number> = []
    const typeBytes = new TextEncoder().encode(boxType)

    for (let i = 4; i <= data.length - typeBytes.length; i++) {
      let matches = true
      for (let j = 0; j < typeBytes.length; j++) {
        if (data[i + j] !== typeBytes[j]) {
          matches = false
          break
        }
      }

      if (matches) {
        // Verify this is actually a box header by checking the size
        const boxSize = this.readUint32BE(data, i - 4)
        if (boxSize >= 8 && i - 4 + boxSize <= data.length) {
          positions.push(i - 4) // Return position of box start (including size)
        }
      }
    }

    return positions
  }

  /**
   * Reconstruct init segment with processed moov box
   */
  private reconstructInitSegment(
    originalBoxes: Array<MP4Box>,
    processedMoovData: Uint8Array,
  ): Uint8Array<ArrayBuffer> {
    let totalSize = 0

    // Calculate total size
    for (const box of originalBoxes) {
      if (box.type === 'moov') {
        totalSize += processedMoovData.length
      } else {
        totalSize += box.size
      }
    }

    const result = new Uint8Array(totalSize)
    let offset = 0

    // Copy boxes
    for (const box of originalBoxes) {
      if (box.type === 'moov') {
        // Use processed moov data
        result.set(processedMoovData, offset)
        offset += processedMoovData.length
      } else {
        // Copy original box
        result.set(box.data, offset)
        offset += box.size
      }
    }

    return result
  }

  /**
   * Validate processed init segment
   */
  private validateProcessedSegment(data: Uint8Array<ArrayBuffer>): void {
    // Parse boxes to ensure structure is valid
    const boxes = this.parseMP4Boxes(data)

    // Check required boxes are present
    const ftypBox = boxes.find((box) => box.type === 'ftyp')
    const moovBox = boxes.find((box) => box.type === 'moov')

    if (!ftypBox) {
      throw new Error('Validation failed - missing ftyp box in processed segment')
    }

    if (!moovBox) {
      throw new Error('Validation failed - missing moov box in processed segment')
    }

    // Verify no HEVC references remain
    const hasHEVCConfig = this.detectHEVCConfig(moovBox.data)
    if (hasHEVCConfig) {
      throw new Error(
        'Validation failed - HEVC configuration still present after processing',
      )
    }

    // Verify AVC configuration is present
    const moovString = Array.from(moovBox.data, (byte) =>
      String.fromCodePoint(byte),
    ).join('')
    const hasAVCConfig = moovString.includes('avcC') || moovString.includes('avc1')

    if (!hasAVCConfig) {
      throw new Error(
        'Validation failed - no AVC configuration found in processed segment',
      )
    }

    this.log('Init segment validation successful')
  }

  /**
   * Read 32-bit big-endian unsigned integer
   */
  private readUint32BE(data: Uint8Array, offset: number): number {
    return (
      (data[offset] << 24) |
      (data[offset + 1] << 16) |
      (data[offset + 2] << 8) |
      data[offset + 3]
    )
  }

  /**
   * Debug logging
   */
  private log(message: string, ...args: Array<any>): void {
    // eslint-disable-next-line no-console
    console.log(`${this.debugPrefix} ${message}`, ...args)
  }
}

// Create singleton instance
export const initSegmentProcessor = new InitSegmentProcessor()
