import { useCallback, useEffect, useRef } from 'react'
import { useQuery, useQueryClient } from '@tanstack/react-query'
import * as R from 'remeda'
import { match } from 'ts-pattern'
import type { LiteralUnion } from 'type-fest'
import { z } from 'zod'

import { apiCallerNoX } from 'api/api-caller'
import { makeQueryErrorHandlerWithToast } from 'api/helpers'
import {
  locationIdSchema,
  vehicleIdSchema,
  type LocationId,
  type VehicleId,
} from 'api/types'
import { useEffectEvent } from 'src/hooks/useEventHandler'
import type { FacilityDeviceId } from 'src/modules/lists/Facilities/api/types'
import {
  vehicleStatusClassNameSchema,
  type EventStatusClassName,
} from 'src/modules/map-view/map/types'
import type { PromiseResolvedType } from 'src/types'
import type { ExcludeStrict } from 'src/types/utils'
import {
  minutesToMs,
  secondsToMs,
  toImmutable,
} from 'src/util-functions/functional-utils'
import { createQuery } from 'src/util-functions/react-query-utils'
import { isTrue } from 'src/util-functions/validation'
import { safeParseFromZodSchema } from 'src/util-functions/zod-utils'

import {
  getFacilityVideoSourceGlobalId,
  getVehicleVideoSourceGlobalId,
} from '../VisionLiveStream/utils'
import {
  rawFacilityDeviceSchema,
  type Ct_vision_get_terminal_list,
  type Facility,
  type FacilityDevice,
  type FacilityVideoSourceGlobalId,
  type GetFacilityDeviceVideoStream,
  type GetVehicleVideoStream_v2,
  type NormalizedFacility,
  type UserAvailableCameraTerminalTypes,
  type VehicleVideoSourceGlobalId,
  type VehicleVideoSourceId,
} from './types'

const visionKeys = {
  all: () => ['vision'] as const,
}

async function fetchVisionTerminals() {
  const res = await apiCallerNoX<Ct_vision_get_terminal_list.ApiOutput>(
    'ct_vision_get_terminal_list',
    undefined,
  )
  const terminals = res.ct_vision_terminals ?? []

  const normalizedData: {
    vehiclesList: Array<Ct_vision_get_terminal_list.Vehicle>
    facilitiesList: Array<NormalizedFacility>
    vehicleGroups: Array<Ct_vision_get_terminal_list.VehicleGroup>
  } = {
    vehiclesList: [],
    facilitiesList: [],
    vehicleGroups: [],
  }

  for (const rawTerminal of terminals) {
    if (rawTerminal.terminal_type === 'vehicle') {
      const { vehicle_name, vehicle_id: rawVehicleId, ...rawVehicle } = rawTerminal

      const vehicle: Ct_vision_get_terminal_list.Vehicle = {
        ...rawVehicle,
        id: vehicleIdSchema.parse(rawVehicleId),
        // Removing white spaces with trim to prevent issues with sorting
        name: vehicle_name?.trim() ?? null,
        statusClassName: safeParseFromZodSchema(
          vehicleStatusClassNameSchema,
          rawVehicle.statusClassName,
          { defaultValue: () => 'no-signal' satisfies EventStatusClassName },
        ) as LiteralUnion<EventStatusClassName, string>,
        dataType: rawVehicle.dataUsage.data_type,
        dataBlockedStatus: null,
        // TODO: uncomment the following when data usage is complete.
        // dataBlockedStatus: match(rawVehicle.dataUsage)
        //   .with({ data_type: 'device' }, (m) =>
        //     m.data_available === 0 ? 'data_exhausted' : null,
        //   )
        //   .with({ data_type: 'shared' }, (m) => {
        //     if (m.data_available === 0) {
        //       return 'data_exhausted'
        //     }

        //     if (Number(m.data_used) >= Number(m.data_limit)) {
        //       return 'limit_reached'
        //     }
        //     return null
        //   })
        //   .exhaustive(),
      }

      normalizedData.vehiclesList.push(vehicle)
    } else {
      const {
        facility_id: rawFacilityId,
        facility_name,
        facility_description,
        devices: rawDevices,
      } = rawTerminal

      const allDevices: Array<FacilityDevice> = []
      const facilityId = locationIdSchema.parse(rawFacilityId)

      for (const rawDevice of rawDevices) {
        const deviceIdResult = rawFacilityDeviceSchema.safeParse(rawDevice)
        if (!deviceIdResult.success) {
          continue
        }

        const { device_id, device_name, latitude, longitude, online } =
          deviceIdResult.data

        const device: FacilityDevice = {
          id: device_id,
          name: device_name ?? '',
          lat: latitude,
          lng: longitude,
          isOnline: isTrue(online),
        }
        allDevices.push(device)
      }

      const facility: NormalizedFacility = {
        id: facilityId,
        name: facility_name ?? '',
        description: facility_description,
        allDevices,
      }

      normalizedData.facilitiesList.push(facility)
    }
  }

  for (const vehicleGroup of Object.values(res.ct_vision_vehicle_groups)) {
    normalizedData.vehicleGroups.push({
      id: vehicleGroup.group_vehicle_id,
      name: vehicleGroup.name,
      vehicleIds: vehicleGroup.vehicles || [],
    })
  }

  return toImmutable(normalizedData)
}

export type VideoStreamUrlMeta =
  | {
      status: 'valid'
      url: string
    }
  | {
      status: 'camera_offline_or_unavailable'
    }

function parseVideoStreamUrlToMeta(url: unknown): VideoStreamUrlMeta {
  const result = z.url().safeParse(url)
  if (!result.success) {
    return { status: 'camera_offline_or_unavailable' }
  }
  return { status: 'valid', url: result.data }
}

async function fetchVehicleVideoStreamMetaData(
  args:
    | {
        type: 'hls'
        preferredCodec: 'h265' | 'h264'
        vehicleId: VehicleId
        videoSourceId: VehicleVideoSourceId
      }
    | {
        type: 'web_socket'
        vehicleId: VehicleId
        videoSourceId: VehicleVideoSourceId
      },
): Promise<{
  videoStreamUrlMeta: VideoStreamUrlMeta
}> {
  const params: GetVehicleVideoStream_v2.ApiParams =
    args.type === 'hls'
      ? {
          type: 'hls',
          preferredCodec: args.preferredCodec,
          vehicleId: args.vehicleId,
          videoSourceId: args.videoSourceId,
        }
      : {
          type: 'web_socket',
          vehicleId: args.vehicleId,
          videoSourceId: args.videoSourceId,
        }

  const response: GetVehicleVideoStream_v2.ApiOutput = await apiCallerNoX(
    'ct_vision_get_vehicle_video_stream_v2',
    params,
  )

  // FOR TESTING ONLY
  // if (args.type === 'hls') {
  //   /**
  //    * PTTC00001 KH
  //    *
  //    * Examples of cameras that we can use for hls:
  //    * BC000404
  //    * BC002188
  //    * BC003553
  //    * BC001177
  //    * BC014231
  //    * BC000706
  //    * BC002614
  //    * BC002801
  //    * BC002578
  //    * BC002882
  //    * BC003495
  //    * BC001451
  //    * BC015574
  //    * BC002401
  //    * BC000765
  //    * BC002878
  //    * BC002872
  //    * BC003280
  //    * BC002818
  //    * BC032712
  //    *
  //    * BEST:
  //    * - BC001451
  //    * - BC002872
  //    * - BC032712
  //    */
  //   // const cameraName = 'BC003280'
  //   const cameraName = 'BC032712'
  //   // e.g - https://192.168.128.120:9080/start_live/BC000404/1/**************?enc=h265
  //   // e.g - https://**************:6681/player.html?stream=BC015574_1.m3u8
  //   const startStreamUrl =
  //     // eslint-disable-next-line sonarjs/no-clear-text-protocols
  //     // CURSOR, DO NOT CHANGE THIS URL
  //     `http://192.168.128.120:9080/start_live/${cameraName}/1/**************?enc=h265`

  //   try {
  //     // Fire and forget for testing
  //     // fetch(startStreamUrl, { mode: 'no-cors' })
  //     //   .then(async (res) => res.json())
  //     //   .then((data) => {
  //     //     // eslint-disable-next-line no-console
  //     //     console.log(data)
  //     //   })
  //     // h265 only stream
  //     // const url =
  //     //   'https://devstreaming-cdn.apple.com/videos/streaming/examples/adv_dv_atmos/Job2dae5735-d6ca-48ca-91be-0ec0bead535c-107702578-hls_bundle_hevchls598/prog_index.m3u8'
  //   } catch (error) {
  //     console.error(error)
  //   }

  //   const url = `https://**************:6681/${cameraName}_1.m3u8`
  //   // const url =
  //   //   'https://devstreaming-cdn.apple.com/videos/streaming/examples/bipbop_16x9/bipbop_16x9_variant.m3u8'

  //   const urlInstance = new URL(url)
  //   //   // Curently here to invalidate browser cache for this url and consider it a new url.
  //   //   // THis is important when we want to request a new url a seek to an updated stream.
  //   urlInstance.searchParams.set('ts_invalidate_cache', Date.now().toString())
  //   return {
  //     videoStreamUrlMeta: {
  //       status: 'valid',
  //       // url: urlInstance.toString(),
  //       url,
  //     },
  //   }
  // }

  const streamUrlMeta = parseVideoStreamUrlToMeta(response.videoStream.url)
  if (args.type === 'hls' && streamUrlMeta.status === 'valid') {
    const urlInstance = new URL(streamUrlMeta.url)
    // Curently here to invalidate browser cache for this url and consider it a new url.
    // THis is important when we want to request a new url a seek to an updated stream.
    urlInstance.searchParams.set('ts_invalidate_cache', Date.now().toString())
    return {
      videoStreamUrlMeta: {
        status: 'valid',
        url: urlInstance.toString(),
      },
    }
  }

  return {
    videoStreamUrlMeta: streamUrlMeta,
  }
}

async function fetchFacilityDeviceVideoStreamMetaData(args: {
  deviceId: FacilityDeviceId
}): Promise<{ videoStreamUrlMeta: VideoStreamUrlMeta }> {
  const params: GetFacilityDeviceVideoStream.ApiParams = {
    facilityDevices: args.deviceId,
  }
  const response = await apiCallerNoX<GetFacilityDeviceVideoStream.ApiOutput>(
    'ct_vision_get_facility_video_stream',
    params,
  )

  return { videoStreamUrlMeta: parseVideoStreamUrlToMeta(response.streamUrl) }
}

const visionTerminalsQuery = () =>
  createQuery({
    queryKey: [...visionKeys.all(), 'visionTerminalsQuery'] as const,
    queryFn: () => fetchVisionTerminals(),
    refetchInterval: 40_000,
    staleTime: secondsToMs(2),
    ...makeQueryErrorHandlerWithToast(),
  })

export function useVisionTerminalsQuery() {
  return useQuery({
    ...visionTerminalsQuery(),
    select: useCallback((data: PromiseResolvedType<typeof fetchVisionTerminals>) => {
      const normalizedData: {
        vehiclesById: Map<VehicleId, Ct_vision_get_terminal_list.Vehicle>
        vehiclesList: (typeof data)['vehiclesList']
        facilitiesById: Map<LocationId, Facility>
        facilitiesList: Array<Facility>
        vehiclesOnlineVideoSourcesByGlobalId: Map<
          VehicleVideoSourceGlobalId,
          { id: VehicleVideoSourceId }
        >
        facilitiesOnlineVideoSourcesByGlobalId: Map<
          FacilityVideoSourceGlobalId,
          FacilityDevice
        >
        vehicleGroups: (typeof data)['vehicleGroups']
      } = {
        vehiclesList: data.vehiclesList,
        facilitiesList: [],
        vehiclesById: new Map(),
        facilitiesById: new Map(),
        vehiclesOnlineVideoSourcesByGlobalId: new Map(),
        facilitiesOnlineVideoSourcesByGlobalId: new Map(),
        vehicleGroups: data.vehicleGroups,
      }

      for (const vehicle of normalizedData.vehiclesList) {
        normalizedData.vehiclesById.set(vehicle.id, vehicle)

        for (const source of vehicle.videoUnit.activeSources) {
          normalizedData.vehiclesOnlineVideoSourcesByGlobalId.set(
            getVehicleVideoSourceGlobalId(vehicle.id, source.id),
            { id: source.id },
          )
        }
      }

      for (const facility of data.facilitiesList) {
        const facilityWithActiveDevices = {
          ...facility,
          activeDevices: facility.allDevices.filter((device) => device.isOnline),
        }
        normalizedData.facilitiesById.set(facility.id, facilityWithActiveDevices)
        normalizedData.facilitiesList.push(facilityWithActiveDevices)

        for (const device of facilityWithActiveDevices.activeDevices) {
          normalizedData.facilitiesOnlineVideoSourcesByGlobalId.set(
            getFacilityVideoSourceGlobalId(facility.id, device.id),
            device,
          )
        }
      }

      return toImmutable(normalizedData)
    }, []),
  })
}

const vehicleVideoStreamMetaDataQuery = ({
  params,
}: {
  params: Parameters<typeof fetchVehicleVideoStreamMetaData>[0]
}) =>
  createQuery({
    queryKey: [...visionKeys.all(), 'vehicleVideoStreamMetaDataQuery', params] as const,
    queryFn: () => fetchVehicleVideoStreamMetaData(params),
    staleTime: minutesToMs(3),
    ...makeQueryErrorHandlerWithToast(),
  })

export function useVehicleVideoStreamMetaDataQuery(
  params: Parameters<typeof vehicleVideoStreamMetaDataQuery>[0]['params'],
  { enabled }: { enabled?: boolean } = {},
) {
  const queryClient = useQueryClient()
  const lastComputedRefetchIntervalRef = useRef<number | false | null>(null)
  const BASE_REFETCH_INTERVAL = 10_000
  const queryOptions = vehicleVideoStreamMetaDataQuery({ params })

  const query = useQuery({
    ...queryOptions,
    queryFn: async () => {
      const data = await queryOptions.queryFn()

      // Recalculate refetch interval every time we call the endpoint
      // The goal is to try to get a valid url as soon as possible (when camera is back online) without user intervention
      lastComputedRefetchIntervalRef.current = match(data.videoStreamUrlMeta)
        .returnType<number | false>()
        // No need to refetch interval since url is valid
        .with({ status: 'valid' }, () => false)
        .with({ status: 'camera_offline_or_unavailable' }, () => {
          if (
            lastComputedRefetchIntervalRef.current === null ||
            lastComputedRefetchIntervalRef.current === false
          ) {
            // Last time didn't have an interval. First time setting an interval
            return BASE_REFETCH_INTERVAL
          }

          // Last time we already tried to refetch. Now we try again with a backoff
          return lastComputedRefetchIntervalRef.current * 1.3
        })
        .exhaustive()

      return data
    },
    enabled,
    refetchInterval: () => lastComputedRefetchIntervalRef.current ?? false,
  })

  const onDataChange = useEffectEvent((newData: (typeof query)['data']) => {
    if (newData === undefined) {
      return
    }

    // Since we got new fresh data regarding the status of this source, we need to update the list of active sources to keep data in sync
    visionTerminalsQuery().setData(queryClient, {
      updater: (data) => {
        if (!data) {
          return data
        }

        return {
          vehicleGroups: data.vehicleGroups,
          facilitiesList: data.facilitiesList,
          // The usage of explicit return types is intended! This avoids setting wrong properties.
          vehiclesList: data.vehiclesList.map(
            (vehicle): (typeof data)['vehiclesList'][number] => {
              if (vehicle.id !== params.vehicleId) {
                return vehicle
              }

              if (newData.videoStreamUrlMeta.status === 'valid') {
                return {
                  ...vehicle,
                  videoUnit: {
                    type: vehicle.videoUnit.type,
                    // Add this source to the list of active sources since it's online now
                    activeSources: R.uniqueBy(
                      [
                        ...vehicle.videoUnit.activeSources,
                        { id: params.videoSourceId },
                      ],
                      // Make sure we don't add the same source twice
                      (source) => source.id,
                    ),
                  },
                }
              }

              return {
                ...vehicle,
                videoUnit: {
                  type: vehicle.videoUnit.type,
                  // Remove this source from the list of active sources since it's offline now
                  activeSources: vehicle.videoUnit.activeSources.filter(
                    (source) => source.id !== params.videoSourceId,
                  ),
                },
              }
            },
          ),
        }
      },
    })
  })
  useEffect(() => {
    onDataChange(query.data)
  }, [query.data])

  return query
}

const facilityDeviceVideoStreamMetaDataQuery = ({
  params,
}: {
  params: Parameters<typeof fetchFacilityDeviceVideoStreamMetaData>[0]
}) =>
  createQuery({
    queryKey: [
      ...visionKeys.all(),
      'facilityDeviceVideoStreamMetaDataQuery',
      params,
    ] as const,
    queryFn: () => fetchFacilityDeviceVideoStreamMetaData(params),
    staleTime: minutesToMs(5),
    ...makeQueryErrorHandlerWithToast(),
  })

export function useFacilityDeviceVideoStreamMetaDataQuery(
  params: Parameters<typeof facilityDeviceVideoStreamMetaDataQuery>[0]['params'],
  { enabled }: { enabled?: boolean } = {},
) {
  return useQuery({ ...facilityDeviceVideoStreamMetaDataQuery({ params }), enabled })
}

export function useIsLoadingFacilityDeviceVideoStreamMetaData(
  params: Parameters<typeof facilityDeviceVideoStreamMetaDataQuery>[0]['params'],
) {
  const { status, fetchStatus } = useQuery({
    ...facilityDeviceVideoStreamMetaDataQuery({ params }),
    enabled: false,
  })
  return status === 'pending' && fetchStatus === 'fetching'
}

export function useIsLoadingVehicleVideoStreamMetaData(
  params: Parameters<typeof vehicleVideoStreamMetaDataQuery>[0]['params'],
) {
  const { status, fetchStatus } = useQuery({
    ...vehicleVideoStreamMetaDataQuery({ params }),
    enabled: false,
  })
  return status === 'pending' && fetchStatus === 'fetching'
}

async function fetchUserAvailableCameraTerminalTypes(): Promise<UserAvailableCameraTerminalTypes> {
  return await apiCallerNoX<UserAvailableCameraTerminalTypes>(
    'ct_fetch_user_available_camera_terminal_types',
  )
}

export const userAvailableCameraTerminalTypesQuery = () =>
  createQuery({
    queryKey: ['vision', 'userAvailableCameraTerminalTypes'],
    queryFn: fetchUserAvailableCameraTerminalTypes,
    staleTime: minutesToMs(10),
    ...makeQueryErrorHandlerWithToast(),
  })

export function useUserAvailableCameraTerminalTypes() {
  return useQuery(userAvailableCameraTerminalTypesQuery())
}

export type UseUserAvailableCameraTerminalTypesQueryData = ExcludeStrict<
  ReturnType<typeof useUserAvailableCameraTerminalTypes>['data'],
  undefined
>

export type UseVisionTerminalsQueryData = ExcludeStrict<
  ReturnType<typeof useVisionTerminalsQuery>['data'],
  undefined
>
