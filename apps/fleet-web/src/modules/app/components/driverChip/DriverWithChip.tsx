import { isEmpty } from 'lodash'
import {
  Avatar,
  Chip,
  colors,
  OverflowTypography,
  Stack,
  Tooltip,
  Typography,
  type ChipProps,
} from '@karoo-ui/core'
import FiberManualRecordIcon from '@mui/icons-material/FiberManualRecord'
import PersonOutlinedIcon from '@mui/icons-material/PersonOutlined'
import PinOutlinedIcon from '@mui/icons-material/PinOutlined'
import QrCodeScannerIcon from '@mui/icons-material/QrCodeScanner'
import SellOutlinedIcon from '@mui/icons-material/SellOutlined'
import { useHistory } from 'react-router'
import { match, P } from 'ts-pattern'

import type { DriverId, DriverName } from 'api/types'
import { DriverLinkingMethod } from 'api/vehicles/useVehicleDetailsQuery'
import SVGIcon from 'src/components/Icon/SVGIcon'
import { getDriverDetailsModalMainPath } from 'src/modules/app/GlobalModals/DriverDetails/utils'
import { ctIntl } from 'src/util-components/ctIntl'

import IdCardIcon from 'assets/svg/DriverProfile/IdCard.svg'
import KeyIcon from 'assets/svg/DriverProfile/Key.svg'
import ListIcon from 'assets/svg/DriverProfile/listSelection.svg'
import NFCIcon from 'assets/svg/DriverProfile/NFC.svg'

type DriverLinkingMethods = (
  | { type: DriverLinkingMethod }
  | { type: null | undefined }
) & { value?: string | null }
const getAvatarBgColor = (name: string) => {
  if (!name || name.length === 0) {
    return colors.grey[100]
  }
  const generatedNumber = (name[0].codePointAt(0) ?? 0) + name.length
  const colorArray = Object.values(colors)
  const bgColor = colorArray[generatedNumber % colorArray.length]
  return (bgColor as { [key: string]: string })[100]
}

const handlerSVG = (icon: string) => (
  <div style={{ height: '14px', maxWidth: '22px' }}>
    <SVGIcon
      height="14"
      svg={icon}
    />
  </div>
)

export default function DriverWithChip({
  driverId,
  //linked driver
  driverName,
  linkingMethod,
  picture,
}: {
  driverId: DriverId | null
  driverName: DriverName
  linkingMethod: DriverLinkingMethods
  picture?: string | null
}) {
  const history = useHistory()

  const { linkMethod } = match(linkingMethod)
    .with({ type: DriverLinkingMethod.LINKAGE_QRCODE }, () => ({
      linkMethod: (
        <QrCodeScannerIcon
          color="primary"
          sx={{ fontSize: 14 }}
        />
      ),
    }))
    .with({ type: DriverLinkingMethod.LINKAGE_LIST }, () => ({
      linkMethod: handlerSVG(ListIcon),
    }))
    .with({ type: DriverLinkingMethod.LINKAGE_NFC }, () => ({
      linkMethod: handlerSVG(NFCIcon),
    }))
    .with({ type: DriverLinkingMethod.LINKAGE_PINPAD }, () => ({
      linkMethod: (
        <PinOutlinedIcon
          color="primary"
          sx={{ fontSize: 14 }}
        />
      ),
    }))
    .with({ type: DriverLinkingMethod.LINKAGE_MIKEY }, () => ({
      linkMethod: handlerSVG(KeyIcon),
    }))
    .with({ type: DriverLinkingMethod.LINKAGE_DIDCARD }, () => ({
      linkMethod: handlerSVG(IdCardIcon),
    }))
    .with(
      {
        type: P.union(DriverLinkingMethod.LINKAGE_DIDTAG, DriverLinkingMethod.DID_TAG),
      },
      () => ({
        linkMethod: (
          <SellOutlinedIcon
            color="primary"
            sx={{ fontSize: 14 }}
          />
        ),
      }),
    )
    .with({ type: DriverLinkingMethod.DEFAULT }, () => ({
      linkMethod: (
        <Typography
          variant="caption"
          sx={({ palette }) => ({
            fontSize: '10px !important',
            color: `${palette.text.secondary} !important`,
            fontWeight: 400,
            lineHeight: 'normal',
          })}
        >
          {ctIntl.formatMessage({ id: 'default' })}
        </Typography>
      ),
    }))
    .with(
      {
        type: P.union(
          null,
          undefined,
          DriverLinkingMethod.E_TACHO,
          DriverLinkingMethod.FLEET_API,
        ),
      },
      () => ({
        linkMethod: null,
      }),
    )
    .exhaustive()

  if (driverName.status === 'UNDISCLOSED') {
    return (
      <DriverChip
        icon={<PersonOutlinedIcon />}
        label={ctIntl.formatMessage({ id: 'vehicle.driverName.undisclosed' })}
      />
    )
  }

  const driverNameStr = driverName.name
  const isClickable = !isEmpty(driverId)

  if (driverNameStr) {
    return (
      <DriverChip
        DriverTagId={linkingMethod.value}
        defaultDriver={linkingMethod.type === DriverLinkingMethod.DEFAULT}
        icon={
          <Avatar
            variant="rounded"
            src={picture ?? undefined}
            sx={({ palette }) => ({
              width: '19px',
              height: '19px',
              margin: '0 !important',
              bgcolor: getAvatarBgColor(driverNameStr),
              borderRadius: '2px',
              color: `${palette.text.secondary} !important`,
            })}
          >
            <Typography
              variant="caption"
              sx={{ fontWeight: 400 }}
            >
              {driverNameStr[0].toUpperCase()}
            </Typography>
          </Avatar>
        }
        label={
          <Stack
            direction="row"
            alignItems="center"
            gap={0.5}
          >
            <OverflowTypography
              typographyProps={{
                variant: 'caption',
                sx: { fontWeight: 500 },
              }}
            >
              {driverNameStr}
            </OverflowTypography>
            {!isEmpty(linkMethod) && (
              <>
                <FiberManualRecordIcon
                  sx={(theme) => ({ color: theme.palette.grey[300], fontSize: 5 })}
                />
                {linkMethod}
              </>
            )}
          </Stack>
        }
        sx={{
          cursor: isClickable ? 'pointer' : 'default',
        }}
        {...(isClickable
          ? {
              onClick: () => {
                history.push(
                  getDriverDetailsModalMainPath(history.location, driverId as DriverId),
                )
              },
            }
          : {})}
      />
    )
  }

  // if the driver name is empty, we assume the driver is not assigned yet
  if (linkingMethod.value) {
    return (
      <DriverChip
        DriverTagId={linkingMethod.value}
        DriverIdentified={false}
        label={
          <Stack
            direction="row"
            alignItems="center"
            gap={0.5}
          >
            <OverflowTypography
              typographyProps={{
                variant: 'caption',
                sx: { fontWeight: 500 },
              }}
            >
              {linkingMethod.value}
            </OverflowTypography>
            {!isEmpty(linkMethod) && (
              <>
                <FiberManualRecordIcon
                  sx={(theme) => ({ color: theme.palette.grey[300], fontSize: 5 })}
                />
                {linkMethod}
              </>
            )}
          </Stack>
        }
      />
    )
  }

  return (
    <DriverChip
      icon={<PersonOutlinedIcon />}
      DriverTagId={linkingMethod.value}
      label={
        <OverflowTypography
          typographyProps={{
            variant: 'caption',
            sx: { color: 'text.secondary' },
          }}
        >
          {ctIntl.formatMessage({ id: 'No linked driver' })}
        </OverflowTypography>
      }
    />
  )
}

const DriverChip = (
  props: ChipProps & {
    DriverTagId?: string | null
    DriverIdentified?: boolean
    defaultDriver?: boolean
  },
) => {
  const {
    sx: sxProps,
    DriverTagId,
    DriverIdentified = true,
    defaultDriver = false,
    ...rest
  } = props
  const driverIdentified = DriverIdentified ? !isEmpty(DriverTagId) : false
  const tooltipText = isEmpty(DriverTagId)
    ? ctIntl.formatMessage({ id: 'driverName.noTagId.tooltip' })
    : ctIntl.formatMessage(
        {
          id: driverIdentified
            ? 'driverName.tagId.tooltip'
            : 'driverName.tagIdWithoutDriver.tooltip',
        },
        { values: { tagId: DriverTagId } },
      )

  return (
    <Tooltip
      title={defaultDriver ? '' : tooltipText}
      placement="top-end"
      arrow={false}
    >
      <Chip
        {...rest}
        variant="outlined"
        sx={{
          borderColor: 'divider',
          borderRadius: '3px',
          fontSize: '12px',
          padding: '1px',
          width: 'fit-content',
          maxWidth: '180px',
          '& .MuiChip-label': {
            paddingRight: '9px',
          },
          ...sxProps,
        }}
      />
    </Tooltip>
  )
}
