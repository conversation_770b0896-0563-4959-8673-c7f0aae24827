import { useMemo } from 'react'
import { startCase } from 'lodash'
import {
  Box,
  Chip,
  CircularProgress,
  OverflowTypography,
  Skeleton,
  Stack,
  Tooltip,
  Typography,
} from '@karoo-ui/core'
import { match } from 'ts-pattern'

import useDriverDetailsQuery from 'api/drivers/useDriverDetailsQuery'
import type { DriverId, VehicleType } from 'api/types'
import DriverAvatar from 'src/components/Driver/Avatar'
import StarsRating from 'src/components/StarsRating'
import { useDriversQuery } from 'src/modules/api/useDriversQuery'
import { useDriverDetailsContext } from 'src/modules/app/GlobalModals/DriverDetails/DriverDetailsContext'
import { ctIntl } from 'src/util-components/ctIntl'
import vehicleIcons from 'src/util-components/vehicle-icons'
import { makeSanitizedInnerHtmlProp } from 'src/util-functions/security-utils'
import { generateImgUriFromBase64 } from 'src/util-functions/string-utils'

import DriverPictureButton from './PictureButton'

type Props = {
  driverId: DriverId
}

const DriverDetailsStatusBox = ({ driverId }: Props) => {
  const driverDetailsQuery = useDriverDetailsQuery({
    driverId,
  })

  const { permissions } = useDriverDetailsContext()

  const driversQuery = useDriversQuery()

  const driverInfoFromDriversListWithStatus = useMemo(() => {
    const data = driversQuery.data
      ? driversQuery.data.activeAndInactiveDrivers.find((d) => d.id === driverId)
      : undefined

    return { data, status: driversQuery.status }
  }, [driversQuery.data, driversQuery.status, driverId])

  return match(driverDetailsQuery)
    .with({ status: 'error' }, () => null)
    .with({ status: 'pending' }, () => (
      <Stack
        minHeight={158}
        sx={{
          alignItems: 'center',
          justifyContent: 'center',
        }}
      >
        <CircularProgress />
      </Stack>
    ))
    .with({ status: 'success' }, ({ data: { driverDetails } }) => (
      <Stack
        data-testid="DriverDetailsStatusBox"
        sx={{ p: 2, pb: 0 }}
        gap={2}
      >
        <Stack
          direction={'row'}
          gap={2}
          sx={{ alignItems: 'center' }}
        >
          <DriverAvatar
            avatar={generateImgUriFromBase64(driverDetails.logoImageBase64)}
          />

          <Stack
            gap={1}
            sx={{ overflow: 'hidden' }}
          >
            <OverflowTypography typographyProps={{ variant: 'subtitle2' }}>
              {driverDetails.name}
            </OverflowTypography>

            {/* TODO: rating must be returned from the driver details endpoint. Change it when the endpoint is updated */}
            {match(driverInfoFromDriversListWithStatus)
              .with({ status: 'pending' }, () => (
                <Skeleton
                  variant="rounded"
                  width={60}
                  height={18}
                />
              ))
              .with(
                { status: 'success' },
                ({ data: diverInfo }) =>
                  diverInfo?.rating !== undefined && (
                    <StarsRating value={diverInfo.rating} />
                  ),
              )
              .with({ status: 'error' }, () => null)
              .exhaustive()}

            <Chip
              label={ctIntl.formatMessage({
                id: driverDetails.active ? 'Active Driver' : 'Inactive Driver',
              })}
              color={driverDetails.active ? 'success' : 'error'}
              variant="outlined"
              size="small"
              sx={{ width: 'fit-content' }}
            />
          </Stack>
        </Stack>
        <DriverPictureButton
          picture={driverDetails.logoImageBase64}
          driverId={driverId}
          disabled={!permissions.edit}
        />

        {/* TODO: vehicle registration must be returned from the driver details endpoint. Change it when the endpoint is updated */}
        {match(driverInfoFromDriversListWithStatus)
          .with({ status: 'pending' }, () => (
            <Skeleton
              variant="rounded"
              width={'100%'}
              height={32}
            />
          ))
          .with(
            { status: 'success' },
            ({ data: diverInfo }) =>
              !!diverInfo?.vehicleRegistration && (
                <Tooltip
                  title={ctIntl.formatMessage({
                    id: 'driverDetails.vehicleRegistration.tooltipMessage',
                  })}
                >
                  <Chip
                    sx={{ '& .MuiChip-label': { width: '100%' } }}
                    label={
                      <Stack
                        direction="row"
                        gap={1}
                        justifyContent="center"
                        alignItems="center"
                      >
                        <Box
                          sx={(theme) => ({
                            height: '20px',
                            '& svg': {
                              height: '20px',
                              path: {
                                fill: theme.palette.grey[500],
                              },
                            },
                          })}
                          {...makeSanitizedInnerHtmlProp({
                            dirtyHtml:
                              vehicleIcons[diverInfo.vehicleType as VehicleType],
                          })}
                        />
                        <Typography variant="caption">
                          {diverInfo.vehicleRegistration}
                        </Typography>
                        <Stack
                          gap={1}
                          direction="row"
                          alignItems="center"
                        >
                          <Skeleton
                            variant="circular"
                            width={7}
                            height={7}
                            animation={false}
                          />
                          <Typography variant="caption">
                            {startCase(diverInfo.statusClassName)}
                          </Typography>
                        </Stack>
                      </Stack>
                    }
                  />
                </Tooltip>
              ),
          )
          .with({ status: 'error' }, () => null)
          .exhaustive()}
      </Stack>
    ))
    .exhaustive()
}

export default DriverDetailsStatusBox
