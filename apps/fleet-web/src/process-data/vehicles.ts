import { get, groupBy, flatMap, concat } from 'lodash'
import { insensitiveCompare } from 'src/util-functions/string-utils'

export const vehicleSortMethods = {
  alphabetical: <Vehicle extends Record<string, any>>(
    vehicles: ReadonlyArray<Vehicle>,
    propToSortBy = 'name',
  ): ReadonlyArray<Vehicle> =>
    [...vehicles].sort((a, b) => {
      const byProp = insensitiveCompare(
        get(a, propToSortBy) || '',
        get(b, propToSortBy) || '',
      )

      const aIsGroup = !a.statusClassName
      const bIsGroup = !b.statusClassName

      if (aIsGroup && bIsGroup) return byProp
      if (aIsGroup) return -1
      if (bIsGroup) return 1

      const aNoSignal = a.statusClassName.includes('no-signal')
      const bNoSignal = b.statusClassName.includes('no-signal')

      if (aNoSignal && bNoSignal) {
        const aWithTime = a.statusClassName === 'no-signal ns-with-time'
        const bWithTime = b.statusClassName === 'no-signal ns-with-time'

        if (aWithTime && bWithTime) return byProp
        if (aWithTime) return -1
        if (bWithTime) return 1

        return byProp
      }

      if (aNoSignal) return 1
      if (bNoSignal) return -1

      return byProp
    }),
  status: <Vehicle extends Record<string, any>>(
    vehicles: ReadonlyArray<Vehicle>,
    propToSortBy = 'name',
  ): ReadonlyArray<Vehicle> => {
    const grouped = groupBy(vehicles, 'statusClassName')

    // Priority order for known groups
    const priorityOrder = [
      'undefined',
      'driving',
      'moving-ignition-off',
      'stationary',
      'idling',
      'ignition-off',
      'maintenance',
      'no-signal ns-with-time',
      'no-signal',
    ] as const

    // Flatten known groups in order
    const ordered = flatMap(priorityOrder, (key) =>
      (grouped[key] ?? []).sort((a, b) =>
        insensitiveCompare(get(a, propToSortBy) ?? '', get(b, propToSortBy) ?? ''),
      ),
    )
    //

    // Order vehicles with class not declared in priority array
    const remainingKeys = Object.keys(grouped).filter(
      (k) => !priorityOrder.includes(k as any),
    )

    const remaining = flatMap(remainingKeys, (key) =>
      (grouped[key] ?? []).sort((a, b) =>
        insensitiveCompare(get(a, propToSortBy) ?? '', get(b, propToSortBy) ?? ''),
      ),
    )
    //

    return concat(ordered, remaining)
  },
}
