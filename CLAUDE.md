# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is a fleet management web application built with React and TypeScript, using Nx as the monorepo toolset. The project consists of:

- **Main Application**: `apps/fleet-web` - The primary React application for fleet management
- **Shared Libraries**: `libs/karoo-ui` and `libs/karoo-utils` - Internal component library and utilities
- **Package Manager**: pnpm with specific configurations for FontAwesome Pro packages

## Architecture

### Technology Stack

- **Frontend**: React 18.3.1 with TypeScript 5.8.3
- **Build System**: Rspack (webpack replacement) with Nx orchestration
- **State Management**: Redux Toolkit with Redux Saga for side effects (older parts of the app)
- **State data structure**: Prevent impossible states by using discriminated unions as much as possible. Avoid fragile conflicting state such as isLoading, isError, isSuccess.
- **Styling**: Material-UI components exposed through a wrapper "@karoo-ui/core"
- **Maps**: Multiple providers (Google Maps, Leaflet, OpenLayers)
- **Testing**: Vitest for unit tests, Cypress for component/e2e tests
- **Linting**: ESLint with oxlint for performance, TypeScript compiler for type checking

### Key Module Structure

The application is organized into feature modules under `apps/fleet-web/src/modules/`:

- `admin/` - Administrative functions and user management
- `dashboard/` - Various dashboard views (Live, Overview, Custom, Coaching)
- `delivery/` and `deliveryRevamp/` - Delivery management (legacy and new versions)
- `map-view/` - Map-based fleet visualization
- `alerts/` - Alert management and notifications
- `vision/` - Vision AI features for fleet monitoring
- `reports/` - Report generation and viewing
- `lists/` - Data management (vehicles, drivers, geofences, etc.)
- `maintenance/` - Vehicle maintenance and inspection management
- `tachograph/` - Tachograph compliance features

### State Management Patterns

- Redux slices in `duxs/` directory with corresponding sagas in `sagas/` (older parts of the app)
- React Query for server state management (newer parts of the app)
- Local state with custom hooks for component-specific logic

## Common Development Commands

### Building

```bash
# Production build
pnpm build

# CI build check
pnpm nx run fleet-web:build-ci-check
```

## Important Development Notes

### FontAwesome Pro Setup

The project uses FontAwesome Pro packages that are locally forked in `apps/fleet-web/src/_third-party-libs-forks/`. These are installed as file dependencies and require special handling.

### Map Providers

The application supports multiple map providers:

- Google Maps (primary)
- Leaflet with various tile providers
- OpenLayers for specific use cases

### Internationalization

- Uses react-intl for internationalization
- Locale files are in `apps/fleet-web/locales/`
- Update translations with `pnpm update-locales`

### Testing Strategy

- Unit tests with Vitest
- Component tests with Cypress
- Component tests are split into chunks for CI: `component-test-ci-chunk1`, `component-test-ci-chunk2`, `component-test-ci-chunk3`

### Code Quality

- ESLint with strict rules including security plugins
- oxlint for additional performance
- Prettier for code formatting
- Husky for git hooks with lint-staged

### Code Style Guidelines

#### Avoid usage of useEffect when possible

For predictable side effect handling you should handle side effects in event handlers.
When that is not possible, you can try to use `useEffect` along with `useEffectEvent` (our polyfill for react 19 experimental useEffectEvent hook) for events within effects to prevent adding unnecessary dependencies to `useEffect` and causing infinite loops.
If that is not possible, you can fallback to adding `useEffect` the "standard" way.

#### Usage of useRef vs useState

If you want to use something that changes BUT is never used in render, prefer storing it on a ref. This will prevent issues in `useEffect` that would be caused if the value was stored in `useState` (as it would have to be added on the dependency array of `useEffect`)

#### Conditional Rendering in JSX

**STRONGLY PREFER** `ts-pattern` over ternary operators for conditional rendering in JSX components:

```typescript
{match(streamType)
  .with('live', () => <Component1 />)
  .with('past', () => <Component2 />)
  .with('other', () => <Component3 />)
  .exhaustive()}
```

**Benefits of ts-pattern:**

- **Type Safety**: Exhaustiveness checking ensures all cases are handled
- **Readability**: Cleaner syntax for complex conditional logic
- **Maintainability**: Easier to add/remove cases without nested ternary complexity

## Key File Locations

- **Main Entry**: `apps/fleet-web/src/index.tsx`
- **App Root**: `apps/fleet-web/src/app-root.tsx`
- **Redux Store**: `apps/fleet-web/src/store.ts`
- **API Layer**: `apps/fleet-web/src/api/`
- **Shared Components**: `apps/fleet-web/src/util-components/`
- **Shared Utilities**: `apps/fleet-web/src/util-functions/`
- **UI library**: `@karoo-ui/core`
- **Types**: `apps/fleet-web/src/types/`

## Development Workflow

1. **Starting Development**: Use `pnpm start` to start the development server
2. **Making Changes**: Follow the established module structure and patterns
3. **Building**: Use `pnpm build` to create production build

## Commit Standards

The project follows Conventional Commits specification with commitizen integration. Use proper commit prefixes (feat, fix, chore, etc.) as defined in the README.md.

### Playwright mcp important notes

When you want to access the browser but you are redirected to the login page (you are not logged in), please ask me to provide you with an `otp_url_suffix`.
When I give you the `otp_url_suffix`, you should open the browser again on:
`localhost:8080?otp_url_suffix`
MAKE SURE to replace the placeholder `otp_url_suffix` with the actual suffix.

# How to logout

1. Click on the lower left corn button (that will have the username).
2. Click "Logout" or the equivalent in the current language
