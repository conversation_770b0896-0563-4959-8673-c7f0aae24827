# See http://help.github.com/ignore-files/ for more about ignoring files.

# compiled output
!dist/
dist/*
!dist/libs/
dist/libs/*
!dist/libs/karoo-ui
!dist/libs/karoo-utils

/tmp
/out-tsc

# dependencies
node_modules

# Env Vars
*.env
!/.env

# IDEs and editors
/.idea
.project
.classpath
.c9/
*.launch
.settings/
*.sublime-workspace

# IDE - VSCode
.vscode/*
.vscode-server/
!.vscode/settings.json
!.vscode/tasks.json
!.vscode/launch.json
!.vscode/extensions.json
!.vscode/mcp.json

# misc
.npm/
.ssh/
.cache/
.gitconfig
/.sass-cache
/connect.lock
/coverage
/libpeerconnection.log
npm-debug.log
yarn-error.log
testem.log
/typings
.eslintcache

# System Files
.DS_Store
Thumbs.db

# microsoft-devcontainer
.devcontainer
docker

# [IMPORTANT for CI] - prevents nx affected commands from failing on bitbucket pipelines
/.pnpm-store

# cypress
screenshots
cypress/downloads

# cypress-multi-reporters
runner-results
multi-reporter-config.json

# Nx
.nx

# Locale files
apps/fleet-web/locales/*
!apps/fleet-web/locales/en.json
!apps/fleet-web/locales/en-ZA.json

**/vite.config.{js,ts,mjs,mts,cjs,cts}.timestamp*

# Cursor
**/.cursor/**
# allow project rules
!**/.cursor/rules.mdc

**/.claude/**
